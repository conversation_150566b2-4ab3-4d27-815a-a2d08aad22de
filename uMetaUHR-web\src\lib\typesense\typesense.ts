import { Client } from 'typesense'

// Define the Typesense client configuration
const typesenseClient = new Client({
  apiKey: 'xyz', // Replace with your API key
  nodes: [
    {
      host: 'localhost', // Replace with your Typesense server host
      port: 8108, // Replace with your Typesense server port
      protocol: 'http' // Replace with 'https' if using SSL
    }
  ],
  connectionTimeoutSeconds: 2
})

export { typesenseClient }

// Define the function to list collections
export async function listCollections(): Promise<any> {
  try {
    // Retrieve all collections
    const collections = await typesenseClient.collections().retrieve()
    console.log('Collections:', collections)
    return collections
  } catch (error) {
    console.error('Error listing collections:', error)
  }
}

export async function queryItem(collectionName: string, query: any): Promise<any> {
  try {
    // 添加按 score 排序的逻辑
    const searchQuery = { ...query, sort_by: '_text_match:desc' }

    // 执行搜索查询
    const searchResults = await typesenseClient
      .collections(collectionName)
      .documents()
      .search(searchQuery)

    console.log('Search Results:', searchResults)
    return searchResults
  } catch (error) {
    console.error('Error querying items:', error)
  }
}

// Define the function to query items in a collection
export async function creatDocument(): Promise<any> {
  try {
    const schema = {
      name: 'nameSearch',
      fields: [
        {
          name: 'name',
          type: 'string',
          facet: false
        },
        {
          name: 'type',
          type: 'string',
          facet: false
        },
        {
          name: 'id',
          type: 'string',
          facet: true
        }
      ],
      default_sorting_field: 'name'
    } as any
    await typesenseClient.collections().create(schema)
  } catch (error) {
    console.error('Error querying items:', error)
  }
}
