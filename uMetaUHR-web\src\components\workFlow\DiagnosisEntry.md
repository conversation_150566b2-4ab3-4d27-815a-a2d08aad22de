## 控件名称

DiagnosisEntry

## 控件描述

DiagnosisEntry 是一个专门用于诊断信息录入和管理的复杂组件，集成了数据库操作和表单交互功能。

### 组件定位
- 诊断管理：处理诊断信息的CRUD操作
- 工作流组件：嵌入在诊疗流程中使用
- 数据持久化：直接与数据库交互

### 核心功能
1. 诊断信息查询和展示
2. 新建/编辑诊断记录
3. 诊断数据持久化
4. 与就诊(encounter)数据关联
5. 对话框形式的编辑界面

### 使用场景
1. 门诊诊断录入
2. 住院诊断管理
3. 诊断信息编辑
4. 诊断历史查看

### 交互规范
1. 通过对话框进行诊断编辑
2. 自动关联当前就诊记录
3. 提供完整的CRUD操作
4. 支持诊断代码和描述录入

## schema

DiagnosisEntry 通过 schema 属性进行配置，主要字段包括：

- `ui`: 界面组件配置
  - `type`: 使用的组件类型
- `schema`: 编辑表单配置
  - `edit-dx`: 诊断编辑表单定义
- `model`: 数据绑定字段路径

### 示例

```json
{
  "ui": {
    "type": "diagnosisTable"
  },
  "schema": {
    "edit-dx": {
      "type": "diagnosisForm",
      "fields": [
        {
          "field": "code",
          "label": "诊断代码"
        },
        {
          "field": "description", 
          "label": "诊断描述"
        }
      ]
    }
  },
  "model": "detail.diagnosis"
}
```

注意事项：
1. 必须配置有效的ui组件
2. 需要正确的encounter上下文
3. 编辑表单需要完整定义

## context

DiagnosisEntry 使用复杂的依赖注入模式与父组件和子组件通信。

### 数据流图

```mermaid
graph TD
    Parent[父组件] -->|provide| EncounterData[就诊数据]
    DiagnosisEntry -->|inject| EncounterData
    DiagnosisEntry -->|provide| QueryCondition[查询条件]
    DiagnosisEntry -->|provide| CRUD[CRUD操作]
    DiagnosisEntry -->|更新| Context[上下文数据]
    Dialog -->|inject| CRUD
```

关键上下文交互：
1. 通过encounter上下文获取当前就诊ID
2. 提供诊断查询条件
3. 提供完整的诊断CRUD操作
4. 直接更新上下文中的诊断数据

## 事件

DiagnosisEntry 处理以下类型的事件：

### 用户事件
1. 新建诊断事件
   - 初始化空诊断表单
   - 重置上下文数据
2. 编辑诊断事件
   - 加载现有诊断数据
   - 打开编辑对话框
3. 保存诊断事件
   - 验证并保存数据
   - 更新上下文

### 系统事件
1. `query-condition`
   - 参数：查询过滤条件
   - 作用：配置诊断搜索条件
2. `save-diagnosis` 
   - 参数：诊断数据
   - 作用：持久化诊断记录

### provide 内容示例
组件提供的关键方法：
```javascript
provide('list-records', async () => {
  // 获取当前就诊的诊断列表
})

provide('save-diagnosis', async (diagnosis) => {
  // 保存诊断记录
})

provide('edit-dx', async (params) => {
  // 打开诊断编辑对话框
})
```

## 其他注意事项

1. 严格依赖encounter上下文
2. 使用PostgreSQL数据库存储
3. 包含完整的错误处理
4. 支持诊断代码和自由文本描述
5. 自动处理数据版本控制
6. 使用WnDialog组件实现编辑界面
