<template>
  <div class="labChart">
    <span v-if="schema?.label" class="label">{{ schema?.label }}</span>
    <table class="table">
      <thead>
        <tr>
          <th>名称</th>
          <th>代码</th>
          <th>日期</th>
          <th>值</th>
          <th>单位</th>
          <th>标记</th>
          <th>历史趋势</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(d, i) in lab_exam_list2" :key="`tr-${i}`">
          <td>{{ d.name.name }}</td>
          <td>{{ d.sym }}</td>
          <td>{{ d.date_time }}</td>
          <td>{{ d.value }}</td>
          <td>{{ d.unit }}</td>
          <td>{{ d.flag }}</td>
          <td>
            <div :key="`chart-${i}`" ref="compChart" class="chart" />
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script lang="ts" setup>
import { defineProps, nextTick, onMounted, ref, watch } from 'vue'
import { G2 } from '@antv/g2plot'
import { getContext, getNestedValue } from '@/lib/getContext'
import { deepMerge } from '@/lib/copymerge'
import { isEmpty } from '@/lib/util'
import moment from 'moment/moment'

const lab_exam_list2 = ref(
  [] as {
    name: { name: string }
    sym: string
    maxDate: string
    date_time: string
    flag: string
    unit: string
    value: string
  }[]
)
const compChart = ref()

const props = defineProps<{
  schema: { model: string }
  context: { [key: string]: any }
  label?: string
  event?: { name: string }
}>()

let chartInst = {} as any

function convertToTimestamp(dateStr: string) {
  // Extract year, month, and day from the date string
  dateStr = '' + dateStr
  const year = +dateStr.slice(0, 4)
  const month = +dateStr.slice(4, 6) - 1 // JavaScript months are 0-indexed (0 = January)
  const day = +dateStr.slice(6, 8)

  // Create a Date object and return the timestamp (milliseconds)
  const date = new Date(year, month, day)
  return date.getTime()
}

const drawChart = async (data_list: any) => {
  const context = getContext(props)

  let lab_exam: { [key: string]: any } = {}
  let symbol_name_map: { [key: string]: string } = {}

  const showItems = props.schema?.showItems

  data_list.forEach((item: any) => {
    let { name, date_time, data: { lab, result } = {} as any } = item
    if (lab) {
      let { data: labData, 申请时间 = '' } = lab || ({} as any)
      if (isEmpty(labData) || !Array.isArray(labData)) return
      let m = moment(申请时间)
      date_time = m.format('YYYYMMDD')

      result = labData.map((d: any) => {
        let { 指标代码, 指标名称, 检验结果, 结果单位, 参考值 } = d || {}
        return {
          flag: '',
          name: { name: 指标名称, symbol: 指标代码 },
          unit: 结果单位,
          value: 检验结果
        }
      })
    }
    if (isEmpty(result) || !Array.isArray(result)) return

    if (showItems) {
      const resultMap = new Map(result.map((x: any) => [x.name.symbol, x]))
      result = showItems
        .map((symbol: string) => resultMap.get(symbol)) // Get items in showItems order
        .filter((x: any) => !!x) // Remove undefined values
    }

    if (isEmpty(result) || !Array.isArray(result)) return

    result.forEach((x: any) => {
      if (isEmpty(x)) return
      const {
        name: { symbol = '', name: compName = '' },
        value
      } = x
      deepMerge(symbol_name_map, { [symbol]: compName })
      deepMerge(lab_exam, { [symbol]: { [convertToTimestamp(date_time)]: { ...x, date_time } } })
    })
  })

  // Prepare lab_exam_list and get all the dates
  let lab_exam_list: any[] = []
  const allDates = [] as any[] // List to store all transformed dates

  Object.keys(lab_exam).forEach((sym) => {
    const comp = lab_exam[sym]
    const maxDate = Math.max(...Object.keys(comp).map((date) => +date))
    lab_exam_list.push({
      sym,
      name: symbol_name_map[sym],
      ...comp[maxDate]
    })

    // Collect all dates to determine global min and max
    Object.keys(comp).forEach((date) => {
      allDates.push(+date) // Convert to days after 1900/01/01
    })
  })

  // Calculate global minDate and maxDate from all the dates
  const minDate = Math.min(...allDates)
  const maxDate = Math.max(...allDates)

  lab_exam_list2.value = lab_exam_list
  context.lab_exam = lab_exam
  context.d = data_list

  await nextTick()

  // Render or update the charts
  lab_exam_list.forEach((item, index) => {
    const comp = lab_exam[item.sym]
    const data = Object.keys(comp).map((date) => ({
      date: +date,
      value: Math.round(comp[date].value) // Ensure value is rounded to the nearest integer
    }))

    // Update chart container
    const chartContainer = compChart.value[index]
    if (!chartContainer) return

    let chart = chartInst[index]
    if (!chart) {
      chart = new G2.Chart({
        container: chartContainer,
        width: 200,
        height: 30,
        padding: [5, 5, 5, 25]
      })
      chartInst[index] = chart
    }

    chart.animate(false)
    chart.data(data)
    chart.scale({
      date: { type: 'time', max: maxDate, min: minDate }, // Use linear scale for days since 1900/01/01
      value: { type: 'linear' }
    })

    chart.line().position('date*value').color('blue')
    chart.point().position('date*value').shape('circle').size(4).color('red')

    // Configure y-axis to show integers
    chart.axis('date', {
      label: {
        formatter: (val: any) => '' //convertDaysToDate(val), // Round the value to integer,
        // style: { fontSize: '1px' }
      },
      nice: false // Disable automatic nice rounding of the y-axis
    })

    chart.render(true)
  })
}

watch(
  () => props?.schema?.model && getNestedValue(props.context, props?.schema?.model || ''),
  async (newValue) => {
    if (!newValue) return
    chartInst = {}
    await drawChart([])
    await nextTick()
    await drawChart(newValue)
  }
)

onMounted(() => {
  // Chart initialization or other mount logic can go here
})
defineExpose({ compName: props?.schema?.type || '-' })
</script>

<style lang="scss" scoped>
.labChart {
  display: flex;
  flex-direction: column;
  margin: 3px;
}

table {
  flex: 1 1 auto;
  min-width: 30rem;
  margin: 5px;
  border-collapse: collapse;
}

thead {
  background-color: #f4f4f4;
}

th,
td {
  padding: 0 10px;
  text-align: left;
  border: 1px solid #ddd;
  //width: 15rem;
}

tr:hover {
  background-color: #f1f1f1;
}

.chartContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid lightblue;
}
</style>
