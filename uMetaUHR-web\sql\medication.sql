-- 药品数据库设计
-- 符合.clinerules/database-design.md规范
-- 生成时间: 2025-06-05

DROP TABLE if exists medications cascade;
drop table if exists medication_clinical_infos cascade;
drop table if exists medication_rules cascade;
drop table if exists medication_supplies cascade;

-- 1. 核心信息表
CREATE TABLE medications
(
    id                 INT GENERATED BY DEFAULT AS IDENTITY
        CONSTRAINT pk_medications PRIMARY KEY,
    name               VARCHAR(255)       NOT NULL,
    code               VARCHAR(50) UNIQUE NOT NULL,
    type               VARCHAR(50)        NOT NULL,
    dosage_form        VARCHAR(50)        NOT NULL,
    strength           VARCHAR(100)       NOT NULL,
    status             VARCHAR(20)        NOT NULL DEFAULT 'active',
    manufacturer       VARCHAR(255),
    description        TEXT,
    -- Regulatory information
    approval_number    VARCHAR(50),
    therapeutic_class  VARCHAR(100),
    -- Storage requirements
    storage_conditions VARCHAR(100),
    -- Additional identifiers
    barcode            VARCHAR(50),
    ndc_code           VARCHAR(20),
    created_at         INTEGER            NOT NULL DEFAULT EXTRACT(EPOCH FROM CURRENT_TIMESTAMP),
    updated_at         INTEGER            NOT NULL DEFAULT EXTRACT(EPOCH FROM CURRENT_TIMESTAMP),
    created_by         VARCHAR(20)        NOT NULL DEFAULT 'system',
    modified_by        VARCHAR(20),
    version            INT                         DEFAULT 0,
    CONSTRAINT uk_medications_code UNIQUE (code)
);

COMMENT ON TABLE medications IS '药品基础信息表。支持药品的批准文号。';
COMMENT ON COLUMN medications.name IS '药品通用名称';
COMMENT ON COLUMN medications.code IS '药品编码';
COMMENT ON COLUMN medications.type IS '药品类型';
COMMENT ON COLUMN medications.dosage_form IS '剂型';
COMMENT ON COLUMN medications.strength IS '规格/浓度';
COMMENT ON COLUMN medications.status IS '状态: active/inactive';
COMMENT ON COLUMN medications.manufacturer IS '生产厂家';
COMMENT ON COLUMN medications.description IS '药品描述';
COMMENT ON COLUMN medications.approval_number IS '批准文号';
COMMENT ON COLUMN medications.therapeutic_class IS '治疗类别';
COMMENT ON COLUMN medications.storage_conditions IS '存储条件';
COMMENT ON COLUMN medications.barcode IS '条形码';
COMMENT ON COLUMN medications.ndc_code IS 'NDC编码';
COMMENT ON COLUMN medications.created_at IS '创建时间';
COMMENT ON COLUMN medications.updated_at IS '更新时间';
COMMENT ON COLUMN medications.created_by IS '创建人';
COMMENT ON COLUMN medications.modified_by IS '修改人';
COMMENT ON COLUMN medications.version IS '版本号(乐观锁)';

-- 2. 临床信息表
CREATE TABLE medication_clinical_infos
(
    id                  INT GENERATED BY DEFAULT AS IDENTITY
        CONSTRAINT pk_medication_clinical_infos PRIMARY KEY,
    medication_id       INTEGER     NOT NULL,
    organization_id     INTEGER     NOT NULL,
    is_inheritable      BOOLEAN     NOT NULL DEFAULT TRUE,
    dosing_instructions TEXT,
    contraindications   TEXT,
    precautions         TEXT,
    effective_from      INTEGER     NOT NULL,
    effective_to        INTEGER,
    created_at          INTEGER     NOT NULL DEFAULT EXTRACT(EPOCH FROM CURRENT_TIMESTAMP),
    updated_at          INTEGER     NOT NULL DEFAULT EXTRACT(EPOCH FROM CURRENT_TIMESTAMP),
    created_by          VARCHAR(20) NOT NULL DEFAULT 'system',
    modified_by         VARCHAR(20),
    version             INT                  DEFAULT 0,
    CONSTRAINT fk_clinical_medication FOREIGN KEY (medication_id) REFERENCES medications (id),
    CONSTRAINT uk_clinical_med_org UNIQUE (medication_id, organization_id)
);

COMMENT ON TABLE medication_clinical_infos IS '药品临床信息表';
COMMENT ON COLUMN medication_clinical_infos.medication_id IS '关联药品ID';
COMMENT ON COLUMN medication_clinical_infos.organization_id IS '关联机构ID';
COMMENT ON COLUMN medication_clinical_infos.is_inheritable IS '是否可继承';
COMMENT ON COLUMN medication_clinical_infos.dosing_instructions IS '给药说明';
COMMENT ON COLUMN medication_clinical_infos.contraindications IS '禁忌症';
COMMENT ON COLUMN medication_clinical_infos.precautions IS '注意事项';
COMMENT ON COLUMN medication_clinical_infos.effective_from IS '生效开始时间';
COMMENT ON COLUMN medication_clinical_infos.effective_to IS '生效结束时间';
COMMENT ON COLUMN medication_clinical_infos.created_at IS '创建时间';
COMMENT ON COLUMN medication_clinical_infos.updated_at IS '更新时间';
COMMENT ON COLUMN medication_clinical_infos.created_by IS '创建人';
COMMENT ON COLUMN medication_clinical_infos.modified_by IS '修改人';
COMMENT ON COLUMN medication_clinical_infos.version IS '版本号(乐观锁)';

-- 3. 供应信息表
CREATE TABLE medication_supplies
(
    id               INT GENERATED BY DEFAULT AS IDENTITY
        CONSTRAINT pk_medication_supplies PRIMARY KEY,
    medication_id    INTEGER     NOT NULL,
    stock_quantity   INTEGER     NOT NULL DEFAULT 0,
    reorder_point    INTEGER     NOT NULL DEFAULT 10,
    supplier_id      INTEGER,
    supplier_name    VARCHAR(255),
    supplier_contact VARCHAR(255),
    reorder_quantity INTEGER,
    last_restock     INTEGER,
    created_at       INTEGER     NOT NULL DEFAULT EXTRACT(EPOCH FROM CURRENT_TIMESTAMP),
    updated_at       INTEGER     NOT NULL DEFAULT EXTRACT(EPOCH FROM CURRENT_TIMESTAMP),
    created_by       VARCHAR(20) NOT NULL DEFAULT 'system',
    modified_by      VARCHAR(20),
    version          INT                  DEFAULT 0,
    CONSTRAINT fk_supply_medication FOREIGN KEY (medication_id) REFERENCES medications (id)
);

COMMENT ON TABLE medication_supplies IS '药品供应信息表';
COMMENT ON COLUMN medication_supplies.medication_id IS '关联药品ID';
COMMENT ON COLUMN medication_supplies.stock_quantity IS '库存数量';
COMMENT ON COLUMN medication_supplies.reorder_point IS '补货点';
COMMENT ON COLUMN medication_supplies.supplier_id IS '供应商ID';
COMMENT ON COLUMN medication_supplies.supplier_name IS '供应商名称';
COMMENT ON COLUMN medication_supplies.supplier_contact IS '供应商联系方式';
COMMENT ON COLUMN medication_supplies.reorder_quantity IS '补货数量';
COMMENT ON COLUMN medication_supplies.last_restock IS '最后补货时间';
COMMENT ON COLUMN medication_supplies.created_at IS '创建时间';
COMMENT ON COLUMN medication_supplies.updated_at IS '更新时间';
COMMENT ON COLUMN medication_supplies.created_by IS '创建人';
COMMENT ON COLUMN medication_supplies.modified_by IS '修改人';
COMMENT ON COLUMN medication_supplies.version IS '版本号(乐观锁)';

-- 4. 药品规则表
CREATE TABLE medication_rules
(
    id             INT GENERATED BY DEFAULT AS IDENTITY
        CONSTRAINT pk_medication_rules PRIMARY KEY,
    medication_id  INTEGER      NOT NULL,
    rule_type      VARCHAR(20)  NOT NULL,
    rule_name      VARCHAR(100) NOT NULL,
    rule_condition TEXT         NOT NULL,
    rule_action    TEXT         NOT NULL,
    priority       INTEGER      NOT NULL DEFAULT 0,
    is_active      BOOLEAN      NOT NULL DEFAULT TRUE,
    created_at     INTEGER      NOT NULL DEFAULT EXTRACT(EPOCH FROM CURRENT_TIMESTAMP),
    updated_at     INTEGER      NOT NULL DEFAULT EXTRACT(EPOCH FROM CURRENT_TIMESTAMP),
    created_by     VARCHAR(20)  NOT NULL DEFAULT 'system',
    modified_by    VARCHAR(20),
    version        INT                   DEFAULT 0,
    CONSTRAINT fk_rule_medication FOREIGN KEY (medication_id) REFERENCES medications (id)
);

COMMENT ON TABLE medication_rules IS '药品规则表';
COMMENT ON COLUMN medication_rules.medication_id IS '关联药品ID';
COMMENT ON COLUMN medication_rules.rule_type IS '规则类型: dosing/warning';
COMMENT ON COLUMN medication_rules.rule_name IS '规则名称';
COMMENT ON COLUMN medication_rules.rule_condition IS '规则条件表达式';
COMMENT ON COLUMN medication_rules.rule_action IS '规则触发动作';
COMMENT ON COLUMN medication_rules.priority IS '规则优先级';
COMMENT ON COLUMN medication_rules.is_active IS '是否激活';
COMMENT ON COLUMN medication_rules.created_at IS '创建时间';
COMMENT ON COLUMN medication_rules.updated_at IS '更新时间';
COMMENT ON COLUMN medication_rules.created_by IS '创建人';
COMMENT ON COLUMN medication_rules.modified_by IS '修改人';
COMMENT ON COLUMN medication_rules.version IS '版本号(乐观锁)';

-- 创建索引
CREATE INDEX idx_medications_type ON medications (type);
CREATE INDEX idx_medications_status ON medications (status);
CREATE INDEX idx_medication_clinical_infos_medication_id ON medication_clinical_infos (medication_id);
CREATE INDEX idx_medication_clinical_infos_organization_id ON medication_clinical_infos (organization_id);
CREATE INDEX idx_medication_supplies_medication_id ON medication_supplies (medication_id);
CREATE INDEX idx_medication_supplies_stock_quantity ON medication_supplies (stock_quantity);
CREATE INDEX idx_medication_rules_medication_id ON medication_rules (medication_id);
CREATE INDEX idx_medication_rules_rule_type ON medication_rules (rule_type);
