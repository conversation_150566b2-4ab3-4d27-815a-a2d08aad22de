<template>
  <a-config-provider :locale="zhCN">
    <div @contextmenu.prevent="rightClick">
      <component :is="loadComponent(schema?.type)" :context="context" :schema="schema" />
      <WnDialog ref="dialogRef" />
      <WnAlert />
      <div class="spin-container" ref="spinRef">
        <a-spin size="large" spinning />
      </div>
    </div>
  </a-config-provider>
</template>

<script lang="ts" setup>
import { onMounted, provide, reactive, ref } from 'vue'
import WnDialog from '@/components/containers/WnDialog.vue'
import WnAlert from '@/components/containers/WnAlert.vue'
import { loadComponent } from '@/components/componentRegistry'
import { loadSchema } from '@/schema/schemaRegistry'
import EasySandbox from '@/lib/EasySandbox'
import { CommandManager } from '@/components/action/commandFlow/CommandFlowManager'
import ServerTime from '@/lib/ServerTime'
import { CacheManager } from '@/lib/CacheManager'
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
dayjs.locale('zh-cn');

const schema = ref({})

// Set a custom default timezone
ServerTime.setDefaultTimeZone('Asia/Shanghai')

const dialogRef = ref(null)
const context = reactive<{ [key: string]: any }>({
  patient: {},
  user: {},
  settings: {}
})

const commandManager = new CommandManager()
provide('commandManager', commandManager)
provide('ROOT_CONTEXT', () => context)
provide('switch-workspace', async (event: any) => {
  try {
    schema.value = await loadSchema('00200-Workspace')
    console.log('Workspace switched successfully')
  } catch (error) {
    // Handle errors that may occur during the import or update process
    console.error('Failed to switch workspace:', error)
    alert('Failed to switch workspace: ' + error?.message)
  }
})

const currentTheme = ref('epic')
const cacheManager = CacheManager.getInstance<any>()

// Set theme and save to localStorage
const setTheme = async (theme: string) => {
  document.documentElement.setAttribute('data-theme', theme)
  await cacheManager.set('system-theme', { theme })
  currentTheme.value = theme
}

onMounted(async () => {
  document.querySelector('.lds-spinner')?.remove()

  const { theme } = (await cacheManager.get('system-theme')) || {}
  await setTheme(theme || 'epic') // Load saved theme or default to light theme

  // schema.value = await loadSchema('00100-Login')
  schema.value = await loadSchema('00200-Workspace')

  // const bdm = new BusinessDataManager()
  // Uncomment and use the following lines as needed
  // const {
  //   results: [{ sessionId, mainSchemaDesc, privilege } = {} as any] = []
  // } = await bdm.mutateConcept({ concept: 'SessionStart' });
  // Object.assign(extModelSet.value, { sessionId, privilege, mainSchemaDesc });
  // await switchDesktop({ mainSchemaDesc });

  //test sandbox
  {
    const sandbox = new EasySandbox('MySandbox', {
      debug: true,
      sharedState: { user: 'John' },
      utilities: {
        log: console.log
      }
    })

    const userCode = `
  console.log(window.user);
  window.aaa = 1
  ccc = 2
  window.log("This is a log from the sandbox! ")
  `
    sandbox.run(userCode)
    console.log('AAA' + window.aaa)
  }
})

async function switchDesktop({ mainSchemaDesc }: any) {
  // if (!mainSchemaDesc) return
  // const bdm = new BusinessDataManager()
  //
  // let schemaData
  // const response = await bdm.gqlGetComp({ descriptor: mainSchemaDesc })
  // schemaData = response.schema
  // if (!schemaData) return
  // schema.value = deepCopy({ groups: [{ fields: [schemaData] }] })
}

function rightClick(event: MouseEvent) {
  // console.log("right click", event);
}

const spinRef = ref<HTMLDivElement | null>(null)

window.popSpin = (show: boolean) => {
  if (spinRef.value) {
    spinRef.value.style.display = show ? 'flex' : 'none'
  }
}

</script>

<style lang="scss" scoped>
[data-theme='epic'] {
  div {
    color: var(--test-color);
    border: 1px solid var(--test-color);
  }
}
.spin-container {
  width: 100vw;
  height: 100vh;
  display: none;
  position: fixed;
  z-index: 99999999;
  justify-content: center;
  align-items: center;
}
</style>
