<template>
  <div v-if="loading">加载中...</div>
  <RouterView v-else />
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { APP_ID } from '@/enums'
import { useAuthInfo } from './hooks/useAuthInfo'
import { useAuth } from './hooks/useAuth'

onMounted(async () => {
  const hostname = window.location.hostname
  const isLocal = hostname === 'localhost' || hostname === '127.0.0.1'
  if (isLocal) return

  const loading = ref(true)
  // Only run authentication setup when not in local mode
  const { fetchAuthInfo } = useAuthInfo()
  const { initializeAuthConfig, initializeAuthClient } = useAuth()

  await fetchAuthInfo(APP_ID)
  initializeAuthConfig()
  initializeAuthClient()
  loading.value = false
})
</script>

<style lang="scss" scoped></style>
