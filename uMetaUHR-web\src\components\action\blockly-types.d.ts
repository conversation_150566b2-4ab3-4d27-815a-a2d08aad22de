// declare module 'blockly' {
//   interface Block {
//     getFieldValue(fieldName: string): string;
//   }
// }
//
// declare module 'blockly/javascript' {
//   interface JavascriptGenerator {
//     select_orders: (block: Blockly.Block) => string;
//     order: (block: Blockly.Block) => [string, number];
//     all_orders: () => [string, number];
//     ORDER_ATOMIC: number;
//   }
// }
//
// declare module './blockly/t-001.json5' {
//   const content: any;
//   export default content;
// }
