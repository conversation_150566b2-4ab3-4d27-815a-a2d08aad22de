<template>
  <div class="blockly">
    <label v-if="props?.schema?.label">
      <span class="label">
        {{ props?.schema?.label }}
        <span v-if="props?.schema?.valid?.required" class="required">*</span>
      </span>
    </label>
    <div ref="blocklyWorkspace" class="blockly-workspace"></div>
    <div class="blockly-controls">
      <button @click="runCode" class="run-button">Run Code</button>
      <button @click="saveWorkspace" class="run-button">Save</button>
      <button @click="loadWorkspace" class="run-button">Load</button>
    </div>
    <div class="code-display">
      <h4>Generated Code:</h4>
      <pre>{{ generatedCode }}</pre>
      <h4>Output:</h4>
      <pre>{{ output }}</pre>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { defineProps, onMounted, onUnmounted, ref } from 'vue'
import * as Blockly from 'blockly'
import { javascriptGenerator } from 'blockly/javascript'
import { Debouncer } from '@/lib/Debouncer'

Blockly.WorkspaceAudio.prototype.load = function () {}
Blockly.WorkspaceAudio.prototype.play = function () {}

const props = defineProps<{
  schema: {
    type?: string
    label?: string
    model: string
    metadata?: string
    options?: Array<{ value: string | number; label: string }>
    inputType?: string
    dataType?: string
    showWhen?: string
    valid?: {
      required?: boolean
    }
  }
  context: { [key: string]: any }
}>()

const blocklyWorkspace = ref<HTMLElement | null>(null)
const workspace = ref<Blockly.WorkspaceSvg | null>(null)
const generatedCode = ref('')
const output = ref('')

const changeEvent = new Debouncer(1000).debounce((change: any) => {
  if (!workspace.value) return ''
  generatedCode.value = javascriptGenerator.workspaceToCode(workspace.value as any)

  output.value = ''
})

onMounted(async () => {
  const { default: blockDef } = ((await import(`./blockly/block-def-001.json5`)) as any) || []
  Blockly.defineBlocksWithJsonArray(blockDef)

  const toolbox = (await import(`./blockly/t-001.json5`)) as any

  workspace.value = Blockly.inject(blocklyWorkspace.value as any, { toolbox })
  workspace.value.addChangeListener(changeEvent)

  javascriptGenerator.forBlock['order_list2'] = function (block: any) {
    const count = block.getFieldValue('count')
    let d = block.getChildren().map((child: any) => javascriptGenerator.blockToCode(child))[0] || ''
    let d2 = d
      .split(';')
      .filter((x: any) => x)
      .join(',')

    const code = `
/*必须从下列医嘱中选取 ${count} 个医嘱:*/
 selectOrders(${count}, [${d2}]);
    `
    return code
  }

  javascriptGenerator.forBlock['my_order'] = function (block: any) {
    return `getOrder(${block.getFieldValue('med')});`
  }

  javascriptGenerator.forBlock['patient'] = function (block: any) {
    return [`patient.${block.getFieldValue('patientAttr')}`, 1]
  }

  javascriptGenerator.forBlock['text'] = function (block: any) {
    return [`"${block.getFieldValue('text')}"`, 1]
  }
})

onUnmounted(() => {
  if (workspace.value) {
    workspace.value.dispose()
  }
})

function saveWorkspace() {
  const xml = Blockly.Xml.workspaceToDom(workspace.value as any)
  const xmlText = Blockly.utils.xml.domToText(xml)
  localStorage.setItem('workspaceBackup', xmlText)
}

async function loadWorkspace() {
  // make sure data is there.
  const xmlText = localStorage.getItem('workspaceBackup')
  if (!xmlText) return

  // replace the old workspace with a new one.
  let ws = workspace.value as any
  if (!ws) return
  try {
    ws.toolbox.dispose(false, false)
  } catch (e) {
    delete ws.toolbox // work around to fix one blockly issue, might cause memory leak. but it for non-critical work.
  }
  ws.dispose(false, false)

  const toolbox = (await import(`./blockly/t-001.json5`)) as any
  ws = Blockly.inject(blocklyWorkspace.value as any, { toolbox })
  if (!ws) return
  workspace.value = ws
  ws.addChangeListener(changeEvent)

  //parse and load
  const xmlDom = Blockly.utils.xml.textToDom(xmlText)
  Blockly.Xml.domToWorkspace(xmlDom, ws)
  ws.markFocused()
}

function runCode() {
  try {
    if (!workspace.value) return
    generatedCode.value = javascriptGenerator.workspaceToCode(workspace.value)
    output.value = ''

    // Safely execute the generated code
    const result = new Function(generatedCode.value)()

    output.value =
      result !== undefined
        ? JSON.stringify(result, null, 2)
        : 'Code executed successfully (no return value)'
  } catch (error) {
    if (error instanceof Error) {
      output.value = `Error: ${error.message}`
    } else {
      output.value = `Error: ${String(error)}`
    }
  }
}

defineExpose({
  compName: 'blocklyEditor',
  getWorkspace: () => workspace.value,
  runCode
})
</script>

<style lang="scss" scoped>
.blockly {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  margin: 2px;
  overflow: hidden;

  > * {
    display: flex;
    flex-direction: column;
    flex: 1;
  }
}

.blockly-workspace {
  width: 100%;
  height: 400px;
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 4px;
  flex: 0 0 auto;
}

.blockly-controls {
  margin: 4px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  flex: 0 0 auto;

  .run-button {
    padding: 8px 16px;
    background-color: #1890ff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    min-width: 8rem;
    //flex: 0;
  }

  .run-button:hover {
    background-color: #40a9ff;
  }
}

.code-display {
  display: block;
  padding: 10px;
  border-radius: 4px;
  flex: 1;
  overflow: auto;

  .code-display pre {
    flex: 0;
    white-space: pre-wrap;
    padding: 10px;
    border-radius: 4px;
    background-color: #f9f9f9;
    margin-top: 5px;
  }
}
</style>
