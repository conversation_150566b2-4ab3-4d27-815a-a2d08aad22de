{
  // Helper functions for structured outputs
  function createCommand(name, args) {
    return { type: "command", name, args };
  }

  function createSection(command, content) {
    return { command, content: content.trim() };
  }
}

start
  = ignoreComments? section1:section sections:(separator section)* ignoreComments? {
      const allSections = [section1]
        .concat(sections.filter(s => s[0] !== 222).map(s => s[1]));
      return allSections;
    }

section
  =  command:command content:optionalContent? {
      return createSection(command, content || "");
    }

command
  = name:identifier args:arguments? ws* newline {
      return createCommand(name, args || []);
    }

arguments
  = ws+ arg:argument rest:(ws+ argument)* {
      return [arg].concat(rest.map(r => r[1]));
    }

argument
  = quotedArg / unquotedArg

quotedArg
  = "\"" chars:(escapedChar / [^"\\\n])* "\"" {
      return chars.join('');
    }

escapedChar
  = "\\" char:. { return char; }

unquotedArg
  = chars:([^\n\t ]+) { return chars.join(''); }

optionalContent
  = lines:(!(separator/ignoreComments) line)* {
      return lines.map(l => l?.[1]?.trim()||'').join('\n');
    }

separator
  = "---" ws* newline { return 111; }
  / "//---" ws* newline { return 222; }

line
  = text:[^\n\r]* newline {
      return text.join('');
    }

identifier
  = chars:([^ \t\n]+) {
      return chars.join('');
    }

ignoreComments
  // 忽略以 /// 开头的行及其后所有文字
  = "///---" .* {
      return null;
    }

ws "whitespace"
  = [ \t]+

newline "newline"
  = [\n\r]+