/**
 * 编辑器实例管理器
 * 负责管理不同view位置的编辑器操作类实例
 * 业务层通过viewId获取对应的编辑器操作能力
 */

import { BaseEditorOperator } from './BaseEditorOperator';

export interface EditorInstanceInfo {
  viewId: string;
  operator: BaseEditorOperator;
  patientId?: string;
  documentId?: string;
  documentType?: string;
}

export class EditorInstanceManager {
  private static instance: EditorInstanceManager;
  private editors: Map<string, EditorInstanceInfo> = new Map();
  private waitingQueue: Map<string, Array<(operator: BaseEditorOperator) => void>> = new Map();

  private constructor() {}

  static getInstance(): EditorInstanceManager {
    if (!EditorInstanceManager.instance) {
      EditorInstanceManager.instance = new EditorInstanceManager();
    }
    return EditorInstanceManager.instance;
  }

  /**
   * 注册编辑器操作类到指定view位置
   * @param viewId view位置标识，如 '门诊工作区'、'历史查看区'
   * @param operator 编辑器操作类实例
   */
  setEditor(viewId: string, operator: BaseEditorOperator): void {
    const editorInfo: EditorInstanceInfo = {
      viewId,
      operator
    };

    this.editors.set(viewId, editorInfo);

    // 通知等待中的调用者
    const waitingCallbacks = this.waitingQueue.get(viewId);
    if (waitingCallbacks) {
      waitingCallbacks.forEach(callback => callback(operator));
      this.waitingQueue.delete(viewId);
    }
  }

  /**
   * 通过viewId获取编辑器操作类（异步，会等待编辑器注册完成）
   * @param viewId view位置标识
   * @param timeout 超时时间（毫秒），默认10秒
   * @returns 编辑器操作类实例的Promise
   */
  async getEditor(viewId: string, timeout: number = 10000): Promise<BaseEditorOperator> {
    // 如果已经存在，直接返回
    const existingEditor = this.editors.get(viewId);
    if (existingEditor) {
      return existingEditor.operator;
    }

    // 如果不存在，等待注册
    return new Promise<BaseEditorOperator>((resolve, reject) => {
      // 设置超时
      const timeoutId = setTimeout(() => {
        // 清理等待队列
        const callbacks = this.waitingQueue.get(viewId);
        if (callbacks) {
          const index = callbacks.indexOf(resolve);
          if (index > -1) {
            callbacks.splice(index, 1);
          }
          if (callbacks.length === 0) {
            this.waitingQueue.delete(viewId);
          }
        }
        reject(new Error(`Editor for viewId "${viewId}" not registered within ${timeout}ms`));
      }, timeout);

      // 添加到等待队列
      if (!this.waitingQueue.has(viewId)) {
        this.waitingQueue.set(viewId, []);
      }

      const wrappedResolve = (operator: BaseEditorOperator) => {
        clearTimeout(timeoutId);
        resolve(operator);
      };

      this.waitingQueue.get(viewId)!.push(wrappedResolve);
    });
  }

  /**
   * 设置编辑器上下文
   * @param viewId view位置标识
   * @param patientId 患者ID
   * @param documentId 文书ID
   * @param documentType 文书类型
   */
  setEditorContext(viewId: string, patientId: string, documentId: string, documentType: string): void {
    const editorInfo = this.editors.get(viewId);
    if (editorInfo) {
      editorInfo.patientId = patientId;
      editorInfo.documentId = documentId;
      editorInfo.documentType = documentType;
    } else {
      console.warn(`Editor not found for viewId: ${viewId}`);
    }
  }

  /**
   * 获取编辑器上下文
   * @param viewId view位置标识
   * @returns 编辑器实例信息
   */
  getEditorContext(viewId: string): EditorInstanceInfo | null {
    return this.editors.get(viewId) || null;
  }

  /**
   * 移除指定view的编辑器
   * @param viewId view位置标识
   */
  removeEditor(viewId: string): void {
    this.editors.delete(viewId);
  }

  /**
   * 检查指定view是否已注册编辑器
   * @param viewId view位置标识
   */
  hasEditor(viewId: string): boolean {
    return this.editors.has(viewId);
  }

  /**
   * 移除指定患者的所有编辑器
   * @param patientId 患者ID
   */
  removeEditorsByPatient(patientId: string): void {
    const editorsToRemove: string[] = [];

    for (const [viewId, editorInfo] of this.editors.entries()) {
      if (editorInfo.patientId === patientId) {
        editorsToRemove.push(viewId);
      }
    }

    editorsToRemove.forEach(viewId => {
      this.removeEditor(viewId);
    });

    console.log(`Removed ${editorsToRemove.length} editors for patient: ${patientId}`);
  }

  /**
   * 获取所有已注册的view列表
   */
  getAllViewIds(): string[] {
    return Array.from(this.editors.keys());
  }

  /**
   * 清理所有编辑器
   */
  clearAllEditors(): void {
    this.editors.clear();
    console.log('All editor operators cleared');
  }
}

// 业务层通过此实例进行编辑器管理
export const editorManager = EditorInstanceManager.getInstance();
