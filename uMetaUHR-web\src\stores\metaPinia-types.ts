export interface MetaPiniaData {
  [key: string]: any
  patients?: Patient[]
  currentPatient?: Patient | null
}

export type Patient = {
  id: string
  name: string
  age?: number
  gender?: '男' | '女' | '其他'
  orders?: Order[]
  labs?: LabTest[]
  [key: string]: any
}

export type Order = {
  id: string
  drug: string
  dose: string
  frequency?: string
  status?: 'pending' | 'completed' | 'cancelled'
  [key: string]: any
}

export type LabTest = {
  id: string
  name: string
  result: string | number
  referenceRange?: string
  [key: string]: any
}

export type AuditEntry = {
  timestamp: Date
  action: string
  user?: string
}

export type CurrentUser = {
  id: string
  name: string
  role?: string
}

export type NewPatient = Omit<Patient, 'id'>

export type NewOrder = {
  drug: string
  dose: string
  frequency?: string
  status?: 'pending' | 'completed' | 'cancelled'
  [key: string]: any
}

export type Diagnosis = {
  id: string
  patientId: string
  code: string
  description: string
  date: Date
  [key: string]: any
}

export type ClinicalNote = {
  id: string
  patientId: string
  content: string
  createdAt: Date
  updatedAt: Date
  [key: string]: any
}

export interface OrderOperations {
  createOrder(order: Order): void
  updateDose(orderId: string, newDose: string): void
  deleteOrder(orderId: string): void
}

export interface LabOperations {
  addLabResult(test: LabTest): void
  flagAbnormalResults(): LabTest[]
}

export type MetaPiniaInstance<T> = T & {
  readonly data: T
  use(path: string, options?: { reactive?: boolean }): any
  getCurrentPatient(): Patient | null | undefined
  switchPatient(patientId: string): void
  logAction(action: string): void
  getAuditLog(): AuditEntry[]
  orders: OrderOperations
  labs: LabOperations
}
