type FormatStyle = {
  key?: [string, string]
  label?: [string, string]
  list?: [string, string, string]
  object?: [string, string, string]
  value?: [string, string]
}

type JsonToReadableTextOptions = {
  levelPrefixes?: string
  format?: FormatStyle[]
}

function jsonToReadableText(json: any, options: JsonToReadableTextOptions = {}): string {
  const defaultFormat: FormatStyle = {
    key: ['', ''],
    label: ['', ''],
    list: ['', '', '\n'],
    object: ['', '', '\n'],
    value: ['', '']
  }

  const { levelPrefixes = '  ', format = [defaultFormat] } = options

  const repeatPrefix = (level: number): string => levelPrefixes.repeat(level)

  const formatJson = (data: any, depth: number = 0): string => {
    const style = format[depth] || defaultFormat
    const pfx = repeatPrefix(depth)

    if (Array.isArray(data)) {
      const [prefix = '', suffix = '', separator = ''] = style.list || []
      if (!data.length) return `${prefix}none${suffix}`
      const formattedItems = data
        .map((item) => `${pfx}${formatJson(item, depth + 1)}`)
        .join(separator)
      return `${prefix}${formattedItems}${suffix}`
    }

    if (data && typeof data === 'object') {
      const [prefix = '', suffix = '', separator = ''] = style.object || []
      const [keyPrefix = '', keySuffix = ''] = style.key || []
      const entries = Object.entries(data)
      if (!entries.length) return `${prefix}none${suffix}`
      const formattedEntries = entries
        .map(([key, value]) => {
          const formattedKey = `${keyPrefix}${key}${keySuffix}`
          return `${pfx}${formattedKey}: ${formatJson(value, depth + 1)}`
        })
        .join(separator)
      return `${prefix}${formattedEntries}${suffix}`
    }

    const [prefix = '', suffix = ''] = style.value || []
    return typeof data === 'string' ? `${prefix}${data}${suffix}` : String(data ?? 'none')
  }

  return formatJson(json)
}

export default jsonToReadableText
