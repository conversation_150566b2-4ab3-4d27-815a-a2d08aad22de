<template>
  <div class="clinical-notes">
    <component
      :is="loadComponent(props.schema?.ui?.type)"
      v-if="props.schema"
      :context="getContext(props)"
      :schema="props.schema?.ui"
    />
  </div>
  <WnDialog ref="dialog" />
</template>

<script lang="ts" setup>
import { defineProps, provide, ref } from 'vue'
import { loadComponent } from '../componentRegistry'
import { getContext, getNestedValue } from '@/lib/getContext'
import WnDialog from '@/components/containers/WnDialog.vue'
import { dbGet, dbList, dbSave } from '@/lib/dataMan'
import { deepMerge } from '@/lib/copymerge'

const props = defineProps<{
  schema: any
  context: { [key: string]: any }
}>()

const dialog = ref<InstanceType<typeof WnDialog>>()

provide('query-condition', async (param: any = {}) => {
  let cat = 'notes'
  let docType = 'col1'
  const filter = cat ? { filter_by: `category:=${cat}` } : {}
  return {
    search: {
      docType,
      param: { query_by: 'name', typo_tokens_threshold: 0, max_candidates: 30, ...filter }
    }
  }
})

provide('list-records', async () => {
  let context = getContext(props)
  let encounterId = context.detail?.encounter?.id || ''
  if (!encounterId) return []

  let sql = `SELECT n.id,
                    n.note_type as type,
                    n.note_content as content,
                    n.note_date as date
             FROM "maxEMR".clinical_notes n
             WHERE n.encounter_id = ?::int
             ORDER BY n.note_date DESC`
  return (
    (await dbList({
      sql,
      param: [encounterId]
    })) || []
  )
})

provide('select-note', async (param = {}) => {
  let context = getContext(props)
  let { data: { id = '' } = {} } = param as any
  if (!id) return

  const note = await dbGet({
    sql: `SELECT *
          FROM "maxEMR".clinical_notes n
          WHERE n.id = ?::int`,
    param: [id]
  })

  context.detail.detail.note = note
  return note
})

provide('new-note', async (param = {}) => {
  let context = getContext(props)
  let { detail: { encounter: { id: encounterId = 0 } = {} } = {} } = context as any
  if (!encounterId) return

  let { getParentExposed } = param || ({} as any)
  let { newItem } = getParentExposed('toc')
  newItem({})

  context.detail.detail.note = {}
  return {}
})

provide('save-note', async (param = {}) => {
  let context = getContext(props)
  let note = context.detail?.detail?.note || {}
  let encounter = context.detail?.detail?.encounter || {}
  if (!encounter.id) {
    await window.popAlert({ message: 'encounter id is not set.' })
    return
  }
  note.encounter_id = encounter.id

  const conditionalColumns = note.id ? ['id'] : []
  const result = await dbSave({
    table: `clinical_notes`,
    data: [note],
    conflict_column: 'id',
    update_columns: [
      ...conditionalColumns,
      'encounter_id',
      'note_type',
      'note_content',
      'note_date',
      'status_c',
      'extensions'
    ]
  })

  if (result?.ids?.[0]?.id) {
    deepMerge(context, { detail: { note: { id: result.ids[0].id } } })
  }
  return result
})

provide('edit-note', async (para: object) => {
  let { getParentExposed, context } = para as any
  if (!getParentExposed || !context) return
  let schema = props.schema.schema['edit-note']
  getParentExposed('recordEntry')?.setRowSchema?.(schema, context.index)
})

provide('edit-note-closed', async (para: object) => {
  let { getParentExposed, context } = para as any
  if (!getParentExposed || !context) return
  let schema = props.schema.schema['edit-note']
  getParentExposed('recordEntry')?.setRowSchema?.(null, context.index)
})

provide('save-records', async (param: Record<string, unknown> = {}) => {
  let context = getContext(props)
  const noteList = getNestedValue(context, 'noteList')
  if (!noteList.length) return
})

defineExpose({ compName: 'clinicalNotes' })
</script>

<style lang="scss" scoped>
.clinical-notes {
  display: flex;
  flex: 1 1 auto;
  min-height: 5rem;
}
</style>
