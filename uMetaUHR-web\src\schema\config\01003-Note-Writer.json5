{
  type: 'noteWriter',
  ui: {
    type: 'layout',
    theme: 'side_by_side_2',
    children: [
      {
        type: 'tocDetail',
        toc: {
          type: 'toc',
          columnDef: [
            {
              field: 'data.sopMarker',
              displayName: '标2'
            }
          ],
          event: {
            listItems: 'list-patients',
            selectItem: 'select-patient'
          },
          selectedLine: 0,
          myFavoriteId: '01003-Note-Writer.41'
        },
        style: {
          toc: {
            width: '10rem'
          },
          main: {
            border: 'none',
            margin: '0 0 0 3px'
          }
        },
        detail: {}
      },
      {
        menu: [
          {
            id: 'pat',
            displayName: '笔记'
          }
        ],
        type: 'stack',
        myFavoriteId: '01003-Note-Writer.79',
        children: {},
        tabPosition: 'right'
      }
    ]
  },
  dialog: {},
  subContext: {
    path: '/temp.noteWriter',
    default: {}
  }
}
