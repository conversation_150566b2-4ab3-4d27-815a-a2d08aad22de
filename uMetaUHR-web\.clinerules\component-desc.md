在处理 \*.vue 文件时，按照下列格式生成控件的文档。
如果章节不适用，需要删除。如果为空也需要删除。

## 控件名称

给出控件的名称，如：Button。

## 控件描述

给出控件的描述，介绍控件的功能、使用场景等。包括：
组件定位：说明控件在体系中的角色（如表单元素/操作触发等）
核心功能：按优先级列出核心能力（不超过5项）
使用场景：典型应用案例（至少3个）
交互规范：给出控件的交互规范，交互全景：包含流程图和状态转换图

## schema

介绍控件的 schema，如何配置控件的界面和行为。
包括字段的定义，特殊要求，注意事项等。

### 示例

```json
{}
```

分析vuejs代码，提炼内容。输出示例的详细解释，字段的定义，特殊要求，注意事项等。

## context

介绍控件的 context。分析vuejs代码，提炼内容，详细介绍如何使用上下文。
必须包含数据流图。

### 示例

分析vuejs代码，提炼内容。输出示例的详细解释。

## 事件

从父子配置的模块的角度，介绍控件的事件、作用和使用方法，包括参数的传递。
给出 provide 应该提供的内容，举例说明。
事件类型：分类说明（用户事件/系统事件）
事件对象：需包含的字段及数据类型

## 。。。 其他内容，需要注意的事项。。。

从schema json中提取需要注意的内容。
