<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div v-if="isVisible" class="choice">
    <label>
      <span class="label">
        {{ props?.schema?.label }}
        <span v-if="props?.schema?.valid?.required" class="required">*</span>
      </span>
      <select v-model="modelValue" :class="{ 'input-error': error }" @blur="validateInput">
        <option v-for="option in options" :key="option.value" :value="option.value">
          {{ option.label }}
        </option>
      </select>
    </label>
    <span v-if="error" :title="error" class="error"> * </span>
  </div>
</template>

<script lang="ts" setup>
import { computed, defineProps, ref, watchEffect } from 'vue'
import useValidation from '../mixins/validationMixin'
import { evaluateShowWhen, getNestedValue, setNestedValue } from '@/lib/getContext'
import { getCodeMapFromMetadata } from '@/lib/metadataUtils'

const props = defineProps<{
  schema: {
    type?: string
    label?: string
    model: string
    metadata?: string
    options?: Array<{ value: string | number; label: string }>
    inputType?: string
    dataType?: string
    showWhen?: string
    valid?: {
      required?: boolean
    }
  }
  context: { [key: string]: any }
}>()

const { error, validate } = useValidation()

const options = ref<Array<{ value: string | number; label: string }>>([])

watchEffect(async () => {
  if (props.schema.options) {
    options.value = props.schema.options
    return
  }

  if (!props.schema.metadata) {
    options.value = []
    return
  }

  const codeMap = await getCodeMapFromMetadata(props.schema.metadata)
  return (options.value = Object.entries(codeMap).map(([value, label]) => ({
    value,
    label: String(label)
  })))
})

const modelValue = computed({
  get: () => getNestedValue(props.context, props.schema.model),
  set: (value) => setNestedValue(props.context, props.schema.model, value)
})

const isVisible = computed(() => evaluateShowWhen(props))

function validateInput() {
  validate(modelValue.value, {
    required: props.schema?.valid?.required || false
  })
}

watchEffect(() => {
  validateInput()
})

defineExpose({ compName: props?.schema?.type || '-' })
</script>

<style lang="scss" scoped>
.choice {
  display: flex;
  flex-wrap: nowrap;
  margin: 2px;

  label {
    display: flex;
    flex-wrap: nowrap;
    flex: 1 1 auto;
    align-items: center;

    span {
      min-width: 5rem;
      white-space: nowrap;
      text-align: right;
      padding-right: 0.5rem;

      &.required {
        color: red;
      }
    }

    select {
      padding: 2px;
      width: 100%;
      min-width: 5rem;
      border-radius: 0.2rem;
      border: 1px solid #ccc;
      height: 1.5rem;

      &.input-error {
        border-color: red;
      }
    }
  }

  .error {
    color: red;
    padding-left: 0.5rem;
  }
}
</style>
