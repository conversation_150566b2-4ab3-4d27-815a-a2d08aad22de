DROP TABLE IF EXISTS encounter CASCADE;

CREATE TABLE encounter
(
    id                    INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    patient_basic_info_id INT NOT NULL,
    enc_type_c            VARCHAR(10)
        CHECK (enc_type_c IN ('1', '2', '3')),
    contact_date          BIGINT,
    provider_id           INT,
    department_id         VARCHAR(32),
    appt_status_c         VARCHAR(2)
        CHECK (appt_status_c IN ('1', '2', '3')),
    enc_closed_yn         CHAR(1)
        CHECK (enc_closed_yn IN ('Y', 'N')),
    checkin_time          BIGINT,
    checkout_time         BIGINT,
    entry_time            BIGINT DEFAULT (EXTRACT(EPOCH FROM CURRENT_TIMESTAMP)),

    -- 住院专属字段 (当 enc_type_c='3' 时有效)
    hosp_admsn_time       BIGINT,
    hosp_disch_time       BIGINT,
    hsp_account_id        VARCHAR(32),
    bed_number            VARCHAR(20),
    admission_type        VARCHAR(10)
        CHECK (admission_type IS NULL OR admission_type IN ('1', '2', '3')),
    discharge_disposition VARCHAR(10),

    extensions            JSONB  DEFAULT '{}'::JSONB,

    -- 为encounter表添加时间戳字段
    created_at            BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
    updated_at            BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),

    -- 外键约束
    FOREIGN KEY (patient_basic_info_id) REFERENCES patient_basic_info (id)

--     FOREIGN KEY (provider_id) REFERENCES provider (id),
--     FOREIGN KEY (department_id) REFERENCES clarity_dep (id)
);

COMMENT ON TABLE encounter IS '就诊核心表（合并门诊/急诊/住院）';
COMMENT ON COLUMN encounter.id IS '全局唯一就诊流水号 [数据元:ENCOUNTER.ID]';
COMMENT ON COLUMN encounter.patient_basic_info_id IS '患者ID [数据元:ENCOUNTER.PATIENT_ID]';
COMMENT ON COLUMN encounter.enc_type_c IS '就诊类型编码(1-门诊,2-急诊,3-住院) [数据元:ENCOUNTER.TYPE]';
COMMENT ON COLUMN encounter.hosp_admsn_time IS '入院时间 [数据元:ENCOUNTER.ADMISSION.DATE]';
COMMENT ON COLUMN encounter.contact_date IS '就诊日期 [数据元:ENCOUNTER.DATE]';
COMMENT ON COLUMN encounter.provider_id IS '接诊医生ID [数据元:ENCOUNTER.PROVIDER]';
COMMENT ON COLUMN encounter.department_id IS '就诊科室ID [数据元:ENCOUNTER.DEPARTMENT]';
COMMENT ON COLUMN encounter.appt_status_c IS '就诊状态(1-预约,2-就诊中,3-完成) [数据元:ENCOUNTER.STATUS]';
COMMENT ON COLUMN encounter.enc_closed_yn IS '就诊是否关闭(Y/N) [数据元:ENCOUNTER.CLOSED]';
COMMENT ON COLUMN encounter.checkin_time IS '签到时间 [数据元:ENCOUNTER.CHECKIN]';
COMMENT ON COLUMN encounter.checkout_time IS '签退时间 [数据元:ENCOUNTER.CHECKOUT]';
COMMENT ON COLUMN encounter.entry_time IS '记录创建时间 [数据元:COMMON.CREATED]';
COMMENT ON COLUMN encounter.hosp_disch_time IS '出院时间 [数据元:ENCOUNTER.DISCHARGE.DATE]';
COMMENT ON COLUMN encounter.hsp_account_id IS '住院账户ID';
COMMENT ON COLUMN encounter.bed_number IS '床位号';
COMMENT ON COLUMN encounter.admission_type IS '入院类型(1-急诊,2-门诊,3-其他)';
COMMENT ON COLUMN encounter.discharge_disposition IS '出院去向';
COMMENT ON COLUMN encounter.extensions IS '扩展字段(JSON格式)';
COMMENT ON COLUMN encounter.created_at IS '记录创建时间戳';
COMMENT ON COLUMN encounter.updated_at IS '记录更新时间戳';

-- 创建索引
DROP INDEX IF EXISTS idx_encounter_patient_id;
DROP INDEX IF EXISTS idx_encounter_contact_date;
DROP INDEX IF EXISTS idx_encounter_provider_id;
DROP INDEX IF EXISTS idx_encounter_department;
DROP INDEX IF EXISTS idx_encounter_admsn_time;
DROP INDEX IF EXISTS idx_encounter_disch_time;

CREATE INDEX idx_encounter_patient_id ON encounter (patient_basic_info_id);
CREATE INDEX idx_encounter_contact_date ON encounter (contact_date);
CREATE INDEX idx_encounter_provider_id ON encounter (provider_id);
CREATE INDEX idx_encounter_department ON encounter (department_id);
CREATE INDEX idx_encounter_admsn_time ON encounter (hosp_admsn_time);
CREATE INDEX idx_encounter_disch_time ON encounter (hosp_disch_time);

