{
  type: 'tocDetail',
  toc: {
    type: 'toc',
    query: {
      search: {
        table: 'user',
        tocItems: ['id', 'username'],
        detailItems: ['id', 'data', 'username', 'employ_id'],
        updateItems: ['id', 'data', 'username', 'employ_id']
      }
    },
    columnDef: [
      {
        field: 'id',
        displayName: 'id'
      },
      {
        field: 'username',
        displayName: '姓名'
      }
    ],
    selectedLine: 0
  },
  style: {
    toc: {
      width: '10rem'
    }
  },
  detail: {
    body: [
      {
        type: 'window',
        body: [
          {
            type: 'section',
            myFavoriteId: '00310-MD-users.section-1',
            title: '基本信息',
            children: [
              {
                type: 'inputText',
                label: 'ID',
                model: 'id'
              },
              {
                type: 'inputText',
                label: '用户名',
                model: 'username'
              }
            ]
          },
          {
            type: 'section',
            myFavoriteId: '00310-MD-users.section-1',
            title: '业务数据',
            children: [
              {
                type: 'inputText',
                label: '姓名',
                model: 'data.fullName'
              },
              {
                type: 'inputText',
                label: '工号',
                model: 'employ_id'
              },
              {
                type: 'inputText',
                label: '部门',
                model: 'data.department.0.displayName'
              },
              {
                type: 'inputText',
                label: '角色',
                model: 'data.role.0.displayName'
              },
              {
                type: 'emrEditor',
                model: 'data.memo',
                label: '备注'
              }
            ]
          },
          {
            type: 'section',
            title: '数据',
            myFavoriteId: '00310-MD-users.section-3',
            children: [
              {
                type: 'dataViewer',
                model: ''
              }
            ]
          }
        ],
        subContext: {
          path: 'detail',
          default: {}
        }
      }
    ],
    head: [
      {
        type: 'toolbar',
        children: [
          {
            type: 'button',
            event: {
              name: 'toc-new-data'
            },
            label: '新建'
          },
          {
            type: 'button',
            event: {
              name: 'toc-save-data'
            },
            label: '保存'
          },
          {
            type: 'button',
            event: {
              name: 'toc-delete-data'
            },
            label: '删除'
          }
        ]
      }
    ],
    type: 'window'
  }
}
