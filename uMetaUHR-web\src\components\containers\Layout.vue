<template>
  <div
    ref="containerRef"
    :class="['field-w-layout', schema?.theme, 'side_by_side_2']"
    :style="schema?.style?.main"
  >
    <template v-for="(child, index) in schema?.children || []" :key="`panel-child-${index}`">
      <div
        ref="childRef"
        :class="[
          'field-w-layout-child',
          `child${index}`,
          collapsed,
          { 'fixed-flex': props.schema?.theme == 'side_by_side_2' && index === 1 }
        ]"
        :style="schema?.style?.[`child${index + 1}`]"
      >
        <component
          :is="loadComponent(child?.type)"
          :ref="`childComp${index}`"
          :context="context"
          :schema="child"
          @stack-collapse-status="handleStackCollapseStatus"
        />
      </div>
      <div
        v-if="index === 0 && schema?.children?.length > 1 && collapsed !== 'collapsed'"
        class="field-w-divider"
        @dblclick="handleCollapse($event)"
        @mousedown="startDrag"
      >
        <div class="field-w-thumb">
          <div></div>
          <div></div>
          <div></div>
        </div>
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted, ref } from 'vue'
import { loadComponent } from '@/components/componentRegistry'
import { cacheManager } from '@/lib/CacheManager'

const props = defineProps({
  schema: Object,
  context: Object,
  style: Object
})

const isDragging = ref(false)
const startX = ref(0)
const leftWidth = ref(0)
const rightWidth = ref(0)
const containerRef = ref<HTMLElement | null>(null)
const childRef = ref<HTMLElement[]>([])
const myFavoriteId = props.schema?.myFavoriteId
const collapsed = ref('')
const childComp1 = ref(null)

onMounted(async () => {
  if (!myFavoriteId) return
  const cachedData = (await cacheManager.get(myFavoriteId)) || {}
  if (cachedData.leftWidth && cachedData.rightWidth) {
    const [leftChild, { style: rStyle = {} } = {}] = childRef.value as any
    rStyle.width = `${cachedData.rightWidth}px`
  }
})

function handleCollapse(event: any) {
  event?.preventDefault?.()
  event?.stopPropagation?.()

  const v = childComp1.value?.find?.((o: any) => o?.toggleCollapse)
  if (!v) return
  v.toggleCollapse?.()
}

function handleStackCollapseStatus(status: boolean) {
  collapsed.value = status ? 'collapsed' : 'expanded'
}

const startDrag = (event: MouseEvent) => {
  isDragging.value = true
  startX.value = event.clientX

  const [leftChild, rightChild] = childRef.value
  leftWidth.value = leftChild.offsetWidth
  rightWidth.value = rightChild.offsetWidth

  document.addEventListener('mousemove', onDrag)
  document.addEventListener('mouseup', stopDrag)
}

const onDrag = (event: MouseEvent) => {
  if (!isDragging.value) return

  const [leftChild, rightChild] = childRef.value
  const dx = event.clientX - startX.value
  const newLeftWidth = leftWidth.value + dx
  const newRightWidth = rightWidth.value - dx

  if (newLeftWidth > 0 && newRightWidth > 0) {
    rightChild.style.width = `${newRightWidth}px`
  }
}

const stopDrag = async () => {
  isDragging.value = false
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('mouseup', stopDrag)

  if (!myFavoriteId) return

  const [leftChild, rightChild] = childRef.value || []
  if (!leftChild || !rightChild) return

  const cachedData = {
    leftWidth: leftChild.offsetWidth,
    rightWidth: rightChild.offsetWidth
  }
  await cacheManager.set(myFavoriteId, cachedData)
}

onUnmounted(stopDrag)
defineExpose({ compName: props?.schema?.type || '-' })
</script>

<style lang="scss" scoped>
.side_by_side_2 {
  display: flex;
  flex: 1 1 auto;
  gap: 2px;

  > div {
    flex: 1 1 auto;
    display: flex;
    margin: 3px 0 0 0;
  }

  .child0,
  .child1 {
    overflow: auto;
  }

  .collapsed.child1 {
    flex: 0 0 auto;
    width: unset !important;
  }

  .fixed-flex {
    flex: 0 0 auto;
  }

  .field-w-divider {
    flex: 0 0 7px; // 增加宽度以提升可见性
    background-color: #fafafa;
    cursor: col-resize;
    align-items: center;
    justify-content: center;

    .field-w-thumb {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 4px;

      div {
        width: 5px;
        height: 5px;
        background-color: #bbb; // 使用背景色代替边框
        border-radius: 50%; // 圆形
        transition: background-color 0.3s ease;
      }
    }

    &:hover {
      .field-w-thumb {
        div {
          background-color: brown; // 使用背景色代替边框
          box-shadow: 1px 1px 3px black;
        }
      }
    }
  }
}
</style>
