export async function copyToClipboard(text: string): Promise<void> {
  if (typeof text !== 'string') {
    console.error('Invalid text provided for copying to clipboard.')
    return
  }

  if (navigator.clipboard) {
    try {
      await navigator.clipboard.writeText(text)
      console.log('Text copied to clipboard successfully.')
    } catch (err) {
      console.error('Failed to copy text:', err)
    }
  } else {
    const textArea = document.createElement('textarea')
    textArea.value = text
    textArea.style.position = 'fixed'
    document.body.appendChild(textArea)
    textArea.select()

    try {
      document.execCommand('copy')
      console.log('Text copied successfully.')
    } catch (err) {
      console.error('Failed to copy text:', err)
    }

    document.body.removeChild(textArea)
  }
}

export async function getClipboardContent(): Promise<string | null> {
  if (navigator.clipboard) {
    try {
      return await navigator.clipboard.readText()
    } catch (err) {
      console.error('Failed to read clipboard content:', err)
      return null
    }
  } else {
    console.error('Clipboard API not supported in this browser.')
    return null
  }
}

export async function getAndValidateCopiedContent(
  dataType: string
): Promise<{ error?: string; data?: any }> {
  const text = await getClipboardContent()
  if (text) {
    try {
      const parsed = JSON.parse(text)
      if (typeof parsed !== 'object' || !parsed.dataType || !parsed.data) {
        return { error: 'Invalid data format' }
      }

      const { dataType: actualType, data } = parsed
      if (actualType !== dataType) {
        return { error: `数据类型不匹配。 期望：${dataType} 实际：${actualType}` }
      }
      return { data }
    } catch (err) {
      console.error('Invalid data format:', err)
      return { error: 'Invalid data format' }
    }
  }
  return { error: 'Failed to read clipboard content' }
}
