<template>
  <div v-if="isVisible" :style="schema?.style?.main" class="input-textarea">
    <label>
      <span v-if="schema?.label" class="label">{{ schema?.label }}</span>
      <textarea
        :class="{ 'input-error': error }"
        :rows="3"
        :value="getNestedValue(context, schema?.model)"
        @input="handleInput($event.target.value)"
        @keydown.enter="handleEnter"
      />
    </label>
    <span v-if="error" :title="error" class="error"> * </span>
  </div>
</template>

<script lang="ts" setup>
import { computed, defineProps, inject, watchEffect } from 'vue'
import useValidation from '../mixins/validationMixin'
import { evaluateShowWhen, getContext, getNestedValue, setNestedValue } from '@/lib/getContext'

const props = defineProps<{
  label?: string
  schema: {
    showWhen?: string
    model: string
    style?: { main?: string }
  }
  validate?: { required: boolean }
  context: { [key: string]: any }
}>()

const { error, validate } = useValidation()

function handleInput(value: any) {
  setNestedValue(props.context, props?.schema?.model, value)
  validate(getNestedValue(props.context, props?.schema?.model), props.validate)
}

let enterEvent = inject(props.schema?.event?.enter?.name, () => 1) as Function

function handleEnter(event: KeyboardEvent) {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    let context = getContext(props)
    enterEvent?.({ context })
  }
}

const isVisible = computed(() => evaluateShowWhen(props))

watchEffect(() => {
  validate(getNestedValue(props.context, props?.schema?.model), props.validate)
})
defineExpose({ compName: props?.schema?.type || '-' })
</script>

<style lang="scss" scoped>
.input-textarea {
  display: flex;
  flex-wrap: nowrap;
  margin: 2px;

  //outline: none;

  textarea {
    width: 100%;
    border: 1px solid lightblue;
    border-radius: 5px;
    resize: none;
    //outline: 1px solid lightseagreen;
    font-family: 'system-ui';
  }

  label {
    display: flex;
    flex-wrap: nowrap;
    flex: 1 1 auto;

    span {
      min-width: 5rem;
      white-space: nowrap;
      text-align: right;
      padding-right: 0.5rem;
    }

    input {
      width: 100%;
      min-width: 5rem;
      border-radius: 0.2rem;
      border: 1px solid #ccc;
    }
  }

  .error {
    color: red;
    margin-left: 0.5rem;
  }
}
</style>
