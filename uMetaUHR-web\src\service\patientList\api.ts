
import { DataTablesEnum } from "../../enums";
import type {
  PatListParams,
  PatientBasicInfoParams,
  DiagnosisParams,
  OrderProcParams,
  PatientListResponse,
  PatientCountResponse,
  PatientBasicInfoResponse,
  PatientExtensionInfoResponse,
  DiagnosisResponse,
  OrderProcResponse
} from '../../types';
import { BaseApis } from "../base/api";
import { Urls } from './urls';
import axios from "axios";
import { setupTimeoutInterceptor } from "../base/interceptors";

// 设置超时拦截器
setupTimeoutInterceptor();

const Apis = {
  // 获取指定时间范围的叫号患者列表，可以筛选特定患者。
  getPaList: async (params: PatListParams) => {
    return axios.post(Urls.getPatientList, params, {
      timeoutHandling: true
    }).then(
      (res) => {
        return res.data
      }
    ) as Promise<PatientListResponse>;
  },

  // 获取指定时间范围的叫号患者列表，可以筛选特定患者。
  getPaCount: async (params: PatListParams) => {
    return axios.post(Urls.getPatientCount, params).then(
      (res) => {
        return res.data
      }
    ) as Promise<PatientCountResponse>;
  },

  // 患者基础信息查询接口
  getPatientBasicInfo: (params: PatientBasicInfoParams) => {
    return axios.post(Urls.getBasicInfo, params).then(
      (res) => {
        return res.data
      }
    ) as Promise<PatientBasicInfoResponse>;
  },

  // 患者扩展信息查询接口(既往病史，过敏史，家族史)
  getPatientExtensionInfo: (params: PatientBasicInfoParams) => {
    return axios.post(Urls.getExtensionInfo, params).then(
      (res) => {
        return res.data
      }
    ) as Promise<PatientExtensionInfoResponse>;
  },

  // 近期诊断记录查询
  getRecentDiagnosis: (params: DiagnosisParams) => {
    return BaseApis.listData({
      ...params,
      table: DataTablesEnum.DIAGNOSIS,
    }) as Promise<DiagnosisResponse>;
  },

  // 患者近期检查检验记录
  getRecentOrders: (params: OrderProcParams) => {
    return BaseApis.listData({
      ...params,
      table: DataTablesEnum.ORDER_PROC,
    }) as Promise<OrderProcResponse>;
  },

  // 叫号
  callPatient: (params: PatientBasicInfoParams) => {
    return axios.put(Urls.callPatient, params).then(
      (res) => {
        return res.data
      }
    ) as Promise<any>;
  },
}

export default Apis;
