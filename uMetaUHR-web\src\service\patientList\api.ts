import { DataTablesEnum } from '@/enums'
import type {
  DiagnosisParams,
  DiagnosisResponse,
  OrderProcParams,
  OrderProcResponse,
  PatientBasicInfoParams,
  PatientBasicInfoResponse,
  PatientCountResponse,
  PatientExtensionInfoResponse,
  PatListParams
} from '@/types'
import { BaseApis } from '../base/api'
import { Urls } from './urls'
import axios from 'axios'
import { setupTimeoutInterceptor } from '../base/interceptors'

// 设置超时拦截器
setupTimeoutInterceptor()

function handleAxiosError(error: Error) {
  if (axios.isAxiosError(error)) {
    console.error('Axios error:', error.message)
    throw new Error(`API请求失败: ${error.response?.status || '无响应'}`)
  } else {
    // 处理其他错误
    console.error('Unexpected error:', error)
    throw new Error('获取患者列表时发生未知错误')
  }
}

const Apis = {
  // 获取指定时间范围的叫号患者列表，可以筛选特定患者。
  getPaList: async (params: PatListParams) => {
    try {
      const config = {
        timeoutHandling: true
      }
      const response = await axios.post(Urls.getPatientList, params, config)
      return response.data.data
    } catch (error: any) {
      handleAxiosError(error)
    }
  },

  // 获取指定时间范围的叫号患者列表，可以筛选特定患者。
  getPaCount: async (params: PatListParams) => {
    const res = (await axios.post(Urls.getPatientCount, params)) as PatientCountResponse
    return res.data
  },

  // 患者基础信息查询接口
  getPatientBasicInfo: (params: PatientBasicInfoParams) => {
    return axios.post(Urls.getBasicInfo, params).then((res) => {
      return res.data
    }) as Promise<PatientBasicInfoResponse>
  },

  // 患者扩展信息查询接口(既往病史，过敏史，家族史)
  getPatientExtensionInfo: (params: PatientBasicInfoParams) => {
    return axios.post(Urls.getExtensionInfo, params).then((res) => {
      return res.data
    }) as Promise<PatientExtensionInfoResponse>
  },

  // 近期诊断记录查询
  getRecentDiagnosis: (params: DiagnosisParams) => {
    return BaseApis.listData({
      ...params,
      table: DataTablesEnum.DIAGNOSIS
    }) as Promise<DiagnosisResponse>
  },

  // 患者近期检查检验记录
  getRecentOrders: (params: OrderProcParams) => {
    return BaseApis.listData({
      ...params,
      table: DataTablesEnum.ORDER_PROC
    }) as Promise<OrderProcResponse>
  },

  // 叫号
  callPatient: (params: PatientBasicInfoParams) => {
    return axios.put(Urls.callPatient, params).then((res) => {
      return res.data
    }) as Promise<any>
  }
}

export default Apis
