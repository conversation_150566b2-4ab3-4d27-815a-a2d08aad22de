import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import vueDevTools from 'vite-plugin-vue-devtools'

// 模块编译到其他目录中。

export default defineConfig({
  plugins: [
    vue(),
    vueJsx(),
    vueDevTools()
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  build: {
    rollupOptions: {
      output: {
        entryFileNames: (chunkInfo) => {
          // 根据入口文件名决定输出目录
          if (chunkInfo.name === 'home') {
            return 'home/[name].js'
          } else if (chunkInfo.name === 'about') {
            return 'about/[name].js'
          }
          return 'common/[name].js'
        },
        chunkFileNames: (chunkInfo) => {
          // 根据 chunk 名称决定输出目录
          if (chunkInfo.name && chunkInfo.name.includes('home')) {
            return 'home/[name].js'
          } else if (chunkInfo.name && chunkInfo.name.includes('about')) {
            return 'about/[name].js'
          }
          return 'common/[name].js'
        },
        assetFileNames: (assetInfo) => {
          // 将 CSS 文件放入对应的模块目录
          if (assetInfo.name && assetInfo.name.includes('home')) {
            return 'home/[name].[ext]'
          } else if (assetInfo.name && assetInfo.name.includes('about')) {
            return 'about/[name].[ext]'
          }
          return 'common/[name].[ext]'
        }
      },
      manualChunks(id) {
        // 手动将模块按路由拆分
        if (id.includes('/src/views/Home')) {
          return 'home'
        } else if (id.includes('/src/views/About')) {
          return 'about'
        }
        return 'common'
      }
    }
  }
})