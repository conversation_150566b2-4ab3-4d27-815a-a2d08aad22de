<template>
  <div class="login">
    <div class="login-box">
      <component
        :is="loadComponent(props.schema?.ui?.type)"
        v-if="props.schema"
        :context="getContext(props)"
        :schema="props.schema?.ui"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { defineProps, inject, provide } from 'vue'
import { loadComponent } from '../componentRegistry'
import axios from 'axios'
import { getContext, setNestedValue } from '@/lib/getContext'
import { dbSave } from '@/lib/dataMan'

const props = defineProps<{
  schema: {
    type: string
    children: Array<any>
  }
  context: { [key: string]: any }
}>()

let switchWorkspace = inject('switch-workspace') as Function

provide('login-login', async (event: any) => {
  const { context: { user: { loginInfo: { username = '', password = '' } = {} } = {} } = {} } =
    props || {}

  try {
    // Sending login request to the server
    const response = await axios.post('http://localhost:8080/login', { username, password })
    const { data: { status, message, sessionId } = {} } = response

    if (status === 'success' || true) {
      // Successful login, store sessionId and switch workspace
      localStorage.setItem('sessionId', sessionId)
      console.log('Session ID stored successfully:', sessionId)

      await switchWorkspace(event)
    } else {
      // Handle unexpected status
      alert(`Login failed: ${message}`)
    }
  } catch (error) {
    // Displaying error message
    alert('Login failed: ' + (error?.response?.data?.message || error.message))
  }
})

provide('login-register-start', async (event: any) => {
  setNestedValue(props.context, 'user.loginInfo.mode', 'register')
})

provide('login-register', async (event: any) => {
  let { context: { user: { loginInfo = {} } = {} } = {} } = props as any
  const fields = ['username', 'password', 'data']
  const data: Record<string, any> = {}
  for (const key of fields) {
    if (key in loginInfo) {
      data[key] = loginInfo[key]
    }
  }
  await dbSave({
    table: 'user',
    data: [data],
    conflict_column: 'id',
    update_columns: ['username', 'password', 'data']
  })
})

defineExpose({ compName: props?.schema?.type || '-' })
</script>

<style lang="scss" scoped>
.login {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  width: 100vw;
  background: transparent;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;

  .login-box {
    display: flex;
    flex-direction: column;
    width: 20rem;
  }
}
</style>
