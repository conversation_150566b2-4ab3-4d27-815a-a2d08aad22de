import axios from "axios";

export function setupTimeoutInterceptor() {
  // 请求拦截器
  axios.interceptors.request.use(config => {
    if (config.timeoutHandling) {
      const source = axios.CancelToken.source();
      config.cancelToken = source.token;

      // 加载超时 - 显示加载 (默认3秒)
      const loadingTimeout = config.loadingTimeout || 3000;
      config.loadingTimer = setTimeout(() => {
        window.popSpin?.(true);
      }, loadingTimeout);

      // 请求超时 - 取消请求并提示 (默认10秒)
      const requestTimeout = config.requestTimeout || 10000;
      config.timeoutTimer = setTimeout(() => {
        window.popSpin?.(false);
        window.popAlert({
          message: '网络异常，请尝试刷新页面重试',
        })
        source.cancel('网络异常，请尝试刷新页面重试');
        clearTimeout(config.loadingTimer);
      }, requestTimeout);
    }
    return config;
  });

  // 响应拦截器
  axios.interceptors.response.use(
    response => {
      // 清除定时器
      if (response.config.timeoutHandling) {
        window.popSpin?.(false);
        clearTimeout(response.config.loadingTimer);
        clearTimeout(response.config.timeoutTimer);
      }
      return response;
    },
    error => {
      if (error.config?.timeoutHandling) {
        window.popSpin?.(false);
        clearTimeout(error.config.loadingTimer);
        clearTimeout(error.config.timeoutTimer);
      }
      return Promise.reject(error);
    }
  );
}
