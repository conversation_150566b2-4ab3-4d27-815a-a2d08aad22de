import { onMounted } from 'vue'
import { storeToRefs } from 'pinia'
// import { usePatientListStore } from '@/stores'
import Apis from '@/service'

export function usePatientList() {
  // const paStore = usePatientListStore()
  const paStore = {} as any
  const { patientListData } = storeToRefs(paStore) as any
  const getPatientList = async () => {
    const res = await Apis.getPaList({
      "columns":[
          "operation",
          "queue_number",
          "patient_id",
          "visit_status",
          "patient_name",
          "gender",
          "birth_date",
          "visit_type",
          "diagnosis",
          "fee_category",
          "medical_card_no",
          "id_card_no",
          "registration_date",
          "registering_doctor",
          "visit_period",
          "triage_time",
          "attending_doctor",
          "department"
      ]
    })
    paStore.setPatientListData(res.data)
  }

  onMounted(() => {
    getPatientList()
  })

  return {
    getPatientList,
    patientListData
  }
}