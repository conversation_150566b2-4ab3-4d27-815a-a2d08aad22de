## 控件名称

RecordEntry

## 控件描述

RecordEntry 是一个表格形式的记录录入控件，支持动态添加、编辑和操作数据行。

### 组件定位
- 数据录入：用于结构化数据的批量录入
- 表格编辑器：提供行内编辑和操作功能

### 核心功能
1. 可配置的表头和多列显示
2. 动态添加新记录
3. 行内编辑功能
4. 每行操作按钮支持
5. 与RecordSelector组件集成

### 使用场景
1. 患者信息批量录入
2. 药品处方编辑
3. 检查项目添加

### 交互规范
1. 鼠标悬停显示行操作按钮
2. 点击添加新记录
3. 支持行内编辑展开
4. 键盘导航支持（通过RecordSelector）

## schema

RecordEntry 通过 schema 属性进行配置，主要字段包括：

- `columnDef`: 表格列定义数组
  - `field`: 数据字段名
  - `displayName`: 列显示名称
  - `style`: 列样式
- `head`: 表头组件配置数组
- `actions`: 行操作按钮配置数组
- `recordSelector`: RecordSelector配置
- `model`: 数据绑定字段路径
- `event`: 事件配置
  - `selectItem`: 选择行项时触发的事件名

### 示例

```json
{
  "columnDef": [
    {
      "field": "name",
      "displayName": "药品名称"
    },
    {
      "field": "dose",
      "displayName": "剂量"
    }
  ],
  "head": [
    {
      "type": "text",
      "label": "处方单号"
    }
  ],
  "actions": [
    {
      "type": "button",
      "label": "删除"
    }
  ],
  "recordSelector": {
    "label": "添加药品",
    "columnDef": [
      {
        "field": "name",
        "displayName": "名称"
      }
    ]
  },
  "model": "prescription.items",
  "event": {
    "selectItem": "select-medication"
  }
}
```

注意事项：
1. `columnDef` 必须配置至少一个列
2. `model` 字段必须指定正确的数据路径
3. 操作按钮需要配置对应的组件类型

## context

RecordEntry 使用 Vue 的依赖注入机制与子组件通信。

### 数据流图

```mermaid
graph TD
    Parent[父组件] -->|provide| ListRecords[记录列表]
    RecordEntry -->|inject| ListRecords
    RecordEntry -->|provide| SelectHandler[选择处理器]
    RecordEntry -->|provide| Refresh[刷新方法]
    RecordEntry -->|更新| Context[上下文数据]
    RecordSelector -->|inject| SelectHandler
```

关键上下文交互：
1. 通过 `list-records` 注入点获取初始记录
2. 通过 `select-suggestion` 提供点处理新记录添加
3. 通过 `refresh-records` 提供点刷新数据
4. 直接更新上下文中的绑定字段

## 事件

RecordEntry 处理以下类型的事件：

### 用户事件
1. 记录选择事件 (通过RecordSelector)
   - 触发新记录添加
   - 更新绑定字段值
2. 行操作事件
   - 按钮点击触发对应操作
3. 行选择事件
   - 高亮显示选中行

### 系统事件
1. `selectItem`
   - 参数：包含选中行数据的对象
   - 作用：通知父组件行选择变化

### provide 内容示例
父组件应提供：
```javascript
provide('list-records', async () => {
  // 返回记录数组
  return await fetchRecords()
})
```

## 其他注意事项

1. 行操作按钮默认在悬停时显示
2. 每行可配置独立的编辑组件
3. 依赖RecordSelector组件添加新记录
4. 支持动态刷新记录列表
5. 表格样式使用CSS scoped防止污染
