<template>
  <div class="help-tip-container" @mouseleave="hideTip" @mouseover="showTip">
    <slot></slot>
    <div v-if="visible" class="help-tip">{{ message }}</div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

const props = defineProps<{
  message: string
}>()

const visible = ref(false)

function showTip() {
  visible.value = true
}

function hideTip() {
  visible.value = false
}

defineExpose({ compName: props?.schema?.type || '-' })
</script>

<style scoped>
.help-tip-container {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.help-tip {
  visibility: visible;
  width: 200px;
  background-color: #555;
  color: #fff;
  text-align: center;
  border-radius: 5px;
  padding: 5px 0;
  position: absolute;
  z-index: 1;
  bottom: 125%; /* Position above the container */
  left: 50%;
  margin-left: -100px;
  opacity: 0;
  transition: opacity 0.3s;
}

.help-tip::after {
  content: '';
  position: absolute;
  top: 100%; /* Arrow below the tooltip */
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: #555 transparent transparent transparent;
}

.help-tip-container:hover .help-tip {
  opacity: 1;
}
</style>
