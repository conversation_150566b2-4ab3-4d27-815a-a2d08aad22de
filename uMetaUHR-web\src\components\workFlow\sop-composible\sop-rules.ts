import { provide } from 'vue'

const alert = async (message: string): Promise<void> => {
  console.log(message)
  if (typeof window.popAlert !== 'function') {
    throw new Error('window.popAlert is not available')
  }
  return window.popAlert({ message })
}

const changeDisplayModeOfLabsAndExam = async (param: { value: unknown }) => {
  const { value } = param
  // await alert(`Value: ${value}`)
}

function useSopRules() {
  provide('change-display-mode-of-labs-and-exam', changeDisplayModeOfLabsAndExam)
}

export { alert, useSopRules }
