-- Drop existing tables if they exist (with cascade to handle dependencies)
DROP TABLE IF EXISTS diagnosis_group_mappings CASCADE;
DROP TABLE IF EXISTS diagnosis_groups CASCADE;
DROP TABLE IF EXISTS diagnosis_order_mappings CASCADE;
DROP TABLE IF EXISTS diagnosis_evidences CASCADE;
DROP TABLE IF EXISTS diagnosis_timelines CASCADE;
DROP TABLE IF EXISTS patient_diagnoses CASCADE;
DROP TABLE IF EXISTS diagnosis_category_mappings CASCADE;
DROP TABLE IF EXISTS diagnosis_categories CASCADE;
DROP TABLE IF EXISTS diagnoses CASCADE;

-- 诊断主表 (存储诊断基本信息)
CREATE TABLE diagnoses
(
    id             INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    diagnosis_code VARCHAR(20)  NOT NULL, -- ICD-10等标准编码
    diagnosis_name VARCHAR(100) NOT NULL,
    description    TEXT,
    is_active      BOOLEAN      DEFAULT TRUE,
    created_at     BIGINT      NOT NULL DEFAULT EXTRACT(EPOCH FROM NOW()),
    updated_at     BIGINT      NOT NULL DEFAULT EXTRACT(EPOCH FROM NOW()),
    CONSTRAINT uk_diagnosis_code UNIQUE (diagnosis_code)
);

COMMENT ON TABLE diagnoses IS '标准诊断库';
COMMENT ON COLUMN diagnoses.id IS '诊断ID，主键';
COMMENT ON COLUMN diagnoses.diagnosis_code IS '诊断编码(ICD-10等)';
COMMENT ON COLUMN diagnoses.diagnosis_name IS '诊断名称';
COMMENT ON COLUMN diagnoses.description IS '诊断描述';
COMMENT ON COLUMN diagnoses.is_active IS '是否启用';

-- 诊断分类表
CREATE TABLE diagnosis_categories
(
    id           INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    category_name VARCHAR(50) NOT NULL,
    parent_id     INT,
    level         INT         NOT NULL,
    sort_order    INT,
    created_at    BIGINT     NOT NULL DEFAULT EXTRACT(EPOCH FROM NOW()),
    updated_at    BIGINT     NOT NULL DEFAULT EXTRACT(EPOCH FROM NOW()),
    FOREIGN KEY (parent_id) REFERENCES diagnosis_categories (id)
);

COMMENT ON TABLE diagnosis_categories IS '诊断分类表';
COMMENT ON COLUMN diagnosis_categories.parent_id IS '父分类ID';

-- 诊断与分类关联表
CREATE TABLE diagnosis_category_mappings
(
    id           INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    diagnosis_id INT NOT NULL,
    category_id  INT NOT NULL,
    created_at   INTEGER NOT NULL DEFAULT EXTRACT(EPOCH FROM NOW()),
    updated_at   INTEGER NOT NULL DEFAULT EXTRACT(EPOCH FROM NOW()),
    FOREIGN KEY (diagnosis_id) REFERENCES diagnoses (id),
    FOREIGN KEY (category_id) REFERENCES diagnosis_categories (id),
    CONSTRAINT uk_diagnosis_category UNIQUE (diagnosis_id, category_id)
);

COMMENT ON TABLE diagnosis_category_mappings IS '诊断与分类关联表';

-- 患者诊断记录表 (患者实际诊断记录)
CREATE TABLE patient_diagnoses
(
    id              INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    patient_id      VARCHAR(50) NOT NULL,               -- 患者ID
    encounter_id    VARCHAR(50),                        -- 就诊ID(可选)
    diagnosis_id    INT         NOT NULL,               -- 诊断ID
    diagnosis_type  VARCHAR(20) NOT NULL CHECK (diagnosis_type IN
                                              ('PROBLEM_LIST', 'ENCOUNTER', 'HISTORICAL', 'PRIMARY',
                                               'SECONDARY')),
    diagnosis_status VARCHAR(20) NOT NULL CHECK (diagnosis_status IN ('ACTIVE', 'RESOLVED', 'REMOVED', 'CHRONIC')),
    onset_date      DATE,                               -- 发病日期
    recorded_date   BIGINT     NOT NULL DEFAULT EXTRACT(EPOCH FROM NOW()),
    recorded_by     VARCHAR(50) NOT NULL,               -- 记录人ID
    verified_by     VARCHAR(50),                        -- 审核人ID
    verified_date   BIGINT DEFAULT NULL,
    is_primary      BOOLEAN     DEFAULT FALSE,          -- 是否主要诊断
    notes           TEXT,
    evidence_data   JSONB      DEFAULT '[]'::jsonb,
    timeline_data   JSONB      DEFAULT '[]'::jsonb,
    created_at      BIGINT     NOT NULL DEFAULT EXTRACT(EPOCH FROM NOW()),
    updated_at      BIGINT     NOT NULL DEFAULT EXTRACT(EPOCH FROM NOW()),
    FOREIGN KEY (diagnosis_id) REFERENCES diagnoses (id)
);

COMMENT ON TABLE patient_diagnoses IS '患者诊断记录表';
COMMENT ON COLUMN patient_diagnoses.diagnosis_type IS '诊断类型：问题列表、就诊诊断、历史诊断等';
COMMENT ON COLUMN patient_diagnoses.diagnosis_status IS '诊断状态：活跃、已解决、已移除等';
COMMENT ON COLUMN patient_diagnoses.is_primary IS '是否主要诊断';
