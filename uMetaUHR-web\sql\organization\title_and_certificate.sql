drop table if exists title cascade;
drop table if exists certificate cascade;

CREATE TABLE title
(
    -- Title ID - Primary key for the title table
    title_id    VARCHAR(10) PRIMARY KEY,

    -- Title name - The name of the professional title
    title_name  VARCHAR(50) NOT NULL,

    -- Title level - Numeric value representing hierarchy (1=highest)
    title_level INT         NOT NULL CHECK (title_level BETWEEN 1 AND 4),

    -- Description - Detailed explanation of the title
    description TEXT,

    -- Creation time - When this title record was created
    created_at  BIGINT  DEFAULT EXTRACT(EPOCH FROM NOW()),

    -- Update time - When this title record was last modified
    updated_at  BIGINT  DEFAULT EXTRACT(EPOCH FROM NOW()),

    -- Active status - Whether this title is currently active
    is_active   BOOLEAN DEFAULT TRUE
);

COMMENT ON TABLE title IS 'Stores professional titles and their hierarchy levels';
COMMENT ON COLUMN title.title_id IS 'Unique identifier for the title';
COMMENT ON COLUMN title.title_name IS 'Name of the professional title (e.g., Chief Physician)';
COMMENT ON COLUMN title.title_level IS 'Hierarchy level (1=highest, 4=lowest)';
COMMENT ON COLUMN title.description IS 'Detailed description of the title and responsibilities';
COMMENT ON COLUMN title.created_at IS 'Timestamp when the title was created';
COMMENT ON COLUMN title.updated_at IS 'Timestamp when the title was last updated';
COMMENT ON COLUMN title.is_active IS 'Whether the title is currently active in the system';

CREATE TABLE certificate
(
    -- certificate type ID - Primary key
    cert_type_id           VARCHAR(10) PRIMARY KEY,

    -- certificate name - Name of the certificate
    cert_name              VARCHAR(100) NOT NULL,

    -- Description - Details about the certificate
    description            TEXT,

    -- Validity period - How long the certificate is valid (in months)
    validity_period_months INT,

    -- Required for operation - Whether this certificate is mandatory
    is_required            BOOLEAN DEFAULT FALSE,

    -- Creation time
    created_at             BIGINT  DEFAULT EXTRACT(EPOCH FROM NOW()),

    -- Update time
    updated_at             BIGINT  DEFAULT EXTRACT(EPOCH FROM NOW())
);

COMMENT ON TABLE certificate IS 'Types of professional certificates';
COMMENT ON COLUMN certificate.cert_type_id IS 'Unique identifier for certificate type';
COMMENT ON COLUMN certificate.cert_name IS 'Name of the certificate (e.g., Surgical Qualification)';
COMMENT ON COLUMN certificate.description IS 'Detailed description of the certificate';
COMMENT ON COLUMN certificate.validity_period_months IS 'Duration in months before recertificate is needed';
COMMENT ON COLUMN certificate.is_required IS 'Whether this certificate is mandatory for certain roles';
