<template>
  <div :class="['rich-text-editor', schema?.theme]" :style="schema?.style?.main">
    <div v-if="schema?.head">
      <component
        :is="loadComponent(tb.type)"
        v-for="(tb, index) in schema?.head || []"
        :key="`head-${index}`"
        :context="getContext(props)"
        :schema="tb"
      />
    </div>
    <span v-if="schema?.label" class="label">{{ schema?.label }}</span>
    <iframe
      :srcdoc="noteEditorText"
      ref="myIframe"
      style="width: 100%; height: 500px; border: none"
    />
    <div ref="noteEditor"></div>
    <WnDialog ref="dialog" />
  </div>
</template>

<script lang="ts" setup>
import { computed, defineProps, onBeforeUnmount, onMounted, provide, ref } from 'vue'

import { getContext, getNestedValue, setNestedValue } from '@/lib/getContext'
import WnDialog from '@/components/containers/WnDialog.vue'
import noteEditorText from './noteEditor/noteEditor.html?raw'
import { loadComponent } from '@/components/componentRegistry'

const myIframe = ref(null)

const sendDataToIframe = (data: any) => {
  const iframeWindow = myIframe.value?.contentWindow
  iframeWindow?.postMessage?.(data, '*')
}

// Refs
const dialog = ref<InstanceType<typeof WnDialog>>()
const noteEditor = ref({} as HTMLElement)

// Props
const props = defineProps<{
  label?: string
  model?: string
  schema: {
    type: string
    theme?: string
    label?: string
    myFavoriteId: string
    model?: string
    eventEndPoint?: string
    shouldNotGroupWhenFull?: boolean
    event?: {
      editorOnChange?: string
    }
  }
  context: { [key: string]: any }
}>()

// Computed
const modelValue = computed(
  () => props.schema?.model && getNestedValue(props.context, props.schema.model)
)

// Watchers
// watch(modelValue, (newValue, oldValue) => {
//   // if (newValue == oldValue) return
//   if (!props?.schema?.mode) return
//   let data = getNestedValue(props.context, props?.schema?.model || '')
//   sendDataToIframe({ data })
// })

provide('data-sync', async (param: any = {}) => {
  let data = getNestedValue(props.context, props?.schema?.model || '')
  sendDataToIframe({ data })
})

const handleMessage = async (event: Event) => {
  // Important: Verify the message origin for security
  // if (event.origin !== 'expected-origin') return;

  if (event?.data?.type != 'note-content') return
  let { data: { data = '' } = {} } = event as any
  if (!data) return

  setNestedValue(props.context, props?.schema?.model || '', data)
}

onMounted(() => {
  window.addEventListener('message', handleMessage)
})

onBeforeUnmount(() => {
  window.removeEventListener('message', handleMessage)
})

defineExpose({ compName: props?.schema?.type || '-' })
</script>

<style scoped>
.rich-text-editor {
  /*height: 20rem;*/
}

/* CKEditor specific styles */
</style>

<style lang="scss">
.rich-text-editor {
  //flex: 1 1 auto;
  //border: 10px solid greenyellow;
  //overflow: hidden;
  //display: flex;
  //display: block;
  //height: 100%;
}

.ck-editor__editable_inline:not(.ck-comment__input *) {
  //flex: 1 1 auto;
  //border: 10px solid red;
  //overflow: hidden;

  min-height: 20rem;
  //max-height: 300px;
  //overflow-y: auto;
}

.standard_height {
  .ck-editor__editable_inline:not(.ck-comment__input *) {
    //min-height: auto;
    //max-height: none;
    //height: 250px;
    //overflow-y: auto;
  }
}

.ck-powered-by {
  display: none !important;
}

.ck-focused {
  border: 1px solid lightblue !important;
}
</style>
