<template>
  <div class="sop">
    <component
      :is="loadComponent(props.schema?.ui?.type)"
      v-if="props.schema"
      :context="getContext(props)"
      :schema="props.schema?.ui"
    />
    <WnDialog ref="dialog" />
  </div>
</template>

<script lang="ts" setup>
import { defineProps } from 'vue'
import { loadComponent } from '../componentRegistry'
import { getContext } from '@/lib/getContext'
import WnDialog from '@/components/containers/WnDialog.vue'

const props = defineProps<{
  schema: any
  context: { [key: string]: any }
}>()

defineExpose({ compName: props?.schema?.type || '-' })
</script>
<style lang="scss" scoped>
.sop {
  display: flex;
  flex: 1 1 auto;
}
</style>
