import { inject } from 'vue'

let rootContext: any = null

export function getContext(props: any, contextPath: string | number = '') {
  const {
    context: parentContext,
    schema: { subContext: { path = '' + contextPath, default: defaultValue = {} } = {} } = {}
  } = props || {}

  if (path.trim() === '') return parentContext
  if (typeof parentContext !== 'object' || parentContext === null) return parentContext

  let pathSegments = path.split('.')
  let currentContext = parentContext

  // 如果路径以 '/' 开头，则从根节点开始
  if (path.startsWith('/')) {
    if (!rootContext) {
      const getRootContext = inject('ROOT_CONTEXT') as Function
      rootContext = getRootContext()
    }
    pathSegments = path.substring(1).split('.')
    currentContext = rootContext
  }

  for (let i = 0; i < pathSegments.length; i++) {
    const segment = pathSegments[i]?.trim() || ''
    if (segment === '') {
      throw new Error(
        `Invalid segment in path at index ${i} from ${path}: must be a non-empty string`
      )
    }

    const isNumber = !isNaN(Number(segment))
    const isLastSegment = i === pathSegments.length - 1

    if (!(segment in currentContext)) {
      currentContext[segment] = isLastSegment ? defaultValue : isNumber ? [] : {}
    } else if (isLastSegment && typeof currentContext[segment] !== typeof defaultValue) {
      // throw new Error(`Type mismatch at path segment ${segment} from path ${path}: expected ${typeof defaultValue}, got ${typeof currentContext[segment]}`)
    }

    if (
      !isLastSegment &&
      (typeof currentContext[segment] !== 'object' || currentContext[segment] === null)
    ) {
      throw new Error(
        `Invalid context at path segment ${segment}: must be a non-null object or array`
      )
    }

    currentContext = currentContext[segment]
  }

  return currentContext || {}
}

export function getNestedValue(obj: any, path: string) {
  return path?.split('.')?.reduce((acc, part) => acc && acc[part], obj)
}

export function setNestedValue(obj: any, path: string, value: any) {
  if (obj == null) return
  const parts = path?.split('.')
  const last = parts.pop()
  let target = obj
  for (const part of parts) {
    if (!target[part]) {
      target[part] = {}
    }
    target = target[part]
  }
  if (last) {
    target[last] = value
  } else {
    if (!(value instanceof Object)) return
    const t = Object.assign({}, value)
    for (const keys in target) delete target[keys]
    Object.assign(target, t)
  }
}

export function evaluateShowWhen(props: any) {
  const { context = {}, schema: { showWhen = '' } = {} } = props
  if (!context || !showWhen) return true
  try {
    return new Function('props', 'context', 'schema', `return ${showWhen}`)(
      props,
      context,
      props?.schema ?? {}
    )
  } catch (e) {
    console.error(`Error evaluating showWhen ${showWhen} condition: `, e)
    return false
  }
}

export function evaluateDisabledWhen(props: any) {
  const { context = {}, schema: { disabledWhen = '' } = {} } = props
  if (!context || !disabledWhen) return false
  try {
    return new Function('props', 'context', 'schema', `return ${disabledWhen}`)(
      props,
      context,
      props?.schema ?? {}
    )
  } catch (e) {
    console.error(`Error evaluating disabledWhen ${disabledWhen} condition: `, e)
    return false
  }
}
