<template>
  <div v-for="item in data">
    <component
      v-for="(child, index) in schema.children"
      :is="loadComponent(child?.type)"
      :key="index"
      :context="{...getContext(props), contextAddonsData: item}"
      :schema="child"
    />
  </div>
</template>

<script lang="ts" setup>
import { defineProps, computed } from 'vue'
import { loadComponent } from '../componentRegistry'
import { getContext, getNestedValue } from '@/lib/getContext'

const props = defineProps<{
  schema: {
    type: string,
    model: string,
    children: any[]
  }
  context: { [key: string]: any },
}>()

const data = computed(()=>{
  return getNestedValue(props.context, props.schema.model)
})


</script>