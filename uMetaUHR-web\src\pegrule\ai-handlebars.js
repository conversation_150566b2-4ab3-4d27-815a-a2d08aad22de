// @generated by Peggy 4.2.0.
//
// https://peggyjs.org/

function peg$subclass(child, parent) {
  function C() {
    this.constructor = child
  }

  C.prototype = parent.prototype
  child.prototype = new C()
}

function peg$SyntaxError(message, expected, found, location) {
  var self = Error.call(this, message)
  // istanbul ignore next Check is a necessary evil to support older environments
  if (Object.setPrototypeOf) {
    Object.setPrototypeOf(self, peg$SyntaxError.prototype)
  }
  self.expected = expected
  self.found = found
  self.location = location
  self.name = 'SyntaxError'
  return self
}

peg$subclass(peg$SyntaxError, Error)

function peg$padEnd(str, targetLength, padString) {
  padString = padString || ' '
  if (str.length > targetLength) {
    return str
  }
  targetLength -= str.length
  padString += padString.repeat(targetLength)
  return str + padString.slice(0, targetLength)
}

peg$SyntaxError.prototype.format = function (sources) {
  var str = 'Error: ' + this.message
  if (this.location) {
    var src = null
    var k
    for (k = 0; k < sources.length; k++) {
      if (sources[k].source === this.location.source) {
        src = sources[k].text.split(/\r\n|\n|\r/g)
        break
      }
    }
    var s = this.location.start
    var offset_s =
      this.location.source && typeof this.location.source.offset === 'function'
        ? this.location.source.offset(s)
        : s
    var loc = this.location.source + ':' + offset_s.line + ':' + offset_s.column
    if (src) {
      var e = this.location.end
      var filler = peg$padEnd('', offset_s.line.toString().length, ' ')
      var line = src[s.line - 1]
      var last = s.line === e.line ? e.column : line.length + 1
      var hatLen = last - s.column || 1
      str +=
        '\n --> ' +
        loc +
        '\n' +
        filler +
        ' |\n' +
        offset_s.line +
        ' | ' +
        line +
        '\n' +
        filler +
        ' | ' +
        peg$padEnd('', s.column - 1, ' ') +
        peg$padEnd('', hatLen, '^')
    } else {
      str += '\n at ' + loc
    }
  }
  return str
}

peg$SyntaxError.buildMessage = function (expected, found) {
  var DESCRIBE_EXPECTATION_FNS = {
    literal: function (expectation) {
      return '"' + literalEscape(expectation.text) + '"'
    },

    class: function (expectation) {
      var escapedParts = expectation.parts.map(function (part) {
        return Array.isArray(part)
          ? classEscape(part[0]) + '-' + classEscape(part[1])
          : classEscape(part)
      })

      return '[' + (expectation.inverted ? '^' : '') + escapedParts.join('') + ']'
    },

    any: function () {
      return 'any character'
    },

    end: function () {
      return 'end of input'
    },

    other: function (expectation) {
      return expectation.description
    }
  }

  function hex(ch) {
    return ch.charCodeAt(0).toString(16).toUpperCase()
  }

  function literalEscape(s) {
    return s
      .replace(/\\/g, '\\\\')
      .replace(/"/g, '\\"')
      .replace(/\0/g, '\\0')
      .replace(/\t/g, '\\t')
      .replace(/\n/g, '\\n')
      .replace(/\r/g, '\\r')
      .replace(/[\x00-\x0F]/g, function (ch) {
        return '\\x0' + hex(ch)
      })
      .replace(/[\x10-\x1F\x7F-\x9F]/g, function (ch) {
        return '\\x' + hex(ch)
      })
  }

  function classEscape(s) {
    return s
      .replace(/\\/g, '\\\\')
      .replace(/\]/g, '\\]')
      .replace(/\^/g, '\\^')
      .replace(/-/g, '\\-')
      .replace(/\0/g, '\\0')
      .replace(/\t/g, '\\t')
      .replace(/\n/g, '\\n')
      .replace(/\r/g, '\\r')
      .replace(/[\x00-\x0F]/g, function (ch) {
        return '\\x0' + hex(ch)
      })
      .replace(/[\x10-\x1F\x7F-\x9F]/g, function (ch) {
        return '\\x' + hex(ch)
      })
  }

  function describeExpectation(expectation) {
    return DESCRIBE_EXPECTATION_FNS[expectation.type](expectation)
  }

  function describeExpected(expected) {
    var descriptions = expected.map(describeExpectation)
    var i, j

    descriptions.sort()

    if (descriptions.length > 0) {
      for (i = 1, j = 1; i < descriptions.length; i++) {
        if (descriptions[i - 1] !== descriptions[i]) {
          descriptions[j] = descriptions[i]
          j++
        }
      }
      descriptions.length = j
    }

    switch (descriptions.length) {
      case 1:
        return descriptions[0]

      case 2:
        return descriptions[0] + ' or ' + descriptions[1]

      default:
        return (
          descriptions.slice(0, -1).join(', ') + ', or ' + descriptions[descriptions.length - 1]
        )
    }
  }

  function describeFound(found) {
    return found ? '"' + literalEscape(found) + '"' : 'end of input'
  }

  return 'Expected ' + describeExpected(expected) + ' but ' + describeFound(found) + ' found.'
}

function peg$parse(input, options) {
  options = options !== undefined ? options : {}

  var peg$FAILED = {}
  var peg$source = options.grammarSource

  var peg$startRuleFunctions = { start: peg$parsestart }
  var peg$startRuleFunction = peg$parsestart

  var peg$c0 = '{{#ai'
  var peg$c1 = '}}'
  var peg$c2 = '{{/ai}}'
  var peg$c3 = '"'

  var peg$r0 = /^[^"]/
  var peg$r1 = /^[0-9]/
  var peg$r2 = /^[a-zA-Z0-9_]/
  var peg$r3 = /^[ \t\n\r]/

  var peg$e0 = peg$anyExpectation()
  var peg$e1 = peg$literalExpectation('{{#ai', false)
  var peg$e2 = peg$literalExpectation('}}', false)
  var peg$e3 = peg$literalExpectation('{{/ai}}', false)
  var peg$e4 = peg$literalExpectation('"', false)
  var peg$e5 = peg$classExpectation(['"'], true, false)
  var peg$e6 = peg$classExpectation([['0', '9']], false, false)
  var peg$e7 = peg$classExpectation([['a', 'z'], ['A', 'Z'], ['0', '9'], '_'], false, false)
  var peg$e8 = peg$classExpectation([' ', '\t', '\n', '\r'], false, false)

  var peg$f0 = function (textBefore, aiBlock, textAfter) {
    return {
      textBefore: textBefore ?? '',
      aiContent: aiBlock?.content ?? '',
      args: aiBlock?.args,
      textAfter: textAfter?.join?.('') ?? '',
      hasAICommands: true
    }
  }
  var peg$f1 = function (textOnly) {
    return {
      hasAICommands: false,
      textBefore: textOnly?.join?.('') || ''
    }
  }
  var peg$f2 = function (chars) {
    // Capture all text until we encounter an {{#ai}} block
    return chars.join('')
  }
  var peg$f3 = function (open, content) {
    return {
      type: 'aiBlock',
      args: open.args,
      content
    }
  }
  var peg$f4 = function (content) {
    // aiContent can contain nested aiBlocks or plain text
    // Return an array of strings and objects
    return content?.join('') || ''
  }
  var peg$f5 = function (chars) {
    return chars.join('')
  }
  var peg$f6 = function (args) {
    return { args: args || [] }
  }
  var peg$f7 = function (arg, rest) {
    return [arg].concat(rest.map((r) => r[1]))
  }
  var peg$f8 = function (chars) {
    return chars.join('')
  }
  var peg$f9 = function (digits) {
    return parseInt(digits.join(''), 10)
  }
  var peg$f10 = function (chars) {
    return chars.join('')
  }
  var peg$currPos = options.peg$currPos | 0
  var peg$savedPos = peg$currPos
  var peg$posDetailsCache = [{ line: 1, column: 1 }]
  var peg$maxFailPos = peg$currPos
  var peg$maxFailExpected = options.peg$maxFailExpected || []
  var peg$silentFails = options.peg$silentFails | 0

  var peg$result

  if (options.startRule) {
    if (!(options.startRule in peg$startRuleFunctions)) {
      throw new Error('Can\'t start parsing from rule "' + options.startRule + '".')
    }

    peg$startRuleFunction = peg$startRuleFunctions[options.startRule]
  }

  function text() {
    return input.substring(peg$savedPos, peg$currPos)
  }

  function offset() {
    return peg$savedPos
  }

  function range() {
    return {
      source: peg$source,
      start: peg$savedPos,
      end: peg$currPos
    }
  }

  function location() {
    return peg$computeLocation(peg$savedPos, peg$currPos)
  }

  function expected(description, location) {
    location = location !== undefined ? location : peg$computeLocation(peg$savedPos, peg$currPos)

    throw peg$buildStructuredError(
      [peg$otherExpectation(description)],
      input.substring(peg$savedPos, peg$currPos),
      location
    )
  }

  function error(message, location) {
    location = location !== undefined ? location : peg$computeLocation(peg$savedPos, peg$currPos)

    throw peg$buildSimpleError(message, location)
  }

  function peg$literalExpectation(text, ignoreCase) {
    return { type: 'literal', text: text, ignoreCase: ignoreCase }
  }

  function peg$classExpectation(parts, inverted, ignoreCase) {
    return { type: 'class', parts: parts, inverted: inverted, ignoreCase: ignoreCase }
  }

  function peg$anyExpectation() {
    return { type: 'any' }
  }

  function peg$endExpectation() {
    return { type: 'end' }
  }

  function peg$otherExpectation(description) {
    return { type: 'other', description: description }
  }

  function peg$computePosDetails(pos) {
    var details = peg$posDetailsCache[pos]
    var p

    if (details) {
      return details
    } else {
      if (pos >= peg$posDetailsCache.length) {
        p = peg$posDetailsCache.length - 1
      } else {
        p = pos
        while (!peg$posDetailsCache[--p]) {}
      }

      details = peg$posDetailsCache[p]
      details = {
        line: details.line,
        column: details.column
      }

      while (p < pos) {
        if (input.charCodeAt(p) === 10) {
          details.line++
          details.column = 1
        } else {
          details.column++
        }

        p++
      }

      peg$posDetailsCache[pos] = details

      return details
    }
  }

  function peg$computeLocation(startPos, endPos, offset) {
    var startPosDetails = peg$computePosDetails(startPos)
    var endPosDetails = peg$computePosDetails(endPos)

    var res = {
      source: peg$source,
      start: {
        offset: startPos,
        line: startPosDetails.line,
        column: startPosDetails.column
      },
      end: {
        offset: endPos,
        line: endPosDetails.line,
        column: endPosDetails.column
      }
    }
    if (offset && peg$source && typeof peg$source.offset === 'function') {
      res.start = peg$source.offset(res.start)
      res.end = peg$source.offset(res.end)
    }
    return res
  }

  function peg$fail(expected) {
    if (peg$currPos < peg$maxFailPos) {
      return
    }

    if (peg$currPos > peg$maxFailPos) {
      peg$maxFailPos = peg$currPos
      peg$maxFailExpected = []
    }

    peg$maxFailExpected.push(expected)
  }

  function peg$buildSimpleError(message, location) {
    return new peg$SyntaxError(message, null, null, location)
  }

  function peg$buildStructuredError(expected, found, location) {
    return new peg$SyntaxError(
      peg$SyntaxError.buildMessage(expected, found),
      expected,
      found,
      location
    )
  }

  function peg$parsestart() {
    var s0, s1, s2, s3, s4

    s0 = peg$currPos
    s1 = peg$parsenonAiText()
    s2 = peg$parseaiBlock()
    if (s2 !== peg$FAILED) {
      s3 = []
      if (input.length > peg$currPos) {
        s4 = input.charAt(peg$currPos)
        peg$currPos++
      } else {
        s4 = peg$FAILED
        if (peg$silentFails === 0) {
          peg$fail(peg$e0)
        }
      }
      while (s4 !== peg$FAILED) {
        s3.push(s4)
        if (input.length > peg$currPos) {
          s4 = input.charAt(peg$currPos)
          peg$currPos++
        } else {
          s4 = peg$FAILED
          if (peg$silentFails === 0) {
            peg$fail(peg$e0)
          }
        }
      }
      peg$savedPos = s0
      s0 = peg$f0(s1, s2, s3)
    } else {
      peg$currPos = s0
      s0 = peg$FAILED
    }
    if (s0 === peg$FAILED) {
      s0 = peg$currPos
      s1 = []
      if (input.length > peg$currPos) {
        s2 = input.charAt(peg$currPos)
        peg$currPos++
      } else {
        s2 = peg$FAILED
        if (peg$silentFails === 0) {
          peg$fail(peg$e0)
        }
      }
      while (s2 !== peg$FAILED) {
        s1.push(s2)
        if (input.length > peg$currPos) {
          s2 = input.charAt(peg$currPos)
          peg$currPos++
        } else {
          s2 = peg$FAILED
          if (peg$silentFails === 0) {
            peg$fail(peg$e0)
          }
        }
      }
      peg$savedPos = s0
      s1 = peg$f1(s1)
      s0 = s1
    }

    return s0
  }

  function peg$parsenonAiText() {
    var s0, s1, s2, s3, s4

    s0 = peg$currPos
    s1 = []
    s2 = peg$currPos
    s3 = peg$currPos
    peg$silentFails++
    s4 = peg$parseopenAiTag()
    peg$silentFails--
    if (s4 === peg$FAILED) {
      s3 = undefined
    } else {
      peg$currPos = s3
      s3 = peg$FAILED
    }
    if (s3 !== peg$FAILED) {
      if (input.length > peg$currPos) {
        s4 = input.charAt(peg$currPos)
        peg$currPos++
      } else {
        s4 = peg$FAILED
        if (peg$silentFails === 0) {
          peg$fail(peg$e0)
        }
      }
      if (s4 !== peg$FAILED) {
        s2 = s4
      } else {
        peg$currPos = s2
        s2 = peg$FAILED
      }
    } else {
      peg$currPos = s2
      s2 = peg$FAILED
    }
    while (s2 !== peg$FAILED) {
      s1.push(s2)
      s2 = peg$currPos
      s3 = peg$currPos
      peg$silentFails++
      s4 = peg$parseopenAiTag()
      peg$silentFails--
      if (s4 === peg$FAILED) {
        s3 = undefined
      } else {
        peg$currPos = s3
        s3 = peg$FAILED
      }
      if (s3 !== peg$FAILED) {
        if (input.length > peg$currPos) {
          s4 = input.charAt(peg$currPos)
          peg$currPos++
        } else {
          s4 = peg$FAILED
          if (peg$silentFails === 0) {
            peg$fail(peg$e0)
          }
        }
        if (s4 !== peg$FAILED) {
          s2 = s4
        } else {
          peg$currPos = s2
          s2 = peg$FAILED
        }
      } else {
        peg$currPos = s2
        s2 = peg$FAILED
      }
    }
    peg$savedPos = s0
    s1 = peg$f2(s1)
    s0 = s1

    return s0
  }

  function peg$parseaiBlock() {
    var s0, s1, s2, s3

    s0 = peg$currPos
    s1 = peg$parseopenAiTag()
    if (s1 !== peg$FAILED) {
      s2 = peg$parseaiContent()
      s3 = peg$parsecloseAiTag()
      if (s3 !== peg$FAILED) {
        peg$savedPos = s0
        s0 = peg$f3(s1, s2)
      } else {
        peg$currPos = s0
        s0 = peg$FAILED
      }
    } else {
      peg$currPos = s0
      s0 = peg$FAILED
    }

    return s0
  }

  function peg$parseaiContent() {
    var s0, s1, s2, s3

    s0 = peg$currPos
    s1 = []
    s2 = peg$currPos
    s3 = peg$parseaiBlock()
    if (s3 !== peg$FAILED) {
      s2 = s3
    } else {
      peg$currPos = s2
      s2 = peg$FAILED
    }
    if (s2 === peg$FAILED) {
      s2 = peg$currPos
      s3 = peg$parsetextChunk()
      if (s3 !== peg$FAILED) {
        s2 = s3
      } else {
        peg$currPos = s2
        s2 = peg$FAILED
      }
    }
    while (s2 !== peg$FAILED) {
      s1.push(s2)
      s2 = peg$currPos
      s3 = peg$parseaiBlock()
      if (s3 !== peg$FAILED) {
        s2 = s3
      } else {
        peg$currPos = s2
        s2 = peg$FAILED
      }
      if (s2 === peg$FAILED) {
        s2 = peg$currPos
        s3 = peg$parsetextChunk()
        if (s3 !== peg$FAILED) {
          s2 = s3
        } else {
          peg$currPos = s2
          s2 = peg$FAILED
        }
      }
    }
    peg$savedPos = s0
    s1 = peg$f4(s1)
    s0 = s1

    return s0
  }

  function peg$parsetextChunk() {
    var s0, s1, s2, s3, s4, s5

    s0 = peg$currPos
    s1 = []
    s2 = peg$currPos
    s3 = peg$currPos
    peg$silentFails++
    s4 = peg$parseopenAiTag()
    peg$silentFails--
    if (s4 === peg$FAILED) {
      s3 = undefined
    } else {
      peg$currPos = s3
      s3 = peg$FAILED
    }
    if (s3 !== peg$FAILED) {
      s4 = peg$currPos
      peg$silentFails++
      s5 = peg$parsecloseAiTag()
      peg$silentFails--
      if (s5 === peg$FAILED) {
        s4 = undefined
      } else {
        peg$currPos = s4
        s4 = peg$FAILED
      }
      if (s4 !== peg$FAILED) {
        if (input.length > peg$currPos) {
          s5 = input.charAt(peg$currPos)
          peg$currPos++
        } else {
          s5 = peg$FAILED
          if (peg$silentFails === 0) {
            peg$fail(peg$e0)
          }
        }
        if (s5 !== peg$FAILED) {
          s2 = s5
        } else {
          peg$currPos = s2
          s2 = peg$FAILED
        }
      } else {
        peg$currPos = s2
        s2 = peg$FAILED
      }
    } else {
      peg$currPos = s2
      s2 = peg$FAILED
    }
    if (s2 !== peg$FAILED) {
      while (s2 !== peg$FAILED) {
        s1.push(s2)
        s2 = peg$currPos
        s3 = peg$currPos
        peg$silentFails++
        s4 = peg$parseopenAiTag()
        peg$silentFails--
        if (s4 === peg$FAILED) {
          s3 = undefined
        } else {
          peg$currPos = s3
          s3 = peg$FAILED
        }
        if (s3 !== peg$FAILED) {
          s4 = peg$currPos
          peg$silentFails++
          s5 = peg$parsecloseAiTag()
          peg$silentFails--
          if (s5 === peg$FAILED) {
            s4 = undefined
          } else {
            peg$currPos = s4
            s4 = peg$FAILED
          }
          if (s4 !== peg$FAILED) {
            if (input.length > peg$currPos) {
              s5 = input.charAt(peg$currPos)
              peg$currPos++
            } else {
              s5 = peg$FAILED
              if (peg$silentFails === 0) {
                peg$fail(peg$e0)
              }
            }
            if (s5 !== peg$FAILED) {
              s2 = s5
            } else {
              peg$currPos = s2
              s2 = peg$FAILED
            }
          } else {
            peg$currPos = s2
            s2 = peg$FAILED
          }
        } else {
          peg$currPos = s2
          s2 = peg$FAILED
        }
      }
    } else {
      s1 = peg$FAILED
    }
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0
      s1 = peg$f5(s1)
    }
    s0 = s1

    return s0
  }

  function peg$parseopenAiTag() {
    var s0, s1, s2, s3

    s0 = peg$currPos
    if (input.substr(peg$currPos, 5) === peg$c0) {
      s1 = peg$c0
      peg$currPos += 5
    } else {
      s1 = peg$FAILED
      if (peg$silentFails === 0) {
        peg$fail(peg$e1)
      }
    }
    if (s1 !== peg$FAILED) {
      s2 = peg$parsearguments()
      if (s2 === peg$FAILED) {
        s2 = null
      }
      if (input.substr(peg$currPos, 2) === peg$c1) {
        s3 = peg$c1
        peg$currPos += 2
      } else {
        s3 = peg$FAILED
        if (peg$silentFails === 0) {
          peg$fail(peg$e2)
        }
      }
      if (s3 !== peg$FAILED) {
        peg$savedPos = s0
        s0 = peg$f6(s2)
      } else {
        peg$currPos = s0
        s0 = peg$FAILED
      }
    } else {
      peg$currPos = s0
      s0 = peg$FAILED
    }

    return s0
  }

  function peg$parsecloseAiTag() {
    var s0

    if (input.substr(peg$currPos, 7) === peg$c2) {
      s0 = peg$c2
      peg$currPos += 7
    } else {
      s0 = peg$FAILED
      if (peg$silentFails === 0) {
        peg$fail(peg$e3)
      }
    }

    return s0
  }

  function peg$parsearguments() {
    var s0, s1, s2, s3, s4, s5, s6

    s0 = peg$currPos
    s1 = peg$parsewsp()
    if (s1 !== peg$FAILED) {
      s2 = peg$parseargument()
      if (s2 !== peg$FAILED) {
        s3 = []
        s4 = peg$currPos
        s5 = peg$parsewsp()
        if (s5 !== peg$FAILED) {
          s6 = peg$parseargument()
          if (s6 !== peg$FAILED) {
            s5 = [s5, s6]
            s4 = s5
          } else {
            peg$currPos = s4
            s4 = peg$FAILED
          }
        } else {
          peg$currPos = s4
          s4 = peg$FAILED
        }
        while (s4 !== peg$FAILED) {
          s3.push(s4)
          s4 = peg$currPos
          s5 = peg$parsewsp()
          if (s5 !== peg$FAILED) {
            s6 = peg$parseargument()
            if (s6 !== peg$FAILED) {
              s5 = [s5, s6]
              s4 = s5
            } else {
              peg$currPos = s4
              s4 = peg$FAILED
            }
          } else {
            peg$currPos = s4
            s4 = peg$FAILED
          }
        }
        peg$savedPos = s0
        s0 = peg$f7(s2, s3)
      } else {
        peg$currPos = s0
        s0 = peg$FAILED
      }
    } else {
      peg$currPos = s0
      s0 = peg$FAILED
    }

    return s0
  }

  function peg$parseargument() {
    var s0

    s0 = peg$parsestringArgument()
    if (s0 === peg$FAILED) {
      s0 = peg$parsenumberArgument()
      if (s0 === peg$FAILED) {
        s0 = peg$parseidentifierArgument()
      }
    }

    return s0
  }

  function peg$parsestringArgument() {
    var s0, s1, s2, s3

    s0 = peg$currPos
    if (input.charCodeAt(peg$currPos) === 34) {
      s1 = peg$c3
      peg$currPos++
    } else {
      s1 = peg$FAILED
      if (peg$silentFails === 0) {
        peg$fail(peg$e4)
      }
    }
    if (s1 !== peg$FAILED) {
      s2 = []
      s3 = input.charAt(peg$currPos)
      if (peg$r0.test(s3)) {
        peg$currPos++
      } else {
        s3 = peg$FAILED
        if (peg$silentFails === 0) {
          peg$fail(peg$e5)
        }
      }
      while (s3 !== peg$FAILED) {
        s2.push(s3)
        s3 = input.charAt(peg$currPos)
        if (peg$r0.test(s3)) {
          peg$currPos++
        } else {
          s3 = peg$FAILED
          if (peg$silentFails === 0) {
            peg$fail(peg$e5)
          }
        }
      }
      if (input.charCodeAt(peg$currPos) === 34) {
        s3 = peg$c3
        peg$currPos++
      } else {
        s3 = peg$FAILED
        if (peg$silentFails === 0) {
          peg$fail(peg$e4)
        }
      }
      if (s3 !== peg$FAILED) {
        peg$savedPos = s0
        s0 = peg$f8(s2)
      } else {
        peg$currPos = s0
        s0 = peg$FAILED
      }
    } else {
      peg$currPos = s0
      s0 = peg$FAILED
    }

    return s0
  }

  function peg$parsenumberArgument() {
    var s0, s1, s2

    s0 = peg$currPos
    s1 = []
    s2 = input.charAt(peg$currPos)
    if (peg$r1.test(s2)) {
      peg$currPos++
    } else {
      s2 = peg$FAILED
      if (peg$silentFails === 0) {
        peg$fail(peg$e6)
      }
    }
    if (s2 !== peg$FAILED) {
      while (s2 !== peg$FAILED) {
        s1.push(s2)
        s2 = input.charAt(peg$currPos)
        if (peg$r1.test(s2)) {
          peg$currPos++
        } else {
          s2 = peg$FAILED
          if (peg$silentFails === 0) {
            peg$fail(peg$e6)
          }
        }
      }
    } else {
      s1 = peg$FAILED
    }
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0
      s1 = peg$f9(s1)
    }
    s0 = s1

    return s0
  }

  function peg$parseidentifierArgument() {
    var s0, s1, s2

    s0 = peg$currPos
    s1 = []
    s2 = input.charAt(peg$currPos)
    if (peg$r2.test(s2)) {
      peg$currPos++
    } else {
      s2 = peg$FAILED
      if (peg$silentFails === 0) {
        peg$fail(peg$e7)
      }
    }
    if (s2 !== peg$FAILED) {
      while (s2 !== peg$FAILED) {
        s1.push(s2)
        s2 = input.charAt(peg$currPos)
        if (peg$r2.test(s2)) {
          peg$currPos++
        } else {
          s2 = peg$FAILED
          if (peg$silentFails === 0) {
            peg$fail(peg$e7)
          }
        }
      }
    } else {
      s1 = peg$FAILED
    }
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0
      s1 = peg$f10(s1)
    }
    s0 = s1

    return s0
  }

  function peg$parsewsp() {
    var s0, s1

    s0 = []
    s1 = input.charAt(peg$currPos)
    if (peg$r3.test(s1)) {
      peg$currPos++
    } else {
      s1 = peg$FAILED
      if (peg$silentFails === 0) {
        peg$fail(peg$e8)
      }
    }
    if (s1 !== peg$FAILED) {
      while (s1 !== peg$FAILED) {
        s0.push(s1)
        s1 = input.charAt(peg$currPos)
        if (peg$r3.test(s1)) {
          peg$currPos++
        } else {
          s1 = peg$FAILED
          if (peg$silentFails === 0) {
            peg$fail(peg$e8)
          }
        }
      }
    } else {
      s0 = peg$FAILED
    }

    return s0
  }

  // Helper functions can be placed here if needed.

  peg$result = peg$startRuleFunction()

  if (options.peg$library) {
    return /** @type {any} */ ({
      peg$result,
      peg$currPos,
      peg$FAILED,
      peg$maxFailExpected,
      peg$maxFailPos
    })
  }
  if (peg$result !== peg$FAILED && peg$currPos === input.length) {
    return peg$result
  } else {
    if (peg$result !== peg$FAILED && peg$currPos < input.length) {
      peg$fail(peg$endExpectation())
    }

    throw peg$buildStructuredError(
      peg$maxFailExpected,
      peg$maxFailPos < input.length ? input.charAt(peg$maxFailPos) : null,
      peg$maxFailPos < input.length
        ? peg$computeLocation(peg$maxFailPos, peg$maxFailPos + 1)
        : peg$computeLocation(peg$maxFailPos, peg$maxFailPos)
    )
  }
}

const peg$allowedStartRules = ['start']

export { peg$allowedStartRules as StartRules, peg$SyntaxError as SyntaxError, peg$parse as parse }
