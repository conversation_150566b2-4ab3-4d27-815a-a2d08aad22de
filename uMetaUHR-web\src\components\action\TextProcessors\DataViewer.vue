<template>
  <div :style="schema?.style?.main" class="data-viewer-container">
    <span v-if="schema?.label" class="label">{{ schema?.label }}</span>
    <json-viewer v-if="!editMode" :value="viewerData || ''" class="json-viewer" />
    <textarea
      v-else
      :value="viewerStringData"
      rows="10"
      @blur="setVal"
      @input="handleInput($event.target.value)"
    />
    <label class="switch">
      <input :value="editMode" type="checkbox" @change="handleToggle" />
      <span class="slider"></span>
    </label>
  </div>
</template>

<script lang="ts" setup>
import { getContext, getNestedValue, setNestedValue } from '@/lib/getContext'
import { JsonViewer } from 'vue3-json-viewer'
import 'vue3-json-viewer/dist/index.css'
import { computed, defineProps, ref } from 'vue'

const editMode = ref(false)

const props = defineProps<{
  schema: {
    type: string
    model: string
    label?: string
  }
  context: { [key: string]: any }
}>()

const viewerData = computed(() => {
  const context = getContext(props)
  const model = props?.schema?.model
  return model ? getNestedValue(context, model) : context
})

const viewerStringData = computed(() => {
  let t = viewerData.value
  return JSON.stringify(t, null, 2) || 'no data'
})

let lastEditedText = ''

function handleInput(value: any) {
  try {
    lastEditedText = value
  } catch (e) {
    console.error(e)
  }
}

function handleToggle() {
  editMode.value = !editMode.value
  if (!editMode.value) {
    let obj = typeof lastEditedText === 'object' ? lastEditedText : JSON.parse(lastEditedText)
    setNestedValue(props.context, props.schema.model, obj)
  } else {
    lastEditedText = getNestedValue(props.context, props.schema.model)
  }
}

function setVal() {
  setNestedValue(props.context, props.schema.model, lastEditedText)
}

defineExpose({ compName: props?.schema?.type || '-' })
</script>

<style lang="scss" scoped>
.data-viewer-container {
  border: 1px solid lightblue;
  max-height: 30vh;
  border-radius: 5px;
  padding: 2px;
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
  overflow: auto;
  position: relative;

  .label {
    font-weight: bold;
    margin-bottom: 5px;
    border: 1px solid lightblue;
  }

  textarea {
    border: 1px solid lightblue;
    border-radius: 4px;
    padding: 5px;
    resize: none;

    //outline: none;
  }

  .input-error {
    border-color: red;
  }

  .switch {
    position: absolute;
    top: 5px;
    right: 5px;
    display: inline-block;
    width: 30px;
    height: 15px;

    input {
      opacity: 0;
      width: 0;
      height: 0;

      &:checked + .slider {
        background-color: lightblue;

        &:before {
          transform: translateX(15px);
        }
      }
    }

    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: 0.4s;
      border-radius: 15px;

      &:before {
        position: absolute;
        content: '';
        height: 13px;
        width: 13px;
        left: 1px;
        bottom: 1px;
        background-color: white;
        transition: 0.4s;
        border-radius: 50%;
      }
    }

    &:hover .slider {
      background-color: #bbb;
    }
  }

  .json-viewer {
    overflow: auto;
  }
}
</style>
