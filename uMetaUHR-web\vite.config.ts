import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import Components from 'unplugin-vue-components/vite';
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers';
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import vueDevTools from 'vite-plugin-vue-devtools'
import { BASE } from './build/constant'
import json5Plugin from 'vite-plugin-json5'

import dotenv from 'dotenv'

const envConfig = dotenv.config()
if (envConfig.error) {
  throw envConfig.error;
}
// https://vitejs.dev/config/
export default defineConfig({
  base:`/${BASE}`,
  plugins: [
    vue(),
    vueJsx(),
    vueDevTools(),
    json5Plugin(), // 添加 JSON5 支持
    Components({   // 添加自动按需引入组件
      resolvers: [
        AntDesignVueResolver({
          importStyle: false, 
        }),
      ],
    })
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      vue: 'vue/dist/vue.esm-browser.js',
    },
  },
  build: {
    rollupOptions: {
      output: {
        // Use [name] to avoid contenthash
        entryFileNames: `[name].js`, // Entry files without hash
        chunkFileNames: `[name].js`, // Chunk files without hash
        assetFileNames: `[name].[ext]` // Assets without hash (e.g., images, CSS)
      }
    }
  },
  server: {
    host: '0.0.0.0',
    port: 8000,
    proxy: {
      [`/${BASE}/api`]: {
        target: `${envConfig.parsed?.PROTOCOL}://${envConfig.parsed?.HOST}:${envConfig.parsed?.PORT}`,
        changeOrigin: true,
        secure: false,
      },
      '/uap-operation-api/v1/config/get': {
        target: 'https://dev.uicloud.com',
        changeOrigin: true,
        secure: false,
      },
    }
  }
})
