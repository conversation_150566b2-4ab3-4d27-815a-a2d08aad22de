CREATE TABLE organizations
(
    -- 核心标识字段
    id                 INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    code               VARCHAR(50) GENERATED ALWAYS AS (attributes ->> 'organization_code') STORED,
    name               VARCHAR(255) NOT NULL DEFAULT '',
    abbreviation       VARCHAR(50)           DEFAULT NULL,

    -- 层级管理字段
    parent_id          INTEGER REFERENCES organizations (id),

    -- 类型与状态
    type               VARCHAR(20)  NOT NULL DEFAULT 'HOSPITAL' CHECK (type IN ('GROUP', 'HOSPITAL', 'CAMPUS', 'WARD',
                                                                                'DEPARTMENT', 'CLINICAL_TEAM')),
    is_active          BOOLEAN               DEFAULT TRUE,
    effective_date     INTEGER      NOT NULL DEFAULT EXTRACT(EPOCH FROM CURRENT_DATE),
    expiration_date    INTEGER               DEFAULT NULL,

    -- 权限控制
    function_points    JSONB        NOT NULL DEFAULT '[]'::JSONB CHECK (jsonb_typeof(function_points) = 'array'),

    -- 资源关联
    facility_resources JSONB        NOT NULL DEFAULT '[]'::JSONB CHECK (jsonb_typeof(facility_resources) = 'array'),

    -- 扩展属性
    attributes         JSONB        NOT NULL DEFAULT '{}'::JSONB CHECK (jsonb_typeof(attributes) = 'object'),

    -- 审计字段
    created_by         VARCHAR(20)  NOT NULL DEFAULT 'system',
    created_at         INTEGER      NOT NULL DEFAULT EXTRACT(EPOCH FROM NOW()),
    modified_by        VARCHAR(20)           DEFAULT NULL,
    modified_at        INTEGER,
    version            INT                   DEFAULT 0
);

COMMENT ON TABLE organizations IS '组织机构主表，存储医院、科室、病区等层级结构信息';
COMMENT ON COLUMN organizations.id IS '自增主键ID';
COMMENT ON COLUMN organizations.code IS '机构编码，从attributes.organization_code生成';
COMMENT ON COLUMN organizations.name IS '机构全称';
COMMENT ON COLUMN organizations.abbreviation IS '机构简称';
COMMENT ON COLUMN organizations.parent_id IS '父机构ID';
COMMENT ON COLUMN organizations.type IS '机构类型(GROUP/HOSPITAL/CAMPUS/WARD/DEPARTMENT/CLINICAL_TEAM)';
COMMENT ON COLUMN organizations.is_active IS '是否启用';
COMMENT ON COLUMN organizations.effective_date IS '生效日期(Unix时间戳)';
COMMENT ON COLUMN organizations.expiration_date IS '过期日期(Unix时间戳)';
COMMENT ON COLUMN organizations.function_points IS '权限配置(JSON格式)';
COMMENT ON COLUMN organizations.facility_resources IS '关联的设施资源(JSON数组)';
COMMENT ON COLUMN organizations.attributes IS '扩展属性(JSON格式)';
COMMENT ON COLUMN organizations.created_by IS '创建人';
COMMENT ON COLUMN organizations.created_at IS '创建时间(Unix时间戳)';
COMMENT ON COLUMN organizations.modified_by IS '修改人';
COMMENT ON COLUMN organizations.modified_at IS '修改时间(Unix时间戳)';
COMMENT ON COLUMN organizations.version IS '版本号(乐观锁)';

-- 索引配置
CREATE INDEX idx_organizations_parent ON organizations (parent_id);
CREATE INDEX idx_organizations_type ON organizations (type);
CREATE INDEX idx_organizations_auth_services ON organizations USING GIN (function_points jsonb_path_ops);
CREATE INDEX idx_organizations_facility_types ON organizations USING GIN (facility_resources jsonb_path_ops);
