drop table if exists providers cascade;

CREATE TABLE providers
(
    -- 核心标识
    id               INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    prov_id          VARCHAR(18)  NOT NULL UNIQUE,        -- 提供者唯一业务ID (SER.1)
    prov_name        VARCHAR(100) NOT NULL,               -- 提供者姓名 (SER.2)
    provider_type_c  INT          NOT NULL,               -- 提供者类型类别 (SER 1040)
    external_name    VARCHAR(100),                        -- 外部显示名称
    staff_resource_c INT          NOT NULL,               -- 人员/资源/类别标识 (SER 30)

    -- 专业信息
    specialty_c      INT,                                 -- 专业类别
    cur_cred_c       INT,                                 -- 当前证书显示 (SER 34000)
    license_number   VARCHAR(50),                         -- 执照号码

    -- 关联信息
    user_id          VARCHAR(18),                         -- 关联的用户ID
    department_id    VARCHAR(18),                         -- 主要部门ID

    -- 状态信息
    is_active        BOOLEAN      NOT NULL DEFAULT TRUE,  -- 是否活跃
    is_resident      BOOLEAN      NOT NULL DEFAULT FALSE, -- 是否为住院医师 (SER 1120)

    -- 时间信息
    created_at       INT          NOT NULL,               -- 创建时间(秒数)
    updated_at       INT          NOT NULL,               -- 更新时间(秒数)
    deleted_at       INT                                  -- 删除时间(秒数), NULL表示未删除
);
COMMENT ON TABLE providers IS '医疗服务提供者表，包括医生、护士、技师等所有临床服务提供者';

-- 列注释
COMMENT ON COLUMN providers.id IS '自增主键';
COMMENT ON COLUMN providers.prov_id IS '提供者唯一业务ID (SER.1)';
COMMENT ON COLUMN providers.prov_name IS '提供者姓名 (SER.2)';
COMMENT ON COLUMN providers.provider_type_c IS '提供者类型类别 (SER 1040)';
COMMENT ON COLUMN providers.external_name IS '外部显示名称';
COMMENT ON COLUMN providers.staff_resource_c IS '人员/资源/类别标识 (SER 30)';
COMMENT ON COLUMN providers.specialty_c IS '专业类别';
COMMENT ON COLUMN providers.cur_cred_c IS '当前证书显示 (SER 34000)';
COMMENT ON COLUMN providers.license_number IS '执照号码';
COMMENT ON COLUMN providers.user_id IS '关联的用户ID';
COMMENT ON COLUMN providers.department_id IS '主要部门ID';
COMMENT ON COLUMN providers.is_active IS '是否活跃';
COMMENT ON COLUMN providers.is_resident IS '是否为住院医师 (SER 1120)';
COMMENT ON COLUMN providers.created_at IS '创建时间(秒数)';
COMMENT ON COLUMN providers.updated_at IS '更新时间(秒数)';
COMMENT ON COLUMN providers.deleted_at IS '删除时间(秒数), NULL表示未删除';
