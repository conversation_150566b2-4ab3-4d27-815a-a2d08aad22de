<template>
  <section
    v-show="isVisible"
    :style="schema?.style?.main"
    :class="[
      'section',
      ...(schema?.themes || []),
      schema?.style?.vars?.themeBorderColor ? 'color-bar' : ''
    ]"
  >
    <!-- Title Section -->
    <div
      v-if="schema?.title"
      :aria-expanded="isOpen"
      :class="{ title: 1, 'non-collapsible': !schema.collapsible }"
      role="button"
      @click="handleClick"
      :style="schema?.style?.title"
    >
      <img
        v-if="schema?.selfIcon"
        :src="`${BASE}${schema.selfIcon}`"
        class="section-icon"
        :style="schema?.style?.selfIcon"
      />
      <span class="title-text">{{ schema?.title }}</span>
      <!-- Show arrow only if schema.collapsible is true -->
      <span
        v-if="schema.collapsible"
        :class="{ 'arrow-rotated': isOpen }"
        :title="toggleLabel"
        aria-label="Toggle Section"
        class="arrow"
      >
        ›
      </span>
    </div>

    <!-- Collapsible Body -->
    <transition name="expand">
      <div v-show="isOpen" :style="schema?.style?.body" class="body">
        <component
          :is="loadComponent(child.type)"
          v-for="(child, index) in schema.children"
          :key="index"
          :context="getContext(props)"
          :schema="child"
        />
      </div>
    </transition>
  </section>
</template>

<script lang="ts" setup>
import { computed, defineProps, onMounted, ref, watch } from 'vue'
import useSubContext from '../mixins/subContextMixin'
import { loadComponent } from '../componentRegistry'
import { evaluateShowWhen, getContext } from '@/lib/getContext'
import { cacheManager } from '@/lib/CacheManager'

defineOptions({
  name: 'BaseSection'
})

// Props definition
const props = defineProps<{
  schema: {
    type: string
    selfIcon: string
    children: Array<any>
    openStatus?: boolean
    collapsible?: boolean // Determines if the section is collapsible
    style?: {
      main?: Record<string, any>
      body?: Record<string, any>
      title?: Record<string, any>
      selfIcon?: Record<string, any>
      vars?: {
        themeBorderColor?: string
      }
    }
    themes?: string[]
    title?: string
    myFavoriteId?: string
  }
  context: Record<string, any>
  onToggle?: (isOpen: boolean) => void
}>()
const BASE = import.meta.env.BASE_URL || ''
// State and Context
const { context: subContext, processSchema } = useSubContext()
subContext.value = { ...props.context }
processSchema(props.schema, subContext.value)

const isVisible = computed(() => evaluateShowWhen(props))

// Open/close state
const isOpen = ref(props.schema?.collapsible ? (props.schema.openStatus ?? true) : true)

// Watch openStatus and update isOpen when collapsible
watch(
  () => props.schema.openStatus,
  (newStatus) => {
    if (props.schema.collapsible && newStatus !== undefined) {
      isOpen.value = newStatus
    }
  }
)

const toggleLabel = computed(() => (isOpen.value ? 'Collapse Section' : 'Expand Section'))

// Methods
function handleClick() {
  if (props.schema.collapsible) {
    toggleSection()
  }
}

async function toggleSection() {
  isOpen.value = !isOpen.value
  props.onToggle?.(isOpen.value)

  if (props.schema.myFavoriteId) {
    const myFavoriteId = props.schema.myFavoriteId
    const favData = (await cacheManager.get(myFavoriteId)) || {}
    favData.openStatus = isOpen.value
    await cacheManager.set(myFavoriteId, favData)
  }
}

// Initialize open state during setup to avoid flashes
onMounted(async () => {
  if (props.schema.collapsible && props.schema.myFavoriteId) {
    const myFavoriteId = props.schema.myFavoriteId
    const favData = (await cacheManager.get(myFavoriteId)) || {}
    isOpen.value = favData.openStatus ?? isOpen.value
  }
})
defineExpose({ compName: props?.schema?.type || '-' })
</script>

<style lang="scss" scoped>
.section {
  --theme-border-color: v-bind('schema?.style?.vars?.themeBorderColor || "#EEE"');

  display: flex;
  flex-direction: column;
  border: 1px solid #dce4ec;
  border-radius: 5px;
  padding: 2px;
  margin: 8px 5px;
  flex: 1 1 auto;
  overflow: auto; //must have, will affect the items inside section. e.g., ckeditor will show ... toolbar under this setting.

  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.2rem 1rem 0.2rem 0.5rem;
    background: #f7f9fc;
    cursor: pointer;
    transition: background-color 0.3s ease;
    gap: 10px;
    overflow: hidden;
    font-size: 16px;

    * {
      user-select: none;
    }

    &:hover {
      background-color: #e8eff7;
    }

    &.non-collapsible {
      cursor: default;

      &:hover {
        background-color: #f7f9fc;
      }
    }

    .title-text {
      font-weight: bold;
      flex: 1;
    }

    .section-icon {
      width: 20px;
      height: 20px;
    }

    .arrow {
      font-size: 1.2rem;
      transition: transform 0.3s ease;
      display: inline-block;
    }

    .arrow-rotated {
      transform: rotate(90deg);
    }
  }

  .body {
    padding: 5px;
    background: #fff;
    border-top: 1px solid #dce4ec;
    overflow: auto; //relay the overflow effect
  }
}

.section.color-bar {
  border: none;
  border-left: 5px solid var(--theme-border-color);
  padding: 0;

  .title {
    color: var(--theme-border-color);
    border-radius: 0 30px 30px 0;
    background: color-mix(in srgb, var(--theme-border-color) 10%, white);
  }

  .body {
    border: none;
  }
}
</style>
