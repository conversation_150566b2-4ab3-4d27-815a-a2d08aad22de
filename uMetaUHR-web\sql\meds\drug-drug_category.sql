CREATE TABLE drug_category_mapping
(
    id          INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    drug_id     INT NOT NULL,
    category_id INT NOT NULL,
    created_at  BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
    updated_at  BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
    FOREIGN KEY (drug_id) REFERENCES drug (id),
    FOREIGN KEY (category_id) REFERENCES drug_category (id),
    UNIQUE (drug_id, category_id) -- Ensure unique mapping
);

COMMENT ON TABLE drug_category_mapping IS 'Mapping between drugs and their categories';
COMMENT ON COLUMN drug_category_mapping.drug_id IS 'Reference to drug table';
COMMENT ON COLUMN drug_category_mapping.category_id IS 'Reference to drug category table';
COMMENT ON COLUMN drug_category_mapping.created_at IS 'Timestamp when mapping was created';
