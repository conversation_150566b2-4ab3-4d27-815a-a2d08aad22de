<template>
  <div class="calendar">
    <!-- 视图切换及导航 -->
    <div class="calendar-controls">
      <component
        :is="loadComponent(schema.view)"
        :context="getContext(props)"
        :schema="schema.view"
      />

      <button @click="changeDate(-1)">←</button>
      <span class="current-range">{{ currentRangeLabel }}</span>
      <button @click="changeDate(1)">→</button>
      <button @click="gotoToday()">今天</button>
    </div>

    <component
      :is="componentRegistry[getContext(props)?.calendarView?.[0]]"
      :active-event-id="props.activeEventId"
      :context="getContext(props)"
      :current-date="currentDate"
      :schema="props.schema?.child"
      @event-drop="handleEventDrop"
      @add-event="handleAddEvent"
      @edit-event="handleEditEvent"
      @delete-event="handleDeleteEvent"
      @reload-events="loadEvents"
    />
    <WnDialog ref="dialog" />
  </div>
</template>

<script lang="ts" setup>
import { computed, defineAsyncComponent, defineProps, inject, onMounted, ref, watch } from 'vue'
import { DateTime } from 'luxon'
import WnDialog from '@/components/containers/WnDialog.vue'
import type { CalendarEvent } from '@/components/action/views/CalendarType'
import { getContext } from '@/lib/getContext'
import { isEmpty } from '@/lib/util'
import { loadComponent } from '@/components/componentRegistry'

const props = defineProps<{
  schema?: any
  context?: { [key: string]: any }
  activeEventId?: string
}>()

const views = ['day', 'week', 'month', 'year'] as const
type ViewMode = (typeof views)[number]
const currentView = ref<ViewMode>('week')
const currentDate = ref(DateTime.now())
const dialog = ref<InstanceType<typeof WnDialog>>()

// 依赖注入事件管理方法
const getCalendarEvents = inject('get-calendar-events', async (param: any) => [])
const addCalendarEvent = inject<(event: CalendarEvent) => Promise<void>>(
  'add-calendar-event',
  async () => {}
)
const editCalendarEvent = inject<(event: CalendarEvent) => Promise<void>>(
  'edit-calendar-event',
  async () => {}
)
const deleteCalendarEvent = inject<(event: CalendarEvent) => Promise<void>>(
  'delete-calendar-event',
  async () => {}
)

// 当前显示范围标签
const currentRangeLabel = computed(() => {
  switch (currentView.value) {
    case 'month':
      return currentDate.value.toFormat('yyyy年MM月')
    case 'week':
      return `${currentDate.value.toFormat('yyyy年')}第${currentDate.value.weekNumber}周`
    case 'year':
      return currentDate.value.toFormat('yyyy年')
    default:
      return currentDate.value.toFormat('yyyy-MM-dd')
  }
})

const componentRegistry: { [key: string]: any } = {
  week: defineAsyncComponent(() => import('./views/weekView.vue')),
  day: defineAsyncComponent(() => import('./views/dayView.vue')),
  month: defineAsyncComponent(() => import('./views/monthView.vue'))
}

// 导航控制
async function changeDate(step: number) {
  const unitMap: Record<ViewMode, string> = {
    year: 'years',
    month: 'months',
    week: 'weeks',
    day: 'days'
  }
  currentDate.value = currentDate.value.plus({ [unitMap[currentView.value]]: step })
  await loadEvents()
}

async function gotoToday() {
  currentDate.value = DateTime.now()
  await loadEvents()
}

// Event loading based on current view and date
async function loadEvents() {
  try {
    const startOfPeriod = currentDate.value.startOf(currentView.value)
    const endOfPeriod = currentDate.value.endOf(currentView.value)

    let context = getContext(props)
    context.events = await getCalendarEvents({
      startTime: startOfPeriod.toSeconds(),
      endTime: endOfPeriod.toSeconds(),
      eventType: '' // Adjust if needed
    })
  } catch (error) {
    console.error('Failed to load calendar events:', error)
  }
}

// Load events on view or date change
watch(currentView, loadEvents)
onMounted(loadEvents)

// Event handlers with reload after changes
async function handleAddEvent(date: DateTime) {
  const newEvent: CalendarEvent = {
    title: '新事件',
    startTime: date.toSeconds(),
    duration: 3600
  }
  await addCalendarEvent(newEvent)
}

async function handleEditEvent(event: CalendarEvent) {
  await editCalendarEvent(event)
}

async function handleDeleteEvent(event: CalendarEvent) {
  await deleteCalendarEvent(event)
}

const saveEvent = inject('save-calendar-event-2', async (a: any) => 1)

async function handleEventDrop(event: CalendarEvent, newStart: DateTime | null) {
  if (!newStart) return
  if (!saveEvent) return

  let { events = [] } = getContext(props) || {}
  let e = events.find((e: CalendarEvent) => e.id === event.id)
  if (isEmpty(e)) return

  e.startTime = newStart?.toSeconds()
  await saveEvent(e)
}
</script>

<style lang="scss" scoped>
.calendar {
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
  overflow: auto;
}

.calendar-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f5f5f5;
}

.current-range {
  min-width: 150px;
  text-align: center;
}
</style>
