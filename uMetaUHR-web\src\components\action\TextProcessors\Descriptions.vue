<template>
  <a-descriptions 
    v-bind="props.schema?.attrs" 
    :style="props.schema?.style"
  >
    <a-descriptions-item 
      v-for="item in getNestedValue(context,props.schema.model) || props.schema?.children" 
      :label="item.label"
      :label-style="{
        display: 'flex',
        justifyContent: 'flex-end',
        width: '80px',
        color: '#939393',
        ...item.labelStyle
      }"
      v-bind="item?.attrs"
    >
      <template v-if="item.type">
        <component
          :is="loadComponent(item.type)"
          :context="getContext(props)"
          :schema="item"
        />
      </template>
      <template v-else>
        {{ item.value || getNestedValue(context, item.model) }}
      </template>
    </a-descriptions-item>
  </a-descriptions>
</template>

<script lang="ts" setup>
import { defineProps } from 'vue'
import { getContext, getNestedValue } from '@/lib/getContext'
import { loadComponent } from '@/components/componentRegistry'

interface DescriptionItem {
  key: string
  label: string
  model: string
  attrs?: Record<string, any>
  labelStyle?: Record<string, any>
  type: string
}

const props = defineProps<{
  schema: {
    attrs?: Record<string, any>
    style?: Record<string, any>
    children: DescriptionItem[],
    model: string
  },
  context?: { [key: string]: any }
}>()
</script>

<style lang="scss" scoped>

</style>
