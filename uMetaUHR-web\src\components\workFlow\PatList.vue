<template>
  <div class="todo">
    <component :is="loadComponent(props.schema?.ui?.type)" v-if="props.schema" :context="getContext(props)"
      :schema="props.schema?.ui" />
  </div>
  <WnDialog ref="dialog" />
</template>

<script lang="ts" setup>
import { defineProps, inject, provide, ref, onMounted, onUnmounted } from 'vue'
import { loadComponent } from '../componentRegistry'
import { getContext, getNestedValue } from '@/lib/getContext'
import WnDialog from '@/components/containers/WnDialog.vue'
import Apis from '@/service'
import { setNestedValue } from '@/lib/getContext'
import { PeriodsEnum, VisitStatusEnum, VisitStatusMarkColorEnum } from '@/enums'
import type { PatientBaseInfo, PatientExtensionInfo, PatListParams, FamilyMemberHistory, PatientListResponse } from '@/types'
import dayjs from 'dayjs';
import { age } from '@/lib/workflow-utils'
import type { PatientRecord } from '@/types/pat-list'

const props = defineProps<{
  schema: any
  context: { [key: string]: any }
}>()

const dialog = ref<InstanceType<typeof WnDialog>>();
const pageNum = ref(1);

/**
 * 分页查询参数
 * @param pageSize 
 */
const setPageSize = (pageSize: number) => {
  setNestedValue(props.context, 'list.conditions.pageSize', pageSize)
}

/**
 * 初始化列表查询条件
 */
const defaultConditions = () => {
  setNestedValue(props.context, 'list', {
    conditions: {
      keyword: "",
      dateRange: [dayjs().add(-3000, 'd'), dayjs()],
      visitStatus: "all",
      visitPeriod: ['AM', 'PM']
    },
    visitStatusLabelSuffix: {
      all: '(0)',
      pending: '(0)',
      missed: '(0)',
      inProgress: '(0)',
      completed: '(0)'
    }
  })
}

/**
 * 点击患者列表项初始化详情患者信息

 * @param patientInfo 
 */
const setDetailPatientInfo = (patientInfo: PatientBaseInfo, registerInfo: any) => {
  const { address, insurance_info } = patientInfo;
  const insuranceInfo = {
    selfFunded: false,
    insuranceUser: false
  }
  if (insurance_info.type === '商保') {
    insuranceInfo.selfFunded = true
  } else {
    insuranceInfo.insuranceUser = true
  }
  setNestedValue(props.context, 'detail', {
    patientInfo: {
      ...patientInfo,
      registerInfo,
      patient_age: age(new Date(patientInfo.birth_date).getTime() / 1000),
      address: `${address.province}${address.city}${address.district}${address.detail}`,
      insuranceInfo
    }
  })
}

/**
 * 点击患者列表时初始化患者扩展信息
 * @param extensionInfo 
 */
const setPatientExtensionInfo = (extensionInfo: PatientExtensionInfo) => {
  const { family_history, medical_info: { allergies } } = extensionInfo;
  const familyHistory = JSON.parse(family_history)
  const familyHistoryList = familyHistory.map((item: FamilyMemberHistory) => ({
    label: item.relation,
    value: item.disease.join('、')
  }))
  setNestedValue(props.context, 'detail.patientInfo.familyHistory', familyHistoryList)
  setNestedValue(props.context, 'detail.patientInfo.allergies', allergies)
}

/**
 * 查询患者列表总数
 * @param conditions 
 */
const fetchPatientCount = async (conditions: PatListParams) => {
  const res = await Apis.getPaCount(conditions)
  const visitStatusLabelSuffix = {
    all: `(${res.data.totalCount})`,
    pending: `(${res.data.pendingCount})`,
    missed: `(${res.data.missedCount})`,
    inProgress: `(${res.data.inProgressCount})`,
    completed: `(${res.data.completedCount})`
  }
  setNestedValue(props.context, 'list.count', res.data.totalCount)
  setNestedValue(props.context, 'list.visitStatusLabelSuffix', visitStatusLabelSuffix)
}

const appendPatientList = (res: PatientListResponse, patientList: any[]) => {
  for (const item of res.data.data) {
    const customVisitStatus = `
        <div style="height:100%;display:flex;align-items:center">
          <div style="background-color:${VisitStatusMarkColorEnum[item.visit_status]};width:8px;height:8px;border-radius:4px;margin-right:8px"></div>
          ${VisitStatusEnum[item.visit_status]}
        </div>`
    patientList.push({
      ...item,
      patient_age: age(new Date(item.birth_date).getTime() / 1000),
      visit_status_template: customVisitStatus,
      visit_status: item.visit_status,
      visit_period: PeriodsEnum[item.visit_period],
    })
  }
}

const updatePatientStatus = (res: PatientListResponse, patientList: PatientRecord[]) => {
  const idMap: Record<string, PatientRecord> = {}
  for (const item of res.data.data) {
    idMap[item.queue_number] = item
  }

  for (const item of patientList) {
    if (item.queue_number in idMap) {
      const customVisitStatus = `
        <div style="height:100%;display:flex;align-items:center">
          <div style="background-color:${VisitStatusMarkColorEnum[idMap[item.queue_number].visit_status]};width:8px;height:8px;border-radius:4px;margin-right:8px"></div>
          ${VisitStatusEnum[idMap[item.queue_number].visit_status]}
        </div>`
      item.visit_status_template = customVisitStatus;
      item.visit_status = idMap[item.queue_number].visit_status
    }
  }
}

function setEmptyText() {
  let text = '今日暂无挂号患者';
  let context = getContext(props)
  let {
    list: {
      conditions: {
        keyword,
        dateRange,
        visitStatus = 'all',
        visitPeriod = ['AM', 'PM']
      } = {}
    } = {}
  } = context;
  if (keyword || 
    visitStatus != 'all' || 
    visitPeriod.length != 2 || 
    dateRange[0].format('YYYY-MM-DD') != dayjs().format('YYYY-MM-DD') ||
    dateRange[1].format('YYYY-MM-DD') != dayjs().format('YYYY-MM-DD')
  ) {
    text = '未找到匹配患者';
  }
  setNestedValue(props.context, 'list.emptyText', text);
}
    

/**
 * 查询患者列表
 * 1. 刷新（包含定时刷新）情况下，只更新状态，通过params中增加查询pageSize和num来控制查询数量，然后只更新当前列表的就诊状态
 * 2. 修改查询条件、加载更多 需要extend数据，通过传递currentPatientList来扩展
 * @param param 
 */
const fetchPatientList = async (param: any = {}, currentPatientList: any[] = [], append: boolean = true) => {
  let context = getContext(props)
  let {
    list: {
      conditions: {
        keyword = '',
        dateRange = [dayjs(), dayjs()],
        visitStatus = 'all',
        visitPeriod = ['AM', 'PM'],
        pageSize = 20
      } = {}
    } = {}
  } = context;
  let pageNumber = pageNum.value;
  if (param && param.pageNum) {
    pageNumber = param.pageNum;
  }
  if (param && param.pageSize) {
    pageSize = param.pageSize;
  }
  const conditions: PatListParams = {
    pageNum: pageNumber,
    pageSize,
    periods: visitPeriod
  }
  if (keyword) conditions.keyword = keyword;
  if (dateRange) {
    conditions.startDate = dateRange[0].format('YYYY-MM-DD');
    conditions.endDate = dateRange[1].format('YYYY-MM-DD');
  }
  if (visitStatus !== 'all') {
    conditions.visitStatus = visitStatus;
  }
  const res = await Apis.getPaList(conditions)
  const patientList = currentPatientList;
  if (append) {
    appendPatientList(res, patientList)
  } else {
    updatePatientStatus(res, patientList);
  }
  if (!patientList.length) {
    setEmptyText();
  }
  fetchPatientCount(conditions);
  setNestedValue(props.context, 'list.data', patientList);
}

provide('list-patients', () => startAutoRefresh())  // 列表查询患者，使用自动刷新机制

provide('load-more-patient-list', (param: any = {}) => {  // 列表加载更多患者
  pageNum.value++;
  startAutoRefresh({}, getNestedValue(props.context, 'list.data'));
})

provide('handle-patient-name', (param: any = {}) => {
  let context = getContext(props)
})

provide('select-patient', async (param: any = {}) => {  // 选择患者
  const { data: { patient_id } } = param
  Apis.getPatientBasicInfo({ patientId: patient_id }).then(res=>{
    setDetailPatientInfo(res.data, param.data);
  })
  Apis.getPatientExtensionInfo({ patientId: patient_id }).then(extensionInfoRes=>{
    setPatientExtensionInfo(extensionInfoRes.data)
  })
})

const selectNextPatient = inject('open-next-patient', async (param: any) => [])
provide('next-patient', async (param: any = {}) => {
  let context = getContext(props)
  await selectNextPatient({ curPatId: context?.patData?.id })
})

provide('refresh-patient-list', () => {
  setNestedValue(props.context, 'list.lastRefreshTime', '刷新中...')
  const data = getNestedValue(props.context, 'list.data') || [];
  startAutoRefresh({                              // 手动刷新，限制已加载的更新数据状态
    pageNum: 1,
    pageSize: data.length
  }, data, false);
})

provide('set-page-size', setPageSize);    // 设置默认分页大小

provide('change-conditions', () => {        // 改变查询条件
  pageNum.value = 1;
});

provide('init-conditions', defaultConditions)  // 初始化查询条件

provide('call-patient', (params: any) => {
  const { context: { tableRowData: { patient_id: patientId } } } = params;
  Apis.callPatient({ patientId })
})

const refreshInterval = ref<NodeJS.Timeout>();
const retryCount = ref(0);
const maxRetries = 3;
const retryDelay = 10000; // 10秒

const refreshFetchData = async () => {
  try {
    const currentData = getNestedValue(props.context, 'list.data') || [];
    await fetchPatientList(
      {
        pageNum: 1,
        pageSize: currentData.length
      },
      currentData,
      false
    );
    return true;
  } catch (error) {
    return false
  }

}

const startAutoRefresh = async (params: any = {}, currentPatientList = [], append: boolean = true) => {
  // 清除现有定时器
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value);
  }

  // 立即执行一次数据获取
  try {
    await fetchPatientList(params, currentPatientList, append);
    setNestedValue(props.context, 'list.lastRefreshTime', dayjs().format("YYYY-MM-DD HH:mm:ss"));
  } catch (error) {
    console.error('首次刷新患者列表失败:', error);
    setNestedValue(props.context, 'list.lastRefreshTime', '加载失败');
  }

  // 设置新的定时器
  refreshInterval.value = setInterval(async () => {
    if (retryCount.value) return;
    const fetchSuccess = await refreshFetchData();
    if (fetchSuccess) {
      retryCount.value = 0;
      setNestedValue(props.context, 'list.lastRefreshTime', dayjs().format("YYYY-MM-DD HH:mm:ss"));
    } else {
      setNestedValue(props.context, 'list.lastRefreshTime', '加载失败');
      retryCount.value += 1;
      while (retryCount.value < maxRetries) {
        const success = await refreshFetchData();
        if (success) {
          retryCount.value = 0;
          break;
        };
        retryCount.value += 1;
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }
      if (retryCount.value >= maxRetries) {
        clearInterval(refreshInterval.value);
        console.log('已达到最大重试次数，停止自动刷新');
      }
    }
  }, (props.schema.refreshInterval || 30) * 1000); // 30秒刷新间隔
};

onMounted(() => {
  defaultConditions();
});

onUnmounted(() => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value);
  }
});

defineExpose({ compName: 'patList' })
</script>

<style lang="scss" scoped>
.todo {
  display: flex;
  flex: 1 1 auto;
  min-height: 300px;
}
</style>
