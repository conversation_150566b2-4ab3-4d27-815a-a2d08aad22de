<template>
  <div class="todo">
    <component
      :is="loadComponent(props.schema?.ui?.type)"
      v-if="props.schema"
      :context="getContext(props)"
      :schema="props.schema?.ui"
    />
  </div>
  <WnDialog ref="dialog" />
</template>

<script lang="ts" setup>
import { defineProps, inject, onMounted, provide, ref } from 'vue'
import { loadComponent } from '../componentRegistry'
import { getContext } from '@/lib/getContext'
import WnDialog from '@/components/containers/WnDialog.vue'
import { dbSvc } from '@/service/base/dbSvc'
import { Urls } from '@/service/base/urls'
import { DateTime } from 'luxon'

const props = defineProps<{
  schema: any
  context: { [key: string]: any }
}>()

const dialog = ref<InstanceType<typeof WnDialog>>()

const xCountPatients = async () => {
  let context = getContext(props)
  const [{ count = 0 }] = await dbSvc(Urls.xQuery, 'xCountPatients', {})
  context.totalPatients = count
  return count
}
onMounted(async () => {
  await xCountPatients()
})

provide('count-patients', xCountPatients)

const xListPatients = async (param = {} as any) => {
  const {
    data: { queryTemplate = 'xEncListAll' } = {},
    queryParam: { limit = 200, offset = 0 } = {}
  } = param
  if (!queryTemplate) return
  let context = getContext(props)
  const data = await dbSvc(Urls.xQuery, queryTemplate, { limit, offset })
  context.patList = data
  context.lastRefreshTime = DateTime.now().toFormat('M/dd hh:mm:ss')
  return data
}
provide('list-patients', xListPatients)

provide('change-patient-list', xListPatients)

provide('select-patient', async (param = {}) => {
  let context = getContext(props)
  let { data: { id = '' } = {} } = param as any
  if (!id) return

  const [patData] = (await dbSvc(Urls.xQuery, 'xGetPatientInfo2', { id })) || []
  if (!patData) {
    console.error('Patient not found with id:', id)
    return
  }

  context.patData = patData
  context.detail.detail = {} //refresh the encounter toc-detail
  context.detail.patient = patData
  return {}
})

const selectNextPatient = inject('open-next-patient', async (param: any) => [])
provide('next-patient', async (param: any = {}) => {
  let context = getContext(props)
  await selectNextPatient({ curPatId: context?.patData?.id })
})

defineExpose({ compName: 'patList' })
</script>

<style lang="scss" scoped>
.todo {
  display: flex;
  flex: 1 1 auto;
  min-height: 300px;
}
</style>
