<template>
  <div class="diagnosis-entry">
    <component
      :is="loadComponent(props.schema?.ui?.type)"
      v-if="props.schema"
      :context="getContext(props)"
      :schema="props.schema?.ui"
    />
  </div>
  <WnDialog ref="dialog" />
</template>

<script lang="ts" setup>
import { defineProps, provide, ref, watch, onMounted, nextTick } from 'vue'
import { loadComponent } from '../componentRegistry'
import { getContext } from '@/lib/getContext'
import WnDialog from '@/components/containers/WnDialog.vue'
import Apis from '@/service'
import eventBus from '@/components/mixins/eventBus'
import { cloneDeep } from 'lodash-es'

const props = defineProps<{
  schema: any
  context: { [key: string]: any }
}>()

const dialog = ref<InstanceType<typeof WnDialog>>()
const currentPatientId = ref<number>()

provide('query-condition', async (query: any = {}) => {
  const res = await Apis.searchDiagnosis({
    "keyword": query
  })
  return res.data;
})

provide('list-records', async () => {
  if (!currentPatientId.value) {
    return []
  }
  const res = await Apis.listDiagnosis({
    "patient_id": currentPatientId.value
  })
  return res.data;
})

provide('save-record', async () => {
  const context = getContext(props)
  if (!context) return
  // 搜索框的返回name要赋值到diagnosis_name，与弹框的textDisplay一致
  context.diagnosisRecord.diagnosis_name = context.diagnosisRecord.name
  let diagnosisContext = {
    diagnosisRecord: cloneDeep(context.diagnosisRecord)
  }
  let schema = props.schema.schema['edit-record-modal']
  const recordEntry = await getCompInstance('record-entry')
  const result = await dialog.value.dialog(
    diagnosisContext,
    schema
  );
  let saveFlag = true
  if (result) {
    // 用户点击了保存
    let { diagnosisRecord } = diagnosisContext
    if (!currentPatientId.value) {
      return true
    }
    const diagnosisData = {
      patient_id: currentPatientId.value,
      diagnosis_id: diagnosisRecord.id,
      diagnosis_name: diagnosisRecord.diagnosis_name,
      diagnosis_type: "ENCOUNTER", // 写死的诊断类型
      encounter_id: 2, // 写死的就诊记录ID
      diagnosis_status: "ACTIVE", // 写死的诊断状态
      recorded_by: 4096, // 写死的记录者ID
      // 构建前后缀JSON字符串
      diagnosis_modifier: JSON.stringify({
        prefix: diagnosisRecord?.diagnosis_modifier?.prefix || "",
        suffix: diagnosisRecord?.diagnosis_modifier?.suffix || ""
      })
    };
    await Apis.addDiagnosis(diagnosisData);
    // 刷新诊断记录
    await recordEntry.fetchRecords()
  } else {
    // 用户点击了取消
    saveFlag = false
  }
  return saveFlag
})

provide('edit-record', async (params: object) => {
  let { context } = params as any
  if (!context) return
  let diagnosisContext = {
    diagnosisRecord: cloneDeep(context.diagnosisRecord)
  }
  let schema = props.schema.schema['edit-record-modal']
  const recordEntry = await getCompInstance('record-entry')
  const result = await dialog.value.dialog(
    diagnosisContext,
    schema
  );
  if (result) {
    // 用户点击了保存
    let { diagnosisRecord } = diagnosisContext
    await Apis.updateDiagnosisAffix({
      id: diagnosisRecord.id,
      prefix: diagnosisRecord?.diagnosis_modifier?.prefix || "",
      suffix: diagnosisRecord?.diagnosis_modifier?.suffix || ""
    })
  // 刷新诊断记录
  await recordEntry.fetchRecords()
  } else {
    // 用户点击了取消
  }
})

provide('delete-record', async (params: object) => {
  let { context } = params as any;
  if (!context) return;
  let { index } = context;
  const recordEntry = await getCompInstance('record-entry');
  // 获取当前行的DOM元素
  const rowEl = await recordEntry.getRowElement(index);
  if (!rowEl) return;
  // 计算行位置和表格宽度
  const rect = rowEl.getBoundingClientRect();
  const tableEl = recordEntry.getTableElement();
  const tableRect = tableEl.getBoundingClientRect();

  // 位置信息
  const position = {
    top: rect.bottom + window.scrollY, // 行底部位置
    left: rect.left + window.scrollX, // 行左边位置
    tableWidth: tableRect.width // 表格宽度
  };

  let schema = props.schema.schema['delete-record-modal'];

  // 设置弹框右边框与表格右边框对齐
  const dialogWidth = 280;
  // 计算左边位置：表格右边位置减去弹框宽度
  const leftPosition = position.left + position.tableWidth - dialogWidth;
  if (!schema.style) schema.style = {};
  schema.style.main = {
    ...schema.style.main,
    width: `${dialogWidth}px`,
    position: 'absolute',
    top: `${position.top}px`, // 右边与表格右边对齐
    left: `${leftPosition}px`
  };
  const result = await dialog.value.dialog(
    context,
    schema
  );
  if (result) {
    // 用户点击了确认删除
    let { diagnosisRecord } = context
    await Apis.deleteDiagnosis({ id: diagnosisRecord.id });
    // 刷新诊断记录
    await recordEntry.fetchRecords();
  } else {
    // 用户点击了取消
  }
})

async function getCompInstance(name: string) {
  let { comp } = (await new Promise((cb) => eventBus.emit(name, { cb }))) || ({} as any)
  return comp ?? {}
}

async function handlePatientChange(newId: number) {
  currentPatientId.value = newId;

  const recordEntry = await getCompInstance('record-entry');
  if (!recordEntry) return;

  // 刷新诊断记录
  await recordEntry.fetchRecords();

  // 滚动到顶部
  recordEntry.scrollToTop();

  const recordSelector = await getCompInstance('record-selector');
  if (!recordSelector) return;

  // 清空搜索框
  recordSelector.clearInputValue();
}


onMounted(async () => {
  // 初始执行一次
  const initialId = getContext(props)?.workspace?.patient?.id;
  if (initialId) {
    await handlePatientChange(initialId);
  }
});

watch(
  () => {
    const context = getContext(props)
    return context?.workspace?.patient?.id
  },
  async (newId: number, oldId: number) => {
    if (newId && newId !== oldId) {
      await handlePatientChange(newId);
    }
  },
)

defineExpose({ compName: 'diagnosisEntry' })
</script>

<style lang="scss" scoped>
.diagnosis-entry {
  display: flex;
  flex: 1 1 auto;
  min-height: 5rem;
}
</style>
