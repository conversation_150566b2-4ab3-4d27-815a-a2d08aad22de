/**
 * 同步数据到编辑器的服务
 * 处理模块间的数据同步Action
 */

import { editorManager } from './EditorInstanceManager'
import { EditorDataApis } from '@/service/editor/api'

export interface editorDataSyncAction {
  type: string           // Action类型：'ORDER_ADDED', 'DIAGNOSIS_UPDATED'等
  sourceKey: string      // 源头标识：关联编辑器元素的sourceKey
  targetViewId: string   // 目标编辑器ID
  patientId: string      // 患者ID（用于校验）
  entityId?: string      // 实体ID（可选）
  timestamp: number      // 时间戳
}

class EditorDataSyncService {
  private static instance: EditorDataSyncService;

  private constructor() {}

  static getInstance(): EditorDataSyncService {
    if (!EditorDataSyncService.instance) {
      EditorDataSyncService.instance = new EditorDataSyncService();
    }
    return EditorDataSyncService.instance;
  }

  /**
   * 发送数据同步Action
   */
  dispatch(action: editorDataSyncAction): void {
    console.log(`[DataSync] 收到Action:`, action);
    this.handleAction(action);
  }

  /**
   * 处理数据同步Action
   */
  private async handleAction(action: editorDataSyncAction): Promise<void> {
    try {
      // 1. 获取目标编辑器上下文
      const context = editorManager.getEditorContext(action.targetViewId);
      
      if (!context) {
        console.warn(`[DataSync] 编辑器不存在: ${action.targetViewId}`);
        return;
      }

      // 2. 校验患者ID
      if (context.patientId !== action.patientId) {
        console.warn(`[DataSync] 患者ID不匹配: 期望${action.patientId}, 实际${context.patientId}`);
        return;
      }

      // 3. 根据Action类型执行对应的同步操作
      await this.executeSync(action);

    } catch (error) {
      console.error(`[DataSync] 处理Action失败:`, error);
    }
  }

  /**
   * 执行具体的同步操作 协议未确定
   */
  private async executeSync(action: editorDataSyncAction): Promise<void> {
    switch (action.type) {
      case 'ORDER_ADDED':
      case 'ORDER_UPDATED':
      case 'ORDER_DELETED':
        await this.syncOrdersToEditor(action);
        break;

      case 'DIAGNOSIS_ADDED':
      case 'DIAGNOSIS_UPDATED':
      case 'DIAGNOSIS_DELETED':
        await this.syncDiagnosisToEditor(action);
        break;

      case 'LAB_RESULT_RECEIVED':
        await this.syncLabResultsToEditor(action);
        break;

      default:
        console.warn(`[DataSync] 未知的Action类型: ${action.type}`);
    }
  }

  /**
   * 同步医嘱数据到编辑器
   */
  private async syncOrdersToEditor(action: editorDataSyncAction): Promise<void> {
    try {
      // 使用EditorDataApis查询医嘱数据
      const orderData = await EditorDataApis.querySingleField({
        table: 'order_proc',
        column: 'drug_name', // 示例字段
        condition: `patient_id = '${action.patientId}' AND status = 'active'`
      });

      // 获取编辑器并同步数据
      const editor = await editorManager.getEditor(action.targetViewId);

      if (editor && typeof editor.syncOrderData === 'function') {
        await editor.syncOrderData([orderData]);
        console.log(`[DataSync] 医嘱数据已同步到编辑器: ${action.targetViewId}`);
      }

    } catch (error) {
      console.error(`[DataSync] 同步医嘱数据失败:`, error);
    }
  }

  /**
   * 同步诊断数据到编辑器
   */
  private async syncDiagnosisToEditor(action: editorDataSyncAction): Promise<void> {
    try {
      // 使用EditorDataApis查询诊断数据
      const diagnosisData = await EditorDataApis.querySingleField({
        table: 'diagnosis',
        column: 'diagnosis_name', // 示例字段
        condition: `patient_id = '${action.patientId}'`
      });

      // 获取编辑器并同步数据
      const editor = await editorManager.getEditor(action.targetViewId);

      if (editor && typeof editor.syncDiagnosisData === 'function') {
        await editor.syncDiagnosisData([diagnosisData]);
        console.log(`[DataSync] 诊断数据已同步到编辑器: ${action.targetViewId}`);
      }

    } catch (error) {
      console.error(`[DataSync] 同步诊断数据失败:`, error);
    }
  }

  /**
   * 同步检验结果到编辑器
   */
  private async syncLabResultsToEditor(action: editorDataSyncAction): Promise<void> {
    try {
      // 使用EditorDataApis查询检验数据
      const labData = await EditorDataApis.querySingleField({
        table: 'order_proc',
        column: 'result_value', // 示例字段
        condition: `patient_id = '${action.patientId}' AND order_type = 'lab'`
      });

      // 获取编辑器并同步数据
      const editor = await editorManager.getEditor(action.targetViewId);

      if (editor && typeof editor.syncLabData === 'function') {
        await editor.syncLabData([labData]);
        console.log(`[DataSync] 检验数据已同步到编辑器: ${action.targetViewId}`);
      }

    } catch (error) {
      console.error(`[DataSync] 同步检验数据失败:`, error);
    }
  }

  /**
   * 模拟发送Action（用于测试）
   */
  simulateAction(type: string, sourceKey: string, targetViewId: string, patientId: string, entityId?: string): void {
    console.log(`[DataSync] 🧪 模拟发送Action`);
    
    this.dispatch({
      type,
      sourceKey: `${sourceKey}(模拟)`,
      targetViewId,
      patientId,
      entityId,
      timestamp: Date.now()
    });
  }
}

// 导出单例实例
export const editorDataSyncService = EditorDataSyncService.getInstance();
