//patCondition

{
  type: 'patCondition',
  ui: [
    {
      type: 'toolbar',
      style: {
        alignItems: 'center',
        gap: "16px"
      },
      children: [
        {
          type: "InputComp",
          model: "conditions.keyword",
          slots: [
            {
              name: "prefix",
              attrs: {
                style: "color: #202020"
              },
              content: {
                component: "SearchOutlined"
              }
            }
          ],
          event: {
            enterEvent: 'change-keyword'
          },
        },
        {
          type: "DatePick",
          model: "conditions.dateRange",
          event: {
            change: 'change-date'
          },
          rangePresets: [
            {label: "今天", diff: 0, unit: 'd'},
            {label: "过去7天", diff: -7, unit: 'd'},
            {label: "过去14天", diff: -14, unit: 'd'},
            {label: "过去30天", diff: -30, unit: 'd'},
          ],
          attrs: {}
        },
        {
          type: "CheckboxGroup",
          model: "conditions.visitPeriod",
          isGroup: true,
          options: [
            {"label": "上午", "value": "AM"},
            {"label": "下午", "value": "PM"},
          ],
          event: {
            change: 'change-period'
          },
          style: {
            marginLeft: "10px"
          }
        },
        {
          type: 'button',
          event: {
            name: 'reset-conditions'
          },
          label: '重置'
        },
      ]
    },
    {
      type: 'toolbar',
      children: [
        {
          type: "RadioGroup",
          model: "conditions.visitStatus",
          event: {
            change: 'change-visit-status'
          },
          radioType: "button",
          options: [
            {"label": "全部", "value": "all"},
            {"label": "待就诊", "value": "pending"},
            {"label": "已过号", "value": "missed"},
            {"label": "就诊中", "value": "inProgress"},
            {"label": "已就诊", "value": "completed"}
          ],
          attrs: {
            buttonStyle: "solid"
          },
          labelSuffixModel: {
            "all": "visitStatusLabelSuffix.all",
            "pending": "visitStatusLabelSuffix.pending",
            "inProgress": "visitStatusLabelSuffix.inProgress",
            "completed": "visitStatusLabelSuffix.completed",
            "missed": "visitStatusLabelSuffix.missed",
          }
        },
      ]
    }
  ]
}
