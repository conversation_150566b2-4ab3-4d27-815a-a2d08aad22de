<template>
  <div class="pat-appointment">
    <component
      :is="loadComponent(props.schema?.ui?.type)"
      v-if="props.schema"
      :context="getContext(props)"
      :schema="props.schema?.ui"
    />
  </div>
  <WnDialog ref="dialog" />
</template>

<script lang="ts" setup>
import { defineProps, provide, ref } from 'vue'
import { loadComponent } from '../componentRegistry'
import { getContext } from '@/lib/getContext'
import WnDialog from '@/components/containers/WnDialog.vue'
import { deepMerge } from '@/lib/copymerge'
import { execAI2, getFile } from '@/lib/aiLib/ai'
import { DateTime } from 'luxon'
import { Urls } from '@/service/base/urls'
import { dbSvc } from '@/service/base/dbSvc'

const props = defineProps<{
  schema: any
  context: { [key: string]: any }
}>()

const dialog = ref<InstanceType<typeof WnDialog>>()

provide('list-patients', async (param: any = {}) => {
  const data = await dbSvc(Urls.xQuery, 'xListPatients', {}, ['id', 'name'])
  return data
})

provide('list-encounters', async (param: any = {}) => {
  let context = getContext(props)
  let { patData: { id = '' } = {} } = context as any
  if (!id) return

  const data = await dbSvc(Urls.xQuery, 'xListEncounters', {
    pat_id: id
  })

  return (data || []).map(({ id, date }: any) => {
    date = date
      ? DateTime.fromSeconds(date, { zone: 'asia/shanghai' }).toFormat('yy-MM-dd HHmm')
      : '-'
    return { id, date }
  })
})

provide('select-patient', async (param = {}) => {
  let context = getContext(props)
  let { data: { id = '' } = {} } = param as any
  if (!id) return

  const [data] = (await dbSvc(Urls.xQuery, 'xGetPatientInfo', { id })) || []
  if (!data) return

  context.patData = data
  context.detail.detail = {} //refresh the encounter toc-detail, by calling list-encounters.
  context.detail.patient = context.patData
  return {}
})

provide('select-encounter', async (param = {}) => {
  let context = getContext(props)
  let { data: { id = '' } = {} } = param as any
  if (!id) return

  const [encounter] = await dbSvc(Urls.xQuery, 'xGetEncounter', { id })

  context.detail.detail.encounter = encounter
  return encounter
})

provide('new-patient', async (param = {}) => {
  let context = getContext(props)

  let { getParentExposed } = param || ({} as any)
  let { newItem } = getParentExposed('toc')

  let patData = {}
  context.patData = patData
  context.detail.patient = patData
  newItem(patData)
  context.detail = {}
  return {}
})

provide('new-appointment', async (param = {}) => {
  let context = getContext(props)
  let { patData: { id = 0 } = {}, detail = {} } = context as any
  if (!id) return

  let { getParentExposed } = param || ({} as any)
  let { newItem } = getParentExposed('toc')
  newItem({})

  detail.detail.encounter = {}
  return {}
})

provide('save-patient', async (param = {}) => {
  let context = getContext(props)
  let { detail: { patient = {} } = {} } = context

  let { id } = patient
  if (!id) {
    id = await dbSvc(Urls.xUpdate, 'xNewPatient', patient)
    if (!id) return
  } else {
    await dbSvc(Urls.xUpdate, 'xUpdatePatient', patient)
  }

  deepMerge(context.detail.patient, { id })
  context.patData = context.detail.patient
  context.detail.detail = {}
  return {}
})

provide('save-encounter', async (param = {}) => {
  let context = getContext(props)
  let {
    detail: { detail: { encounter = {} } = {}, patient = {} }
  } = context
  if (!patient.id) {
    await window.popAlert({ message: 'patient id is not set.' })
    return
  }
  encounter.patient_basic_info_id = patient.id

  let { id } = encounter
  if (!id) {
    id = await dbSvc(Urls.xUpdate, 'xNewEncounter', encounter)
    if (!id) return
  } else {
    await dbSvc(Urls.xUpdate, 'xUpdateEncounter', encounter)
  }

  if (id) {
    deepMerge(context, { detail: { appointment: { id } } })
  }
})

provide('generate-data', async (param: any) => {
  let context = getContext(props)
  let prompt = `
  当前时间：${DateTime.now().toFormat('yyyy/MM/dd')}
  ${await getFile('SOP-prompts', '生成患者数据')}
  `
  const dialogSchema = props.schema.dialog.genPatData
  const data = {
    prompt
  }
  const dRes = await dialog.value!.dialog(data, dialogSchema)
  if (!dRes || !data.prompt) {
    await window.popAlert({ message: 'AI 需要合适的提示词。修改后重新提供。' })
    return
  }
  let p = context.detail.patient
  let { parsedData: { patient = {} } = {} } = await execAI2(data.prompt, param?.context || {})
  deepMerge(context.detail.patient, patient, { id: p.id })
})

provide('ambulatory-checkin', async (param: any) => {
  let context = getContext(props)
  let { detail: { detail: { encounter: { id } = {} as any } = {} } = {} } = context || {}
  debugger

  if (!id) return

  const [data] = (await dbSvc(Urls.xQuery, 'insertQueueEntry', { encounter_id: id })) || []
  if (!data) return
})

defineExpose({ compName: props?.schema?.type || '-' })
</script>

<style lang="scss" scoped>
.pat-appointment {
  display: flex;
  flex: 1 1 auto;
  min-height: 300px;
}
</style>
