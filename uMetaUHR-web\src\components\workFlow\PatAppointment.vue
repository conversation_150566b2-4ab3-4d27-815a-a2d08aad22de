<template>
  <div class="pat-appointment">
    <component
      :is="loadComponent(props.schema?.ui?.type)"
      v-if="props.schema"
      :context="getContext(props)"
      :schema="props.schema?.ui"
    />
  </div>
  <WnDialog ref="dialog" />
</template>

<script lang="ts" setup>
import { defineProps, provide, ref } from 'vue'
import { loadComponent } from '../componentRegistry'
import { getContext } from '@/lib/getContext'
import WnDialog from '@/components/containers/WnDialog.vue'
import { dbGet, dbList, dbSave } from '@/lib/dataMan'
import { deepMerge } from '@/lib/copymerge'
import { execAI2, getFile } from '@/lib/aiLib/ai'
import { DateTime } from 'luxon'

const props = defineProps<{
  schema: any
  context: { [key: string]: any }
}>()

const dialog = ref<InstanceType<typeof WnDialog>>()

provide('list-patients', async (param: any = {}) => {
  let sql = `select id, name
             from "maxEMR".patient_basic_info`
  if (!sql) {
    await alert('patient query is missing')
    return
  }
  return (await dbList({ sql })) || []
})

provide('list-encounters', async (param: any = {}) => {
  let context = getContext(props)
  let { patData: { id = '' } = {} } = context as any
  if (!id) return

  let sql = `SELECT e.id,
                    e.contact_date as date
             FROM "maxEMR".encounter e
             WHERE e.patient_basic_info_id = ?::int
             ORDER BY e.contact_date DESC`
  return (
    (await dbList({
      sql,
      param: [id]
    })) || []
  ).map(({ id, date }: any) => {
    date = date
      ? DateTime.fromSeconds(date, { zone: 'asia/shanghai' }).toFormat('yy-MM-dd HHmm')
      : '-'
    return { id, date }
  })
})

provide('select-patient', async (param = {}) => {
  let context = getContext(props)
  let { data: { id = '' } = {} } = param as any
  if (!id) return

  // Get patient data
  context.patData =
    (await dbGet({
      sql: 'select * from "maxEMR".patient_basic_info where id = ?::int',
      param: [id]
    })) || []

  context.detail.detail = {} //refresh the encounter toc-detail, by calling list-encounters.
  context.detail.patient = context.patData
  return {}
})

provide('select-encounter', async (param = {}) => {
  let context = getContext(props)
  let { data: { id = '' } = {} } = param as any
  if (!id) return

  const encounter = await dbGet({
    sql: `SELECT *
          FROM "maxEMR".encounter e
          WHERE e.id = ?::int`,
    param: [id]
  })

  context.detail.detail.encounter = encounter
  return encounter
})

provide('new-patient', async (param = {}) => {
  let context = getContext(props)

  let { getParentExposed } = param || ({} as any)
  let { newItem } = getParentExposed('toc')

  let patData = {}
  context.patData = patData
  context.detail.patient = patData
  newItem(patData)
  context.detail = {}
  return {}
})

provide('new-appointment', async (param = {}) => {
  let context = getContext(props)
  let { patData: { id = 0 } = {}, detail = {} } = context as any
  if (!id) return

  let { getParentExposed } = param || ({} as any)
  let { newItem } = getParentExposed('toc')
  newItem({})

  detail.detail.encounter = {}
  return {}
})

provide('save-patient', async (param = {}) => {
  let context = getContext(props)
  let { detail: { patient = {} } = {} } = context
  let {
    ids: [{ id = 0 }]
  } =
    (await dbSave({
      table: `patient_basic_info`,
      data: [patient],
      conflict_column: 'id',
      update_columns: [
        ...(patient.id ? ['id'] : []),
        'name',
        'gender',
        'birth_date',
        'id_card',
        'phone',
        'address',
        'insurance_info'
      ]
    })) || ({} as any)
  if (!id) return

  deepMerge(context.detail.patient, { id })
  context.patData = context.detail.patient
  context.detail.detail = {}
  return {}
})

provide('save-encounter', async (param = {}) => {
  let context = getContext(props)
  let {
    detail: { detail: { encounter = {} } = {}, patient = {} }
  } = context
  if (!patient.id) {
    await window.popAlert({ message: 'patient id is not set.' })
    return
  }
  encounter.patient_basic_info_id = patient.id

  const result = await dbSave({
    table: `encounter`,
    data: [encounter],
    conflict_column: 'id',
    update_columns: [
      ...(encounter.id ? ['id'] : []),
      'patient_basic_info_id',
      'enc_type_c',
      'contact_date',
      'provider_id',
      'department_id',
      'appt_status_c',
      'enc_closed_yn',
      'checkin_time',
      'checkout_time',
      'extensions'
    ]
  })

  if (result?.ids?.[0]?.id) {
    deepMerge(context, { detail: { appointment: { id: result.ids[0].id } } })
  }
  return result
})

provide('generate-data', async (param: any) => {
  let context = getContext(props)
  let prompt = `
  当前时间：${DateTime.now().toFormat('yyyy/MM/dd')}
  ${await getFile('SOP-prompts', '生成患者数据')}
  `

  const dialogSchema = props.schema.dialog.genPatData
  const data = {
    prompt
  }
  const dRes = await dialog.value!.dialog(data, dialogSchema)
  if (!dRes || !data.prompt) {
    await window.popAlert({ message: 'AI 需要合适的提示词。修改后重新提供。' })
    return
  }
  let p = context.detail.patient
  let { parsedData: { patient = {} } = {} } = await execAI2(data.prompt, param?.context || {})
  deepMerge(context.detail.patient, patient, { id: p.id })
})

defineExpose({ compName: props?.schema?.type || '-' })
</script>

<style lang="scss" scoped>
.pat-appointment {
  display: flex;
  flex: 1 1 auto;
  min-height: 300px;
}
</style>
