<template>
  <div v-if="messages.length > 0" class="wn-alert">
    <div class="wn-alert-header">
      消息
      <button style="margin: 0" @click="() => closeErrBox(-1)">x</button>
    </div>
    <div class="wn-alert-body">
      <div
        v-for="(m, index) in messages"
        :key="`wn-alert-${index}`"
        class="wn-alert-box"
        tabindex="0"
        @click="() => checkBox(index)"
      >
        <button :class="{ visible: m?.naildown }">&#x2713;</button>
        {{ m.message }}
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

const messages = ref<{ message: string }[]>([]) as any

function show({ message = '' }: { message?: string } = {}) {
  if (!message) return
  let v = { message } as any
  messages.value.push(v)
  v.tag = +new Date()
  registerRemove(v)
}

function registerRemove(v: any, timeout = 5000): void {
  setTimeout(() => {
    for (let i = 0; i < messages.value.length; i++) {
      if (messages.value[i].tag == v.tag && !v.naildown) {
        messages.value.splice(i, 1)
      }
    }
  }, timeout)
}

function closeErrBox(index: number) {
  while (messages.value.length > 0) messages.value.pop()
}

function checkBox(index: number) {
  if (!messages.value[index]?.naildown) {
    messages.value[index].naildown = 1
    return
  }
  delete messages.value[index].naildown
  registerRemove(messages.value[index], 1000)
}

window.popAlert = async (data: any) => show(data)

defineExpose({ compName: 'alert' })
</script>

<style lang="scss" scoped>
.wn-alert {
  display: block;
  position: fixed;
  bottom: 5px;
  right: 5px;
  width: 20em;
  box-shadow: 1px 1px 5px gray;
  background: #effafa;
  z-index: 200;
  padding: 5px;
  border-radius: 5px;

  .wn-alert-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 3px;
    border-radius: 3px;
    font-weight: bolder;
    background: sienna;
    color: white;
    padding: 0 5px;
  }

  .wn-alert-body {
    margin: 0;
    padding: 0;
    border: none;
    max-height: calc(100vh - 3rem);
    overflow: auto;

    .wn-alert-box {
      display: block;
      border: 1px solid lightblue;
      margin: 3px;
      padding: 5px;
      border-radius: 5px;
      background: white;
      white-space: break-spaces;
      word-break: break-all;
      position: relative;

      button {
        display: none;
        position: absolute;
        right: 0;
        top: 0;
        background: white;
        margin: 3px;
        border-radius: 5px;
        border: none;
        color: lightgray;
        font-size: small;
      }

      button.visible {
        display: block;
        color: green;
      }

      &:hover button {
        display: block;
      }
    }
  }
}
</style>
