<template>
  <template v-if="props?.schema?.isGroup">
    <a-checkbox-group :value="getNestedValue(context, props?.schema?.model)" v-bind="props?.schema?.attrs"
      @change="onChange" :style="props.schema.style">
      <a-checkbox :value="option.value" v-for="option in props?.schema?.options">
        {{ `${option.label}${props.schema.labelAddonsModel && props.schema.labelAddonsModel[option.value] ?
          getNestedValue(context, props.schema.labelAddonsModel[option.value]) : ''}` }}
      </a-checkbox>
    </a-checkbox-group>
  </template>
  <template v-else>
    <a-checkbox :value="getNestedValue(context, props?.schema?.model)" v-bind="props?.schema?.attrs"
      @change="onChange">
      {{ `${props?.schema?.options?.[0]?.label}${props.schema.labelAddonsModel && props.schema.labelAddonsModel[props?.schema?.options?.[0]?.value] ?
        getNestedValue(context, props.schema.labelAddonsModel[props?.schema?.options?.[0]?.value]) : ''}` }}
    </a-checkbox>
  </template>
</template>

<script lang="ts" setup>
import { defineProps, inject } from 'vue'
import { getNestedValue, setNestedValue } from '@/lib/getContext'

const props = defineProps<{
  label?: string
  schema: {
    attrs?: Record<string, any>,
    model: string,
    isGroup?: boolean,
    options: Array<{
      label: string
      value: any
    }>,
    event?: {
      change?: string
    },
    style: Record<string, string>,
    labelAddonsModel?: Record<string, string>
  }
  context: { [key: string]: any }
}>()

const {
  schema: {
    event: {
      change: changeEventInject = "change-checkbox-event",
    } = {}
  } = {}
} = props

const changeEvent = inject(changeEventInject, () => 1) as Function

const onChange = (e: any) => {
  const value = props?.schema?.isGroup ? e : e.target.checked
  setNestedValue(props.context, props.schema?.model, value)
  changeEvent(value)
};
</script>

<style lang="scss" scoped></style>
