<template>
  <div class="patent-condition">
    <component
      v-for="child in schema.ui"
      :is="loadComponent(child.type || '')"
      :context="getContext(props)"
      :schema="child"
    />
  </div>
</template>

<script lang="ts" setup>
import { inject, defineProps, onMounted, watch, provide } from 'vue'
import { loadComponent } from '../componentRegistry'
import { getContext } from '@/lib/getContext'
import { Dayjs } from 'dayjs';

const props = defineProps<{
  schema: {
    type: string
    ui?: any[],
    children: Array<any>
  }
  context: { [key: string]: any }
}>()

const getListPatients = inject('list-patients', () => 1) as Function


const defaultConditions = inject('init-conditions', () => 1) as Function

const changeConditions = inject('change-conditions', () => 1) as Function

provide('reset-conditions', ()=>{
  defaultConditions()
  changeConditions();
  getListPatients()
})

const change = ()=>{
  changeConditions();
  getListPatients()
}
provide('change-date', change)
provide('change-visit-status', change)
provide('change-period', change)
provide('change-keyword', change)

defineExpose({ compName: props?.schema?.type || '-' })
</script>

<style lang="scss" scoped>
.patent-condition {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  overflow: auto;
}
</style>
