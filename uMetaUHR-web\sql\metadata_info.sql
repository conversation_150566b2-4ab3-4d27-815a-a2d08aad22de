DROP TABLE IF EXISTS metadata_info CASCADE;

CREATE TABLE metadata_info
(
    -- Primary key as auto-incrementing integer per .clinerules
    id             SERIAL PRIMARY KEY,

    -- Core identifier (now with UNIQUE constraint)
    code           VARCHAR(50)  NOT NULL UNIQUE CHECK (code ~ '^[A-Z]+\..+'),
    /* 数据元编码，采用"域.元素"的层级结构 */

    name           VARCHAR(100) NOT NULL,
    /* 数据元标准名称 */

    description    TEXT,
    /* 详细说明 */

    definition     TEXT,
    /* 正式定义 */

    data_source    VARCHAR(100),
    /* 数据来源系统 */

    business_owner VARCHAR(50),
    /* 业务责任主体 */

    -- Type control
    type           VARCHAR(10) CHECK (type IN ('simple', 'composite', 'virtual')),

    -- Structure definition
    data_type      VARCHAR(20) GENERATED ALWAYS AS (
        CASE WHEN type = 'composite' THEN NULL ELSE (config ->> 'dataType') END
        ) STORED,

    path           VARCHAR(255) GENERATED ALWAYS AS (
        CASE
            WHEN type = 'composite' THEN NULL
            ELSE COALESCE(config ->> 'path', '$.' || REPLACE(code, '.', '->'))
            END
        ) STORED,

    -- Foreign key follows naming convention
    parent_id      INTEGER REFERENCES metadata_info (id),
    /* 父级数据元ID */

    -- Code value definitions
    codeValue      JSONB        NOT NULL DEFAULT '[]'::JSONB CHECK (jsonb_typeof(codeValue) = 'array'),
    /* 代码值定义，包含：
     * - code: 编码值
     * - name: 标准名称
     * - displayName: 显示名称
     * - alias: 别名/缩写 */

    -- Rule configuration
    config         JSONB        NOT NULL DEFAULT '{}'::JSONB CHECK (jsonb_typeof(config) = 'object'),

    -- Version control
    version        INT          NOT NULL DEFAULT 1,

    -- Timestamps as integer seconds since epoch
    created_at     BIGINT       NOT NULL DEFAULT EXTRACT(EPOCH FROM NOW()),
    /* 创建时间 (秒) */

    updated_at     BIGINT       NOT NULL DEFAULT EXTRACT(EPOCH FROM NOW())
    /* 最后修改时间 (秒) */
);

-- Indexes
CREATE INDEX idx_metadata_info_parent_id ON metadata_info (parent_id);
CREATE INDEX idx_metadata_info_type ON metadata_info (type);
CREATE INDEX idx_metadata_info_data_type ON metadata_info (data_type);
CREATE INDEX idx_metadata_info_path ON metadata_info (path);
CREATE INDEX idx_metadata_info_created_at ON metadata_info (created_at);
CREATE INDEX idx_metadata_info_updated_at ON metadata_info (updated_at);

-- Comments
COMMENT ON TABLE metadata_info IS '数据元定义表';
COMMENT ON COLUMN metadata_info.code IS '数据元编码，格式: DOMAIN.ELEMENT';
COMMENT ON COLUMN metadata_info.config IS '约束规则及配置';
COMMENT ON COLUMN metadata_info.parent_id IS '父级数据元ID引用';
