import time
import random
import argparse
from pync import Notifier

def start_pomodoro(cycle, work_time, variable_time):
    while True:
        # 计算实际工作时间
        actual_work_time = work_time + random.randint(-variable_time, variable_time)

        # 提示工作开始
        Notifier.notify(f'工作时间开始！工作 {actual_work_time} 分钟', title='番茄钟')
        print(f'开始工作 {actual_work_time} 分钟')

        # 工作计时
        for remaining in range(actual_work_time * 60, 0, -1):
            if remaining % 300 == 0:  # 每10秒提示一次剩余时间
                minutes, seconds = divmod(remaining, 60)
                Notifier.notify(f'剩余时间：{minutes} 分 {seconds} 秒', title='番茄钟')
                print(f'剩余时间：{minutes} 分 {seconds} 秒')
            time.sleep(1)

        # 提示工作结束
        Notifier.notify('工作时间结束！休息一会儿吧！', title='番茄钟')
        print('工作时间结束！')

        # 休息计时
        rest_time = cycle - actual_work_time
        print(f'休息 {rest_time} 分钟')
        time.sleep(rest_time * 60)

if __name__ == "__main__":
    # 配置命令行参数解析
    parser = argparse.ArgumentParser(description='番茄钟定时器')
    parser.add_argument('cycle', type=int, help='周期（分钟）')
    parser.add_argument('work_time', type=int, help='标准工作时长（分钟）')
    parser.add_argument('variable_time', type=int, help='可变时间（分钟）')
    args = parser.parse_args()

    # 运行番茄钟
    print(f'番茄钟启动：周期={args.cycle} 分钟，标准工作时长={args.work_time} 分钟，可变时间={args.variable_time} 分钟')
    start_pomodoro(args.cycle, args.work_time, args.variable_time)
