import type { ClinicalNote, MetaPiniaData } from './metaPinia-types'
import { createMetaPinia } from './metaPinia'

interface NoteData extends MetaPiniaData {
  notes: ClinicalNote[]
}

/**
 * 临床记录数据管理扩展
 */
export function createNoteStore(initialData: NoteData) {
  const med = createMetaPinia(initialData)

  // 临床记录专用操作扩展
  const noteApi = {
    ...med,

    /**
     * 创建临床记录
     */
    createNote(content: string): ClinicalNote {
      if (!med.data.currentPatient) {
        throw new Error('没有当前患者')
      }

      const newNote: ClinicalNote = {
        id: `note_${Date.now()}`,
        patientId: med.data.currentPatient.id,
        content,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      if (!med.data.notes) med.data.notes = []
      med.data.notes.push(newNote)
      med.logAction(`创建临床记录: ${content}`)
      return newNote
    },

    /**
     * 更新临床记录
     */
    updateNote(noteId: string, newContent: string): boolean {
      const note = med.data.notes?.find(n => n.id === noteId)
      if (!note) return false

      const oldContent = note.content
      note.content = newContent
      note.updatedAt = new Date()
      med.logAction(`更新临床记录: ${oldContent} → ${newContent}`)
      return true
    },

    /**
     * 获取患者的所有临床记录
     */
    getPatientNotes(patientId: string): ClinicalNote[] {
      return med.data.notes?.filter(note => note.patientId === patientId) || []
    },

    /**
     * 获取所有临床记录列表
     */
    getNoteList(): ClinicalNote[] {
      return med.data.notes || []
    }
  }

  return noteApi
}

export type NoteStore = ReturnType<typeof createNoteStore>
