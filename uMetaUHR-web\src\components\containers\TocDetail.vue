<template>
  <div :class="`${theme} field-w-toc-detail`" :style="schema?.style?.main">
    <div v-if="schema?.head" class="field-w-toc-detail-head">
      <component
        :is="loadComponent(tb.type)"
        v-for="(tb, index) in schema?.head || []"
        :key="`head-${index}`"
        :context="getContext(props)"
        :schema="tb"
      />
    </div>
    <div
      :class="{ 'toc-show': showTocSidebar, 'toc-hide': !showTocSidebar }"
      class="field-w-toc-detail-body"
    >
      <div v-if="!showTocSidebar" class="toc-sidebar-actor-out" @click="toggleToc">≫</div>
      <div
        v-if="schema?.toc && showTocSidebar"
        ref="tocDetail1"
        :style="{ ...(schema?.style?.toc || {}), width: tocWidth + 'px' }"
        class="toc-detail-1"
      >
        <div v-if="showTocSidebar" class="toc-sidebar-actor" @click="toggleToc">≪</div>
        <div v-if="showTocSidebar" class="drag-handle" @mousedown="startResize" @dblclick.prevent.stop="toggleToc">
          <div class="field-w-thumb">
            <div></div>
            <div></div>
            <div></div>
          </div>
        </div>
        <component
          :is="loadComponent(schema?.toc?.type)"
          :context="getContext(props)"
          :schema="schema?.toc"
          @toc-select-line="selectItem"
        />
      </div>
      <div class="toc-detail-2">
        <component
          :is="loadComponent(schema?.detail?.type)"
          v-if="schema?.detail"
          ref="detailComp"
          :context="getContext(props)"
          :schema="schema?.detail"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { inject, onMounted, provide, ref, watch } from 'vue'
import { loadComponent } from '@/components/componentRegistry'
import { getContext } from '@/lib/getContext'
import { cacheManager } from '@/lib/CacheManager'

interface Schema {
  type: string
  head?: any[]
  toc?: any
  detail?: any
  theme?: string
  showTocSidebar?: boolean
  myFavoriteId?: string
  tocDefaultWidth?: number
  style?: {
    main?: any
    toc?: any
    detail?: any
    vars?: {}
  }
  event?: {
    listItems?: string
    getItems?: string
    selectItem?: string
  }
}

interface Props {
  schema?: Schema
  context?: any
}

const props = defineProps<Props>()

const schema = ref(props.schema)
const theme = ref('')
const style = ref({})
const showTocSidebar = ref(true)
const selectedItemId = ref('')
const detailComp = ref(null)
const tocWidth = ref(props?.schema?.tocDefaultWidth || 300) // Default width
const isResizing = ref(false)
const tocDetail1 = ref<HTMLElement | null>(null)

onMounted(async () => {
  const {
    theme: schemaTheme,
    style: schemaStyle,
    tocDefaultWidth,
    showTocSidebar: schemaShowTocSidebar
  } = props.schema || {}
  theme.value = schemaTheme || ''
  style.value = schemaStyle || {}
  showTocSidebar.value = schemaShowTocSidebar ?? true

  const myFavoriteId = props.schema?.myFavoriteId
  if (myFavoriteId) {
    const favData = (await cacheManager.get(myFavoriteId)) || {}
    showTocSidebar.value = favData?.showTocSidebar ?? showTocSidebar.value
    tocWidth.value = favData?.tocWidth ?? tocDefaultWidth ?? tocWidth.value
  }
  showTocSidebar.value = showTocSidebar.value ?? true
})

function startResize(event: MouseEvent) {
  isResizing.value = true
  document.addEventListener('mousemove', handleResize)
  document.addEventListener('mouseup', stopResize)
}

async function handleResize(event: MouseEvent) {
  document.body.style.userSelect = 'none';
  if (!isResizing.value || !tocDetail1.value) return
  const rect = tocDetail1.value.getBoundingClientRect()
  const newWidth = event.clientX - rect.left
  if (newWidth <= 100) return

  tocWidth.value = newWidth

  const myFavoriteId = props.schema?.myFavoriteId
  if (myFavoriteId) {
    const favData = (await cacheManager.get(myFavoriteId)) || {}
    favData.tocWidth = tocWidth.value
    await cacheManager.set(myFavoriteId, favData)
  }
}

function stopResize() {
  isResizing.value = false
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
  document.body.style.userSelect = 'unset';
}

async function flowActivate() {
  const context = getContext(props)
  const id = selectedItemId.value
  context.detail = {} // new object to trigger refresh
  const d = await getItemFromParent({ id })
  Object.assign(context.detail, d)
  return 1
}

async function toggleToc(event: Event) {
  event?.stopPropagation()
  event?.preventDefault()
  showTocSidebar.value = !showTocSidebar.value

  const myFavoriteId = props.schema?.myFavoriteId
  if (myFavoriteId) {
    const favData = (await cacheManager.get(myFavoriteId)) || {}
    favData.showTocSidebar = showTocSidebar.value
    await cacheManager.set(myFavoriteId, favData)
  }
}

const {
  schema: {
    event: {
      listItems: listItemsEventName = 'toc-list-data',
      getItems: getItemEventName = 'toc-get-data',
      selectItem: selectItemEventName = 'toc-select-line'
    } = {}
  } = {}
} = props

const getItemFromParent = inject(getItemEventName, () => 1) as Function
const selectLine = async (selectedLine: any) => {
  await selectItem(selectedLine.data)
}
provide(selectItemEventName, selectLine)

const listItemsFromParent = inject(listItemsEventName, () => 1)
provide(listItemsEventName, listItemsFromParent)

provide('toc-new-data', async (selectedLine: any) => {
  const context = getContext(props)
  context.detail = {}
})

watch(selectedItemId, (newValue) => {
  // Handle selectedItemId changes if needed
})

async function selectItem(param: any, options: any = {}) {
  const { forceRefresh } = options
  const { id } = param || {}
  if (id && selectedItemId.value === id && !forceRefresh) return
  selectedItemId.value = id

  const context = getContext(props)
  context.detail = {} // new object to trigger refresh
  const d = await getItemFromParent(param)
  Object.assign(context.detail, d)
}

defineExpose({ compName: 'tocDetail', flowActivate })
</script>

<style lang="scss" scoped>
.field-w-toc-detail {
  --theme-background-color: v-bind('schema?.style?.vars?.themeBC || "none"');
  --theme-border-color: v-bind('schema?.style?.vars?.themeBorderColor || "#EEE"');
  --theme-content-background-color: v-bind('schema?.style?.vars?.themeContentBC || "white"');

  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  overflow: auto;
  margin: 2px;
  border-radius: 4px;
  padding: 2px;
  background-color: var(--theme-background-color);

  .field-w-toc-detail-head {
    flex: 0 0 auto;
    margin: 2px;
    height: 1pt;
  }

  .field-w-toc-detail-body {
    display: flex;
    flex: 1 1 auto;
    overflow: hidden;

    .toc-detail-1 {
      display: flex;
      flex: 0 0 auto;
      position: relative;
      background: none;
      overflow: visible;
      resize: horizontal; /* Optional: Enable native resize */
      padding: 2px;
      margin: 0 0 2px 2px;
    }

    .toc-detail-2 {
      display: flex;
      flex: 1 1 auto;
      overflow: auto;
      margin: 0 2px 2px 2px;
      background: var(--theme-content-background-color);
      border: 3px solid var(--theme-border-color);
      border-radius: 5px;
      padding: 5px;
    }
  }

  .field-w-toc-detail-body.toc-hide {
    position: relative;

    > .toc-detail-1 {
      overflow: hidden;
      width: 0 !important;
    }
  }

  .toc-sidebar-actor,
  .toc-sidebar-actor-out {
    font-weight: bolder;
    font-size: 7pt;
    color: #1c6ca1;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 1rem;
    position: absolute;
    bottom: 0;
    z-index: 10;
    width: 20px;
    cursor: pointer;
    overflow: hidden;
    background: linear-gradient(to right, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
    border: 1px solid lightblue;
  }

  .toc-sidebar-actor {
    right: 0;
    border-radius: 10px 0 0 10px;
  }

  .toc-sidebar-actor-out {
    border-radius: 0 10px 10px 0;
  }

  .drag-handle {
    display: flex;
    width: 1px;
    border: none;
    height: 100%;
    position: absolute;
    right: 0;
    background: transparent;
    z-index: 20;

    flex: 0 0 7px; // 增加宽度以提升可见性
    cursor: col-resize;
    align-items: center;
    justify-content: center;

    .field-w-thumb {
      display: none;
    }

    &:hover {
      width: 5px;

      .field-w-thumb {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 4px;

        div {
          width: 5px;
          height: 5px;
          border-radius: 50%; // 圆形
          transition: background-color 0.3s ease;
          background-color: brown; // 使用背景色代替边框
          box-shadow: 1px 1px 3px black;
        }
      }
    }
  }

  &.horizontal {
    .field-w-toc-detail-body {
      flex-direction: column;

      .toc-detail-1 {
        flex: 0 0 auto;
        height: 30rem;
      }
    }

    .toc-sidebar-actor,
    .toc-sidebar-actor-out {
      right: 0;
    }

    .field-w-toc-detail-body.toc-hide {
      > .toc-detail-1 {
        flex: 0 0 auto;
        height: 0 !important;
      }
    }
  }

  //second level for nested stacks
  .field-w-toc-detail {
    .toc-sidebar-actor,
    .toc-sidebar-actor-out {
      bottom: 1rem;
    }
  }
}
</style>
