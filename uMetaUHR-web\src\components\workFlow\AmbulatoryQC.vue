<template>
  <div class="ma-agents">
    <component
      :is="loadComponent(props.schema?.ui?.type)"
      v-if="props.schema"
      :context="getContext(props)"
      :schema="props.schema?.ui"
    />
  </div>
</template>

<script lang="ts" setup>
import { defineProps, onMounted, provide, ref } from 'vue'
import { loadComponent } from '../componentRegistry'
import { getContext } from '@/lib/getContext'
import axios from 'axios'
import { getAgent } from '@/lib/ai'
import { execAI2 } from '@/lib/aiLib/ai'
import { compileTemplate2 } from '@/components/action/TextProcessors/ext_handlebars'

const props = defineProps<{
  schema: {
    type: string
    children: Array<any>
  }
  context: { [key: string]: any }
}>()
let noteList = ref([]) as any

provide('toc-list-data', async (param: any) => {
  const { data: { result = [] } = {} } =
    (await axios.post('http://localhost:8080/note.qc/listData', {})) || {}
  noteList.value = result || []
  return result
})

provide('toc-get-data', async (param: any) => {
  let { ID } = param || {}
  if (!ID) return
  const item = noteList.value.find((x: any) => x.ID === ID)
  const context = getContext(props)
  if (context?.detail) {
    context.detail.qcResult = item?.结论
  }
  return item
})

onMounted(async () => {
  const agentData = await getAgent('门诊质控')
  const {
    config = {},
    prompts = [],
    role: { player_role = '', role_spec = '' } = {}
  } = agentData.data || {}
  const context = getContext(props)
  context.dataTemplate = prompts.find((o: any) => o?.name === 'dataDisplayTemplate')?.prompt
  context.resultTemplate = prompts.find((o: any) => o?.name === 'resultTemplate')?.prompt
})

provide('ambulatory-qc', async (param: any) => {
  let {
    context: {
      detail: data = {},
      detail: { ID = '' }
    }
  } = param || {}
  if (!ID) return

  const agentData = await getAgent('门诊质控')
  const {
    config = {},
    prompts = [],
    role: { player_role = '', role_spec = '' } = {}
  } = agentData.data || {}

  await window.popAlert({ message: 'AI 门诊质控 ... ' })
  const prompt = prompts.findLast(({ name }: any) => name === 'rule-1')?.prompt
  if (!prompt) {
    return await window.popAlert({ message: '未找到提示词模版。' })
  }

  const context = getContext(props)
  const content = await compileTemplate2(prompt, data)
  const { parsedData: aiComment } = await execAI2(content, data)

  const templateSource = `
{{#each this}}

### {{@index}}

**评分**: {{评分}}

**解释**: {{解释}}

**思考过程**: {{思考过程}}

---

{{/each}}
`

  context.detail.qcResult = await compileTemplate2(templateSource, aiComment)
  await window.popAlert({ message: '完成' })
})

defineExpose({ compName: props?.schema?.type || '-' })
</script>

<style lang="scss" scoped>
.ma-agents {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  overflow: auto;
}
</style>
