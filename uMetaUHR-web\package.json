{"name": "data-driven-ux", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "compile-peggy": "cd src/pegrule; for file in ./*.pegjs; do peggy \"$file\" --format es; done", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies-backend": {"A": {}, "B": {}}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@antv/g2plot": "^2.4.32", "@ckeditor/ckeditor5-build-classic": "^42.0.1", "@ckeditor/ckeditor5-vue": "^6.0.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.0.8", "@types/handlebars": "^4.1.0", "@uap/oidc-client": "^2.0.5", "@vue/compat": "^3.4.31", "ant-design-vue": "^4.2.6", "blockly": "^12.1.0", "clipboard": "^2.0.11", "codemirror": "^5.65.16", "dayjs": "^1.11.13", "dexie": "^4.0.8", "handlebars": "^4.7.6", "handlebars-async-helpers": "^1.0.6", "handlebars-helpers": "^0.10.0", "hotkeys-js": "^3.13.7", "js-yaml": "^4.1.0", "jsonlint": "^1.6.3", "jsonpath": "^1.1.1", "jsonpath-plus": "10.2.0", "luxon": "^3.6.1", "marked": "^13.0.2", "md5": "^2.3.0", "mini-css-extract-plugin": "^2.9.2", "moment": "^2.30.1", "ntp-client": "^0.5.3", "oidc-client": "^1.12.3", "pg": "^8.12.0", "pinia": "^2.1.7", "script-loader": "^0.7.2", "sortablejs": "^1.15.2", "spark-md5": "^3.0.2", "typesense": "^1.8.2", "vue": "^3.5.14", "vue-router": "^4.3.3", "vue-virtual-scroller": "^2.0.0-beta.8", "vue3-json-viewer": "2.2.2"}, "devDependencies": {"@rushstack/eslint-patch": "^1.8.0", "@tsconfig/node20": "^20.1.4", "@types/handlebars-helpers": "^0.5.6", "@types/jest": "^29.5.14", "@types/luxon": "^3.6.2", "@types/md5": "^2.3.5", "@types/mocha": "^10.0.10", "@types/node": "^20.14.5", "@types/ntp-client": "^0.5.0", "@types/pegjs": "^0.10.6", "@types/sortablejs": "^1.15.8", "@types/spark-md5": "^3.0.5", "@vitejs/plugin-vue": "^5.0.5", "@vitejs/plugin-vue-jsx": "^4.0.0", "@vue/compiler-sfc": "^3.4.31", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vue/tsconfig": "^0.5.1", "dotenv": "^16.4.5", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.23.0", "jest": "^30.0.0", "json5": "^2.2.3", "npm-run-all2": "^6.2.0", "peggy": "^4.2.0", "prettier": "^3.2.5", "raw-loader": "^4.0.2", "rollup": "^4.39.0", "sass": "^1.77.8", "start-server-and-test": "^2.0.4", "typescript": "~5.4.0", "unplugin-vue-components": "^28.7.0", "vite": "^5.3.1", "vite-plugin-json5": "^1.1.9", "vite-plugin-string": "^1.2.3", "vite-plugin-vue-devtools": "^7.3.1", "vue-tsc": "^2.0.21", "yaml-loader": "^0.8.1"}}