-- 医院排队叫号系统数据模型 (PostgreSQL版本)
-- 基于数据库设计标准规范创建

DROP TABLE IF EXISTS qs_service_definition CASCADE;

-- 1. 队列服务定义表
CREATE TABLE qs_service_definition
(
    id                 INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    service_id         INT UNIQUE         NOT NULL,
    service_name       VARCHAR(100)       NOT NULL,
    service_code       VARCHAR(20) UNIQUE NOT NULL,
    department_id      INT                NOT NULL,
    doctor_id          INT,

    -- 基础配置
    prefix             VARCHAR(1)         NOT NULL DEFAULT 'A',
    initial_serial     INTEGER            NOT NULL DEFAULT 1,
    queue_strategy     VARCHAR(20)        NOT NULL DEFAULT 'FIFO' CHECK (queue_strategy IN ('FIFO', 'PRIORITY', 'APPOINTMENT_FIRST')),
    max_concurrent     INTEGER                     DEFAULT 5,

    -- 拥堵控制策略
    max_queue_length   INTEGER,
    expected_wait_time INTEGER,
    overload_strategy  VARCHAR(20)        NOT NULL DEFAULT 'REDIRECT' CHECK (overload_strategy IN ('REJECT', 'REDIRECT', 'EXTEND_TIME')),

    -- 异常处理策略
    max_miss_count     INTEGER            NOT NULL DEFAULT 2,
    miss_penalty_score INTEGER            NOT NULL DEFAULT 10,
    recall_interval    INTEGER            NOT NULL DEFAULT 3,
    timeout_minutes    INTEGER            NOT NULL DEFAULT 30,

    -- 状态控制
    is_active          BOOLEAN            NOT NULL DEFAULT TRUE,
    is_public          BOOLEAN            NOT NULL DEFAULT TRUE,

    -- 时间戳
    created_at         INTEGER            NOT NULL DEFAULT extract(epoch from now()),
    updated_at         INTEGER            NOT NULL DEFAULT extract(epoch from now()),

    -- 外键约束
    FOREIGN KEY (department_id) REFERENCES organizations (id),
    FOREIGN KEY (doctor_id) REFERENCES users (id)
);

COMMENT ON TABLE qs_service_definition IS '队列服务定义及策略配置';
COMMENT ON COLUMN qs_service_definition.service_name IS '服务名称如"内科普通门诊"';
COMMENT ON COLUMN qs_service_definition.service_code IS '服务编码';
COMMENT ON COLUMN qs_service_definition.prefix IS '队列前缀';
COMMENT ON COLUMN qs_service_definition.is_public IS '是否对外开放';

-- 索引
CREATE INDEX idx_qs_service_department ON qs_service_definition (department_id);
CREATE INDEX idx_qs_service_doctor ON qs_service_definition (doctor_id);
CREATE INDEX idx_qs_service_active ON qs_service_definition (is_active);

-- 2. 患者队列表
DROP TABLE IF EXISTS qs_service_queue CASCADE;

CREATE TABLE qs_service_queue
(
    id                   INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    queue_id             INT,
    service_id           INT,
    patient_id           INT,
    encounter_id         INT UNIQUE ,

    -- 排队信息
    serial_number        VARCHAR(10),
    checkin_time         INTEGER,
    initial_score        INTEGER DEFAULT 50,
    current_score        INTEGER,
    miss_count           INTEGER DEFAULT 0,

    -- 时间节点
    first_call_time      INTEGER,
    last_call_time       INTEGER,
    service_start_time   INTEGER,
    finish_time          INTEGER,

    -- 状态控制
    status               VARCHAR(20) NOT NULL DEFAULT 'WAITING' CHECK (status IN
                                                                       ('WAITING', 'CALLED', 'IN_SERVICE',
                                                                        'COMPLETED', 'MISSED', 'CANCELLED')),
    status_reason        VARCHAR(100),

    -- 预测信息
    estimated_wait_time  INTEGER,
    predicted_serve_time INTEGER,

    -- 时间戳
    created_at           INTEGER     NOT NULL DEFAULT extract(epoch from now()),
    updated_at           INTEGER     NOT NULL DEFAULT extract(epoch from now()),

    -- 外键约束
    FOREIGN KEY (service_id) REFERENCES qs_service_definition (service_id),
    FOREIGN KEY (patient_id) REFERENCES patient_basic_info (id),
    FOREIGN KEY (encounter_id) REFERENCES encounter (id)
);

COMMENT ON TABLE qs_service_queue IS '患者队列实时状态';
COMMENT ON COLUMN qs_service_queue.serial_number IS 'A001格式';
COMMENT ON COLUMN qs_service_queue.checkin_time IS '签到时间(Unix时间戳)';
COMMENT ON COLUMN qs_service_queue.current_score IS '当前动态分';

-- 索引
CREATE INDEX idx_qs_queue_service ON qs_service_queue (service_id);
CREATE INDEX idx_qs_queue_status ON qs_service_queue (status);
CREATE INDEX idx_qs_queue_score ON qs_service_queue (current_score DESC);
CREATE INDEX idx_qs_queue_patient ON qs_service_queue (patient_id);
CREATE INDEX idx_qs_queue_serial ON qs_service_queue (serial_number);

-- 3. 队列事件日志表
DROP TABLE IF EXISTS qs_queue_log CASCADE;

CREATE TABLE qs_queue_log
(
    id              SERIAL PRIMARY KEY,
    log_id          INT UNIQUE NOT NULL,
    queue_id        INT        NOT NULL,
    service_id      INT        NOT NULL,
    event_type      VARCHAR(30)        NOT NULL CHECK (event_type IN (
                                                                      'CHECK_IN', 'CALL', 'RECALL', 'MISS',
                                                                      'SERVICE_START',
                                                                      'SERVICE_END', 'PRIORITY_CHANGE',
                                                                      'QUEUE_TRANSFER', 'AUTO_CANCEL'
        )),

    -- 事件详情
    event_time      INTEGER            NOT NULL,
    operator_id     INT,
    device_id       INT,
    previous_status VARCHAR(20),
    current_status  VARCHAR(20),

    -- 事件数据
    score_change    INTEGER,
    from_service_id INT,
    to_service_id   INT,
    wait_duration   INTEGER,

    -- 环境上下文
    queue_length    INTEGER,
    avg_wait_time   INTEGER,

    -- 时间戳
    created_at      INTEGER            NOT NULL DEFAULT extract(epoch from now()),

    -- 外键约束
    FOREIGN KEY (queue_id) REFERENCES qs_service_queue (queue_id),
    FOREIGN KEY (service_id) REFERENCES qs_service_definition (service_id),
    FOREIGN KEY (operator_id) REFERENCES providers (id)
);

COMMENT ON TABLE qs_queue_log IS '队列全生命周期事件日志';
COMMENT ON COLUMN qs_queue_log.event_type IS '事件类型：CHECK_IN(签到), CALL(叫号), RECALL(重呼), MISS(过号), SERVICE_START(服务开始), SERVICE_END(服务结束), PRIORITY_CHANGE(优先级变更), QUEUE_TRANSFER(队列转移), AUTO_CANCEL(自动取消)';
COMMENT ON COLUMN qs_queue_log.wait_duration IS '等待时长(分钟)';

-- 索引
CREATE INDEX idx_qs_log_event_type ON qs_queue_log (event_type);
CREATE INDEX idx_qs_log_time ON qs_queue_log (event_time);
CREATE INDEX idx_qs_log_service ON qs_queue_log (service_id);
CREATE INDEX idx_qs_log_queue ON qs_queue_log (queue_id);

-- -- 4. 优先级规则表
-- CREATE TABLE qs_priority_rule
-- (
--        id                 INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,

--     rule_id              INT UNIQUE NOT NULL,
--     rule_name            VARCHAR(100)       NOT NULL,
--     service_id           INT,

--     -- 规则配置
--     condition_expression JSONB              NOT NULL,
--     score_adjustment     INTEGER            NOT NULL,
--     effect_duration      INTEGER,

--     -- 执行控制
--     execution_order      INTEGER            NOT NULL DEFAULT 0,
--     is_active            BOOLEAN            NOT NULL DEFAULT TRUE,

--     -- 时间戳
--     created_at           INTEGER            NOT NULL DEFAULT extract(epoch from now()),
--     updated_at           INTEGER            NOT NULL DEFAULT extract(epoch from now()),

--     -- 外键约束
--     FOREIGN KEY (service_id) REFERENCES qs_service_definition (service_id)
-- );

-- COMMENT ON TABLE qs_priority_rule IS '动态优先级计算规则';
-- COMMENT ON COLUMN qs_priority_rule.condition_expression IS '条件表达式(JSONB结构)';
-- COMMENT ON COLUMN qs_priority_rule.score_adjustment IS '分数调整值';

-- -- 索引
-- CREATE INDEX idx_qs_rule_service ON qs_priority_rule (service_id);
-- CREATE INDEX idx_qs_rule_active ON qs_priority_rule (is_active);
-- CREATE INDEX idx_qs_rule_order ON qs_priority_rule (execution_order);

-- -- 5. 服务状态快照表
-- CREATE TABLE qs_service_snapshot
-- (
--     id                  SERIAL PRIMARY KEY,
--     snapshot_id         INT UNIQUE NOT NULL,
--     service_id          INT        NOT NULL,
--     snapshot_time       INTEGER            NOT NULL,

--     -- 队列指标
--     waiting_count       INTEGER            NOT NULL DEFAULT 0,
--     avg_wait_time       INTEGER,
--     max_wait_time       INTEGER,
--     served_count        INTEGER,

--     -- 性能指标
--     avg_service_time    INTEGER,
--     current_utilization DECIMAL(5, 2),

--     -- 时间戳
--     created_at          INTEGER            NOT NULL DEFAULT extract(epoch from now()),

--     -- 外键约束
--     FOREIGN KEY (service_id) REFERENCES qs_service_definition (service_id)
-- );

-- COMMENT ON TABLE qs_service_snapshot IS '服务队列状态历史快照';
-- COMMENT ON COLUMN qs_service_snapshot.current_utilization IS '资源利用率(%)';

-- -- 索引
-- CREATE INDEX idx_qs_snapshot_service ON qs_service_snapshot (service_id);
-- CREATE INDEX idx_qs_snapshot_time ON qs_service_snapshot (snapshot_time);

-- -- 6. 队列显示屏配置表
-- CREATE TABLE qs_display_config
-- (
--     id               SERIAL PRIMARY KEY,
--     config_id        INT UNIQUE NOT NULL,
--     service_id       INT        NOT NULL,
--     display_name     VARCHAR(100)       NOT NULL,
--     display_type     VARCHAR(20)        NOT NULL CHECK (display_type IN ('WAITING_AREA', 'DOCTOR_ROOM', 'COUNTER')),
--     display_location VARCHAR(200),

--     -- 显示配置
--     show_fields      JSONB              NOT NULL DEFAULT '{
--       "show_serial": true,
--       "show_name": false,
--       "show_wait_count": true
--     }',
--     refresh_interval INTEGER            NOT NULL DEFAULT 5,

--     -- 状态控制
--     is_active        BOOLEAN            NOT NULL DEFAULT TRUE,

--     -- 时间戳
--     created_at       INTEGER            NOT NULL DEFAULT extract(epoch from now()),
--     updated_at       INTEGER            NOT NULL DEFAULT extract(epoch from now()),

--     -- 外键约束
--     FOREIGN KEY (service_id) REFERENCES qs_service_definition (service_id)
-- );

-- COMMENT ON TABLE qs_service_snapshot IS '队列显示屏配置';
-- COMMENT ON COLUMN qs_display_config.show_fields IS '显示字段配置(JSONB)';

-- -- 索引
-- CREATE INDEX idx_qs_display_service ON qs_display_config (service_id);
-- CREATE INDEX idx_qs_display_type ON qs_display_config (display_type);

-- -- 7. 队列语音配置表
-- CREATE TABLE qs_voice_config
-- (
--     id             SERIAL PRIMARY KEY,
--     config_id      INT UNIQUE NOT NULL,
--     service_id     INT        NOT NULL,
--     voice_template VARCHAR(500)       NOT NULL DEFAULT '请{serial_number}号到{location}就诊',
--     voice_language VARCHAR(10)        NOT NULL DEFAULT 'zh-CN',
--     voice_speed    INTEGER            NOT NULL DEFAULT 5 CHECK (voice_speed BETWEEN 1 AND 10),
--     repeat_count   INTEGER            NOT NULL DEFAULT 2 CHECK (repeat_count BETWEEN 1 AND 5),

--     -- 状态控制
--     is_active      BOOLEAN            NOT NULL DEFAULT TRUE,

--     -- 时间戳
--     created_at     INTEGER            NOT NULL DEFAULT extract(epoch from now()),
--     updated_at     INTEGER            NOT NULL DEFAULT extract(epoch from now()),

--     -- 外键约束
--     FOREIGN KEY (service_id) REFERENCES qs_service_definition (service_id)
-- );

-- COMMENT ON TABLE qs_voice_config IS '队列语音播报配置';

-- -- 索引
-- CREATE INDEX idx_qs_voice_service ON qs_voice_config (service_id);

-- -- 创建更新触发器函数
-- CREATE OR REPLACE FUNCTION update_updated_at_column()
--     RETURNS TRIGGER AS
-- $$
-- BEGIN
--     NEW.updated_at = extract(epoch from now());
--     RETURN NEW;
-- END;
-- $$ language 'plpgsql';

-- -- 为需要自动更新updated_at的表创建触发器
-- CREATE TRIGGER update_qs_service_definition_updated_at
--     BEFORE UPDATE
--     ON qs_service_definition
--     FOR EACH ROW
-- EXECUTE FUNCTION update_updated_at_column();

-- CREATE TRIGGER update_qs_service_queue_updated_at
--     BEFORE UPDATE
--     ON qs_service_queue
--     FOR EACH ROW
-- EXECUTE FUNCTION update_updated_at_column();

-- CREATE TRIGGER update_qs_priority_rule_updated_at
--     BEFORE UPDATE
--     ON qs_priority_rule
--     FOR EACH ROW
-- EXECUTE FUNCTION update_updated_at_column();

-- CREATE TRIGGER update_qs_display_config_updated_at
--     BEFORE UPDATE
--     ON qs_display_config
--     FOR EACH ROW
-- EXECUTE FUNCTION update_updated_at_column();

-- CREATE TRIGGER update_qs_voice_config_updated_at
--     BEFORE UPDATE
--     ON qs_voice_config
--     FOR EACH ROW
-- EXECUTE FUNCTION update_updated_at_column();

-- -- 创建视图：当前队列状态
-- CREATE VIEW v_current_queue_status AS
-- SELECT sd.service_id,
--        sd.service_name,
--        sd.prefix,
--        COUNT(CASE WHEN sq.status = 'WAITING' THEN 1 END)              as waiting_count,
--        COUNT(CASE WHEN sq.status = 'CALLED' THEN 1 END)               as called_count,
--        COUNT(CASE WHEN sq.status = 'IN_SERVICE' THEN 1 END)           as serving_count,
--        MIN(CASE WHEN sq.status = 'WAITING' THEN sq.serial_number END) as next_number,
--        AVG(sq.estimated_wait_time)                                    as avg_wait_time
-- FROM qs_service_definition sd
--          LEFT JOIN qs_service_queue sq ON sd.service_id = sq.service_id
--     AND sq.status IN ('WAITING', 'CALLED', 'IN_SERVICE')
--     AND sq.created_at >= extract(epoch from date_trunc('day', now()))
-- GROUP BY sd.service_id, sd.service_name, sd.prefix;

-- COMMENT ON VIEW v_current_queue_status IS '当前队列状态视图';

-- -- 创建视图：队列性能统计
-- CREATE VIEW v_queue_performance AS
-- SELECT sd.service_id,
--        sd.service_name,
--        DATE_TRUNC('hour', to_timestamp(sq.created_at))  as hour_slot,
--        COUNT(*)                                         as total_served,
--        AVG(sq.finish_time - sq.service_start_time)      as avg_service_time,
--        AVG(sq.service_start_time - sq.checkin_time)     as avg_wait_time,
--        COUNT(CASE WHEN sq.status = 'MISSED' THEN 1 END) as missed_count
-- FROM qs_service_queue sq
--          JOIN qs_service_definition sd ON sq.service_id = sd.service_id
-- WHERE sq.finish_time IS NOT NULL
--   AND sq.created_at >= extract(epoch from now() - INTERVAL '7 days')
-- GROUP BY sd.service_id, sd.service_name, DATE_TRUNC('hour', to_timestamp(sq.created_at))
-- ORDER BY hour_slot DESC;

-- COMMENT ON VIEW v_queue_performance IS '队列性能统计视图';
