import { AuthConfig, AuthClient } from '@uap/oidc-client'
import { useAuthInfo } from './useAuthInfo'
import type { AuthConfigParam, SubMap, AuthTokenType } from '@uap/oidc-client'

function createUseAuthHook () {
  const auth: {
    config: AuthConfigParam | null,
    client: AuthClient | null,
  } = {
    config: null,
    client: null,
  }

  return function () {
    const { authInfo } = useAuthInfo()

    function initializeAuthConfig () {
      if (auth.config) return

      auth.config = new AuthConfig({
        provider: `${authInfo.authServerUrl}v2/`,
        clientId: authInfo.uapClientId,
        loginCallback: `${window.location.origin}/uhr/login-callback`,
        logoutRedirect: window.location.origin,
        onError: () => {},
      })
    }

    function initializeAuthClient () {
      if (auth.client) return

      const subscribes: SubMap = new Map()
      subscribes.set('/change-tenant', (response, client) => {
        const res = JSON.parse(response.body)
        if (!res)
          return

        const handleUserTenant = sessionStorage.getItem('changeTenantSuccess')
        sessionStorage.removeItem('changeTenantSuccess')
        if (handleUserTenant === res.tenantId) {
          return
        }

        client.silentRenew()
        localStorage.setItem('tenantId', res.data)

        client.getAuthToken().then((authToken) => {
          if (authToken) {
            checkLogin(client, authToken)
          }
        })
      })

      subscribes.set('/logout', (_, client) => {
        saveRedirectUrl(true)
        clearAuthLoginInfo()
        client.localClear()
      })

      auth.client = new AuthClient(auth.config, {
        socketAuth: {
          useSocket: true,
          socketProvider: `${authInfo.nettySocket}v1/`,
          subscribes,
        },
      })
    }

    function clearAuthLoginInfo () {
      localStorage.removeItem('userId')
      localStorage.removeItem('tenantId')
    }
  
    function saveRedirectUrl (isLogout = false) {
      if (sessionStorage.getItem('redirectUrlLogout')) {
        sessionStorage.removeItem('redirectUrlLogout')
        return
      }
  
      sessionStorage.setItem(
        'redirectUrl',
        window.location.href
          .replace(window.location.origin, '')
          .replace('/zh-cn', '')
          .replace('/en-us', '')
      )
  
      if (isLogout) {
        sessionStorage.setItem('redirectUrlLogout', 'true')
      }
    }
  
    function checkLogin (client: AuthClient, authToken: AuthTokenType) {
      const localUserId = localStorage.getItem('userId')
      const localTenantId = localStorage.getItem('tenantId')
      if (
        authToken &&
        (authToken.getUserId() !== localUserId ||
          (localTenantId && authToken.profile.tenantId !== localTenantId))
      ) {
        client.logout()
        saveRedirectUrl()
        client.login()
        return false
      }
      return true
    }

    return {
      auth,
      clearAuthLoginInfo,
      saveRedirectUrl,
      checkLogin,
      initializeAuthConfig,
      initializeAuthClient,
    }
  }
}

export const useAuth = createUseAuthHook()
