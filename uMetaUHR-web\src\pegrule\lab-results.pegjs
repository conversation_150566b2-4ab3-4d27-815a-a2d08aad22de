{
  function pad0(x, n){

  }
  // 辅助函数将报告对象数组按 reportTime 分组，并转换为 JSONL 格式字符串
  function toJSONLGroupedByTime(reports) {
    const grouped = {};
    reports.forEach(report => {
      if (!grouped[report.reportTime]) {
        grouped[report.reportTime] = [];
      }
      grouped[report.reportTime].push({
        reportName: report.reportName,
        content: report.content
      });
    });
    return Object.keys(grouped).map(time => JSON.stringify({
      reportTime: time,
      reports: grouped[time]
    })).join('\n');
  }
}

Start
  = reports:Report+ { return reports; }

Report
  = reportTime:Date ws reportName:ReportName ws [：:] ws content:Content {
      return {reportTime,reportName,content};
    }
    /.
    ;

Date
  = year:[0-9]+ [/-] month:[0-9]+ [/-] day:[0-9]+ {
      return (+year.join('') * 100+ +month.join(''))*100 + +day.join('');
    }


ReportName
  = name:[^：:]+ { return name.join('').trim(); }

Content
  = contentText:(!Date x:.)+ {
      // 去除结尾的 Separator（如果有的话）
      return contentText.map(x=>x[1]).join("")
    }

Separator
  = ws:" "*

ws
  = [ \t\n\r]*