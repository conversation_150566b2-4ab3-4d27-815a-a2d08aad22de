CREATE TABLE order_template
(
    id                     INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    template_name          VARCHAR(100) NOT NULL,
    template_type          VARCHAR(50)  NOT NULL,
    description            TEXT,

    -- Applicability settings
    applicable_departments VARCHAR(50)[],
    applicable_conditions  JSONB,

    -- Template content
    orders                 JSONB        NOT NULL,

    -- Access control
    owner_id               INT  NOT NULL,
    access_control         JSONB,

    -- Status management
    status                 VARCHAR(20) DEFAULT 'ACTIVE',
    version                VARCHAR(20)  NOT NULL,
    parent_template_id     INT,

    -- Usage statistics
    usage_count            INT         DEFAULT 0,
    last_used              BIGINT,

    created_at             BIGINT      DEFAULT EXTRACT(EPOCH FROM NOW()),
    updated_at             BIGINT      DEFAULT EXTRACT(EPOCH FROM NOW()),

    FOREIGN KEY (parent_template_id) REFERENCES order_template (id)
);

COMMENT ON TABLE order_template IS '医嘱模板定义表';
COMMENT ON COLUMN order_template.template_name IS '模板名称';
COMMENT ON COLUMN order_template.template_type IS '模板类型(SYSTEM-系统/DEPARTMENT-科室/PERSONAL-个人)';
COMMENT ON COLUMN order_template.description IS '模板描述';
COMMENT ON COLUMN order_template.applicable_departments IS '适用科室ID列表';
COMMENT ON COLUMN order_template.applicable_conditions IS '适用临床条件(JSON格式)';
COMMENT ON COLUMN order_template.orders IS '模板包含的医嘱项目';
COMMENT ON COLUMN order_template.owner_id IS '所有者ID(系统/科室ID/用户ID)';
COMMENT ON COLUMN order_template.access_control IS '访问控制设置(JSON格式)';
COMMENT ON COLUMN order_template.status IS '模板状态(ACTIVE-启用/INACTIVE-停用/DRAFT-草稿)';
COMMENT ON COLUMN order_template.version IS '模板版本号';
COMMENT ON COLUMN order_template.parent_template_id IS '父模板ID(用于版本控制)';
COMMENT ON COLUMN order_template.usage_count IS '使用次数统计';
COMMENT ON COLUMN order_template.last_used IS '最后使用时间';
COMMENT ON COLUMN order_template.created_at IS '记录创建时间';
COMMENT ON COLUMN order_template.updated_at IS '记录更新时间';

-- Indexes
CREATE INDEX idx_template_type ON order_template (template_type);
CREATE INDEX idx_template_owner ON order_template (owner_id);
CREATE INDEX idx_template_departments ON order_template USING GIN (applicable_departments);
