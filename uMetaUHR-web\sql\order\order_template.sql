CREATE TABLE order_template
(
    id                     INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    template_name          VARCHAR(100) NOT NULL,
    template_type          VARCHAR(50)  NOT NULL,
    description            TEXT,

    -- Applicability settings
    applicable_departments VARCHAR(50)[],
    applicable_conditions  JSONB,

    -- Template content
    orders                 JSONB        NOT NULL,

    -- Access control
    owner_id               INT  NOT NULL,
    access_control         JSONB,

    -- Status management
    status                 VARCHAR(20) DEFAULT 'ACTIVE',
    version                VARCHAR(20)  NOT NULL,
    parent_template_id     INT,

    -- Usage statistics
    usage_count            INT         DEFAULT 0,
    last_used              BIGINT,

    created_at             BIGINT      DEFAULT EXTRACT(EPOCH FROM NOW()),
    updated_at             BIGINT      DEFAULT EXTRACT(EPOCH FROM NOW()),

    FOREIGN KEY (parent_template_id) REFERENCES order_template (id)
);

COMMENT ON TABLE order_template IS 'Order template definitions';
COMMENT ON COLUMN order_template.template_name IS 'Template name';
COMMENT ON COLUMN order_template.template_type IS 'Template type (SYSTEM/DEPARTMENT/PERSONAL)';
COMMENT ON COLUMN order_template.description IS 'Template description';
COMMENT ON COLUMN order_template.applicable_departments IS 'Applicable department IDs';
COMMENT ON COLUMN order_template.applicable_conditions IS 'Applicable clinical conditions in JSON format';
COMMENT ON COLUMN order_template.orders IS 'Order items included in template';
COMMENT ON COLUMN order_template.owner_id IS 'Owner ID (system/department_id/user_id)';
COMMENT ON COLUMN order_template.access_control IS 'Access control settings in JSON format';
COMMENT ON COLUMN order_template.status IS 'Template status (ACTIVE/INACTIVE/DRAFT)';
COMMENT ON COLUMN order_template.version IS 'Template version';
COMMENT ON COLUMN order_template.parent_template_id IS 'Parent template ID for versioning';
COMMENT ON COLUMN order_template.usage_count IS 'Usage count';
COMMENT ON COLUMN order_template.last_used IS 'Last used timestamp';
COMMENT ON COLUMN order_template.created_at IS 'Timestamp when record was created';
COMMENT ON COLUMN order_template.updated_at IS 'Timestamp when record was last updated';

-- Indexes
CREATE INDEX idx_template_type ON order_template (template_type);
CREATE INDEX idx_template_owner ON order_template (owner_id);
CREATE INDEX idx_template_departments ON order_template USING GIN (applicable_departments);
