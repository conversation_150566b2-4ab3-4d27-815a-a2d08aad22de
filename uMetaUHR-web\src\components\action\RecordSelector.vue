<template>
  <div v-if="isVisible" class="input-text">
    <label>
      <img
        v-if="props?.schema?.selfIcon && !isFocused"
        :src="`${BASE}${props?.schema?.selfIcon}`"
        class="input-icon"
        :style="props?.schema?.style?.selfIcon"
      />
      <input
        ref="inputRef"
        :class="{ 'input-error': error }"
        :type="props?.schema?.inputType ?? 'text'"
        :value="inputValue"
        @input="handleInput($event)"
        @keydown="handleKeyDown"
        @focus="handleInputFocus"
        @blur="handleInputBlur"
        autocomplete="off"
        maxlength="20"
        :placeholder="props?.schema?.placeholder || '输入记录名称'"
      />
    </label>
    <span v-if="error" :title="error" class="error"> * </span>

    <!-- Teleport dropdown to body -->
    <teleport to="body">
      <div
        v-if="showDropdown"
        :style="dropdownStyle"
        class="autocomplete-results"
        role="listbox"
      >
        <table v-if="suggestions.length > 0">
          <thead>
            <tr>
              <th
                v-for="(col, colIndex) in props.schema?.columnDef"
                :key="col.field + colIndex"
                :style="col?.style"
              >
                {{ col.displayName || col.field }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="(suggestion, index) in suggestions"
              :key="suggestion.id"
              ref="suggestionRefs"
              :class="{ selected: index === selectedIndex }"
              role="option"
              @mousedown="selectSuggestion(suggestion)"
            >
              <td v-for="column in props.schema.columnDef" :key="column.field">
                <span v-html="getDisplay(column, suggestion)"></span>
              </td>
            </tr>
          </tbody>
        </table>

        <div v-else class="empty-placeholder">
          <div class="placeholder-content">
            <img :src="`${BASE}${'/noData.png'}`" />
            <p>{{ props?.schema?.noDataText }}</p>
          </div>
        </div>
      </div>
    </teleport>
  </div>
</template>

<script lang="ts" setup>
import { computed, defineProps, inject, ref, watch, onMounted, onUnmounted, watchEffect, getCurrentInstance } from 'vue'
import { evaluateShowWhen, getNestedValue, setNestedValue, getContext } from '@/lib/getContext'
import useValidation from '../mixins/validationMixin'
import { useEventBus } from '@/components/mixins/eventBus'
import { Debouncer } from '@/lib/Debouncer'

// --- Component Props ---
const props = defineProps({
  schema: {
    type: Object,
    required: true,
    default: () => ({
      label: '',
      model: '',
      inputType: 'text',
      columnDef: [],
      event: { selectSuggestion: '' }
    })
  },
  context: { type: Object, required: true }
})

// --- Refs and Reactive State ---

interface TypesenseHit {
  id: string; // id 字段在 data 对象内部
  [key: string]: unknown; // 其他任意字段
}

interface Suggestion {
  id: string
  data: Record<string, unknown>
}

const BASE = import.meta.env.BASE_URL || ''

const suggestions = ref<Suggestion[]>([])
const selectedIndex = ref(-1)
const inputRef = ref<HTMLInputElement | null>(null)
const dropdownPosition = ref({ top: 0, left: 0, width: 0 })
const suggestionRefs = ref<HTMLElement[]>([])
const inputValue = ref('')
const isFocused = ref(false);
const isOpenModal = ref(false);
const emit = defineEmits(['focus'])

// --- Computed Properties ---
const isVisible = computed(() => evaluateShowWhen(props))
const { error, validate } = useValidation()

const comp = getCurrentInstance()
const eventBus = useEventBus()
const callBackWithContext = (data: any) => {
  let { cb } = data || {}
  if (!cb) return
  cb({ comp: comp?.exposed || {} })
}

const showDropdown = computed(() => {
  // 当有输入值，且长度大于1，不是打开模态框状态，且输入框获得焦点时显示下拉框
  return inputValue.value && inputValue.value.length > 1 && !isOpenModal.value && isFocused.value;
});

const dropdownStyle = computed(() => {
  const rect = inputRef.value?.getBoundingClientRect()
  const windowHeight = window.innerHeight
  const dropdownHeight = Math.min((suggestions?.value?.length + 1) * 25, 150)
  const wouldOverflowBottom = rect && rect.bottom + dropdownHeight > windowHeight

  return wouldOverflowBottom
    ? {
        bottom: `${windowHeight - rect.top + 5}px`,
        left: `${dropdownPosition.value.left}px`,
        width: `${dropdownPosition.value.width}px`
      }
    : {
        top: `${dropdownPosition.value.top}px`,
        left: `${dropdownPosition.value.left}px`,
        width: `${dropdownPosition.value.width}px`
      }
})

// --- Injected Dependencies ---
const queryCondition = inject(
  props.schema?.event?.queryCondition || 'query-condition',
  () => 1
) as Function;

const selectSuggestionEvent = inject(
  props.schema?.event?.selectSuggestion || 'select-suggestion',
  () => 1
) as Function;

// --- Constants ---
const KEY_ACTIONS = {
  TAB: 'Tab',
  ARROW_DOWN: 'ArrowDown',
  ARROW_UP: 'ArrowUp',
  ENTER: 'Enter',
  ESCAPE: 'Escape'
} as const

// --- Dropdown Positioning ---
function updateDropdownPosition() {
  if (!inputRef.value) return

  const rect = inputRef.value.getBoundingClientRect()
  const windowHeight = window.innerHeight
  const dropdownHeight = Math.min((suggestions?.value?.length + 1) * 25, 150)
  const wouldOverflowBottom = rect.bottom + dropdownHeight > windowHeight

  dropdownPosition.value = {
    top: wouldOverflowBottom
      ? rect.top + window.scrollY - dropdownHeight - 5
      : rect.bottom + window.scrollY,
    left: rect.left + window.scrollX,
    width: rect.width
  }
}

// --- Suggestion Handling ---
async function fetchSuggestions(query = '') {
  if (!query.trim()) {
    suggestions.value = []
    return
  }

  let results = await queryCondition(query)
  suggestions.value = (results as TypesenseHit[] || []).map(result => ({
    id: result.id,
    data: result,
  }))
}

function getDisplay(column: { field: string }, suggestion: Suggestion) {
  const { field } = column
  const query = inputValue.value.toLowerCase().trim();
  const value = getNestedValue(suggestion.data, field)
  if (!query || !value) return value;

  // 转义正则特殊字符
  const escapedQuery = query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  const regex = new RegExp(`(${escapedQuery})`, 'gi');

  return value.toString().replace(
    regex,
    `<mark style="color: #202020; font-weight: 700; background-color: #e8eeff;">$1</mark>`
  );
}

async function selectSuggestion(suggestion: Suggestion) {
  isOpenModal.value = true
  setNestedValue(props.context, props.schema.model, suggestion.data)
  let tempSuggestions = suggestions.value
  suggestions.value = []
  let saveFlag = await selectSuggestionEvent()
  if (saveFlag) {
    // 用户点击了保存
    // 清空输入值
    inputValue.value = ''
    isFocused.value = false
    emit('focus')
  } else {
    // 用户点击了取消
    // 恢复原来的建议列表
    suggestions.value = tempSuggestions
    // 通过 schema 控制是否聚焦
    if (props.schema?.autoFocus) {
      inputRef.value?.focus()
    }
  }
  isOpenModal.value = false
}

// --- Keyboard Navigation ---
function handleKeyDown(event: KeyboardEvent) {
  if (suggestions.value.length === 0) return
  switch (event.key) {
    case KEY_ACTIONS.TAB:
    case KEY_ACTIONS.ARROW_DOWN:
    case KEY_ACTIONS.ARROW_UP:
      event.preventDefault()
      handleArrowNavigation(event.key === KEY_ACTIONS.ARROW_UP ? -1 : 1)
      break

    case KEY_ACTIONS.ENTER:
      event.preventDefault()
      selectHighlightedSuggestion()
      break

    case KEY_ACTIONS.ESCAPE:
      event.preventDefault()
      clearSuggestions()
      break
  }
}

function handleArrowNavigation(direction: -1 | 1) {
  const newIndex =
    (selectedIndex.value + direction + suggestions.value.length) % suggestions.value.length
  selectedIndex.value = newIndex
  scrollIntoView(suggestionRefs.value[newIndex])
}

function selectHighlightedSuggestion() {
  if (!suggestionRefs.value.length) return
  const safeIndex = Math.max(0, Math.min(selectedIndex.value, suggestions.value.length - 1))
  selectSuggestion(suggestions.value[safeIndex])
}

function handleInputFocus() {
  isFocused.value = true
  emit('focus')
  if (inputValue.value) {
    fetchSuggestions(inputValue.value)
  }
}

function handleInputBlur() {
  if (isOpenModal.value) {
    return
  }
  isFocused.value = false
  clearSuggestions()
}

function clearSuggestions() {
  suggestions.value = []
  selectedIndex.value = -1
}

function scrollIntoView(target: HTMLElement | null) {
  target?.scrollIntoView?.({ behavior: 'smooth', block: 'nearest' })
}

// --- Event Handlers ---
async function handleInput(event: Event) {
  const value = (event.target as HTMLInputElement)?.value
  if (value == null) return
  inputValue.value = value
  if (value.length < 2) {
    suggestions.value = []
    return;
  }
  await debouncedFetchSuggestions(value)
}

const debouncedFetchSuggestions = new Debouncer(300).debounce(async (value: string) => {
  await fetchSuggestions(value)
})

function clearInputValue() {
  inputValue.value = ''
}

onMounted(async () => {
  if (props.schema?.event?.messageEndPoint) {
      eventBus.on(props.schema?.event?.messageEndPoint, callBackWithContext)
  }
})

onUnmounted(() => {
  if (props.schema?.event?.messageEndPoint) {
      eventBus.on(props.schema?.event?.messageEndPoint, callBackWithContext)
  }
})

watchEffect(() => {
  if (props.schema?.valid?.required) {
    validate(inputValue.value, {
      required: props.schema.valid.required
    })
  }
})

// --- Watchers ---
watch(suggestions, () => {
  updateDropdownPosition()
  if (suggestions.value.length > 0) {
    selectedIndex.value = 0
  }
})

defineExpose({ compName: 'recordSelector', clearInputValue })
</script>

<style lang="scss" scoped>
.input-text {
  display: flex;
  flex-wrap: nowrap;
  margin: 2px;
  position: relative;

  .input-icon {
    width: 20px;
    height: 20px;
    background: white;
  }

  label {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    flex: 1 1 auto;
    background: white;

    span {
      min-width: 5rem;
      white-space: nowrap;
      text-align: right;
      padding-right: 0.5rem;
    }

    input {
      flex: 1;
      width: 100%;
      min-width: 5rem;
      border-radius: 0.2rem;
      border: none;

      &:focus {
        border: 1px solid #365FD9;
      }
    }
  }

  .error {
    color: red;
    margin-left: 0.5rem;
  }

  .input-error {
    border-color: red;
  }
}

.autocomplete-results {
  position: absolute;
  background-color: white;
  border: 1px solid #ccc;
  border-radius: 0.2rem;
  max-height: 440px;
  overflow-y: auto;
  z-index: 10000;

  .empty-placeholder {
    height: 160px;
    text-align: center;
    vertical-align: middle;

    .placeholder-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 20px;

      img {
        width: 80px;
        height: 80px;
        margin-bottom: 16px;
      }

      p {
        color: #999;
        font-size: 14px;
        margin: 0;
      }
    }
  }

  table {
    width: 100%;
    border-collapse: collapse;

    thead {
      background: rgba(54,95,217,0.08);
      pointer-events: none;
    }

    th,
    td {
      border: 1px solid #ddd;
      text-align: left;
      padding-left: 10px;
      font-size: 14px;
    }

    tr {
      height: 40px;
    }

    tr:hover {
      background: #E8EEFF;
      border: 1px solid #5077e2;
      cursor: pointer;
    }

    .selected {
      border: 2px solid #5077e2;
    }

    .selected:hover {
      border: 2px solid #5077e2;
    }

  }
}
</style>
