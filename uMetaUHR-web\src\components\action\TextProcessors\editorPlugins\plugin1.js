// src/myplugin.js

export default async function MyPlugin(editor) {
  // You can directly interact with the editor instance
  // without extending the Plugin class.

  delete globalThis.CKEDITOR_VERSION
  let ButtonView = await import('@ckeditor/ckeditor5-ui/src/button/buttonview.js')

  function F(editor2) {
    editor2.ui.componentFactory.add('myButton', (locale) => {
      console.log('here we go')
      const button = new ButtonView(locale)
      button.set({
        label: 'My Button',
        icon: '<svg>...</svg>',
        tooltip: true
      })
      button.on('execute', () => {
        alert('Button clicked!')
      })

      return button
    })
  }

  return F
}
