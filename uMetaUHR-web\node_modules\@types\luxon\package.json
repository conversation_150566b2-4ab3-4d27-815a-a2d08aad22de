{"name": "@types/luxon", "version": "3.6.2", "description": "TypeScript definitions for luxon", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/luxon", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "carsonf", "url": "https://github.com/carsonf"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/luxon"}, "scripts": {}, "dependencies": {}, "peerDependencies": {}, "typesPublisherContentHash": "15d32f46de44a489139cf391deb427aab536333e5844ff49714b39e44a4ecd4d", "typeScriptVersion": "5.1"}