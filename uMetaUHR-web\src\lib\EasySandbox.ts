type Hook = (sandbox: EasySandbox) => Promise<void> | void // 生命周期钩子类型
type ErrorHandler = (error: Error) => void // 错误处理函数类型

interface EasySandboxOptions {
  sharedState?: Record<string, any> // 共享状态
  utilities?: Record<string, Function> // 工具函数
  debug?: boolean // 调试模式
  beforeRun?: Hook // 前置钩子
  afterRun?: Hook // 后置钩子
  onError?: ErrorHandler // 错误处理
}

class EasySandbox {
  private active = false // 是否激活
  private sandboxData: Record<string, any> = {} // 沙箱内部数据
  private proxy: Window // 沙箱代理

  // 配置相关
  private sharedState: Record<string, any> // 共享状态
  private utilities: Record<string, Function> // 工具函数
  private logsEnabled: boolean // 是否启用日志
  private hooks: { beforeRun: Hook; afterRun: Hook } // 钩子
  private onError: ErrorHandler // 错误处理函数

  constructor(
    private name: string,
    options: EasySandboxOptions = {}
  ) {
    // 使用解构赋值初始化配置
    ;({
      sharedState: this.sharedState = {},
      utilities: this.utilities = {},
      debug: this.logsEnabled = false,
      onError: this.onError = this.defaultErrorHandler
    } = options)

    // 钩子默认值
    const nullFun = () => {}
    this.hooks = {
      beforeRun: options.beforeRun || nullFun,
      afterRun: options.afterRun || nullFun
    }

    // 初始化代理
    this.proxy = this.createProxy()
  }

  /**
   * 注册共享状态或工具方法
   */
  public register(key: string, value: any): void {
    if (typeof value === 'function') {
      this.utilities[key] = value.bind(this) // 绑定沙箱上下文
      this.log(`Registered utility: ${key}`)
    } else {
      this.sharedState[key] = value
      this.log(`Registered shared state: ${key}`)
    }
  }

  /**
   * 执行用户代码（支持异步代码）
   * @param code 用户代码
   * @param context 动态注入的上下文
   * @returns 返回用户代码执行结果
   */
  public async run<T = unknown>(
    code: string,
    context: Record<string, any> = {}
  ): Promise<T | void> {
    if (this.active) {
      throw new Error(`[EasySandbox: ${this.name}] Sandbox is already active.`)
    }

    this.activate()

    const proxy = new Proxy(this.proxy, {
      get: (target, key: string) => (key in context ? context[key] : target[key as keyof Window])
    })

    try {
      await this.safeHook(this.hooks.beforeRun)
      const executeCode = new Function('window', code) // 使用沙箱代理执行代码
      const result = await executeCode(proxy)
      await this.safeHook(this.hooks.afterRun)
      return result as T
    } catch (error) {
      this.onError(error as Error)
    } finally {
      this.deactivate()
    }
  }

  /**
   * 创建代理对象
   * 控制对 `window` 和沙箱上下文的访问
   */
  private createProxy(): Window {
    return new Proxy(window, {
      get: (target, key: string) => {
        if (key in this.sharedState) return this.sharedState[key]
        if (key in this.utilities) return this.utilities[key]
        if (key in this.sandboxData) return this.sandboxData[key]
        if (key in target) {
          const value = target[key as keyof Window]
          return typeof value === 'function' ? value.bind(target) : value
        }
        return undefined
      },
      set: (_, key: string, value) => {
        this.sandboxData[key] = value
        this.log(`Set ${key} = ${value}`)
        return true
      }
    })
  }

  /**
   * 默认错误处理函数
   */
  private defaultErrorHandler(error: Error) {
    console.error(`[EasySandbox: ${this.name}] Error:`, error)
  }

  /**
   * 执行钩子并保护错误
   */
  private async safeHook(hook: Hook): Promise<void> {
    try {
      await hook(this)
    } catch (error) {
      this.log(`Hook error: ${error}`)
    }
  }

  /**
   * 激活沙箱
   */
  private activate(): void {
    this.active = true
    this.log(`Activated`)
  }

  /**
   * 停用沙箱并清理资源
   */
  private deactivate(): void {
    this.clear()
    this.active = false
    this.log(`Deactivated`)
  }

  /**
   * 清理沙箱数据
   */
  private clear(): void {
    this.sandboxData = {}
    this.log(`Cleared sandbox data`)
  }

  /**
   * 日志工具
   */
  private log(message: string): void {
    if (this.logsEnabled) {
      console.log(`[EasySandbox: ${this.name}] ${message}`)
    }
  }
}

export default EasySandbox
