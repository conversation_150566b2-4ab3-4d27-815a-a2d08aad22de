/// <reference types="cypress" />

import { createOrderStore } from '@/stores'

describe('Order Store', () => {
  const initialData = {
    orders: [
      { id: 'o001', drug: 'Insulin', dose: '10IU', frequency: 'daily' }
    ],
    patients: [
      {
        id: 'p001',
        name: 'Patient One',
        orders: [{ id: 'o001', drug: 'Insulin', dose: '10IU', frequency: 'daily' }]
      }
    ],
    currentPatient: null
  }

  it('should initialize with provided data', () => {
    const store = createOrderStore(initialData)
    cy.wrap(store.data).should('deep.equal', initialData)
  })

  describe('Order Operations', () => {
    let store: ReturnType<typeof createOrderStore>

    beforeEach(() => {
      store = createOrderStore(initialData)
      store.switchPatient('p001')
    })

    it('should create new order', () => {
      store.createOrder({
        drug: 'Metformin',
        dose: '500mg',
        frequency: 'bid'
      })
      const orders = store.data.orders || []
      cy.wrap(orders).should('have.length', 2)
    })

    it('should create order for current patient', () => {
      store.createOrderForCurrentPatient({
        drug: 'Metformin',
        dose: '500mg',
        frequency: 'bid'
      })
      const orders = store.getCurrentPatientOrders()
      cy.wrap(orders).should('have.length', 2)
    })

    it('should update order dose', () => {
      store.updateDose('o001', '15IU')
      const order = store.data.orders?.find(o => o.id === 'o001')
      cy.wrap(order?.dose).should('equal', '15IU')
    })

    it('should delete order', () => {
      store.deleteOrder('o001')
      const orders = store.data.orders || []
      cy.wrap(orders).should('have.length', 0)
    })

    it('should delete order from current patient', () => {
      store.deleteOrderFromCurrentPatient('o001')
      const orders = store.getCurrentPatientOrders()
      cy.wrap(orders).should('have.length', 0)
    })

    it('should find order by id', () => {
      const order = store.data.currentPatient?.orders?.find(o => o.id === 'o001')
      cy.wrap(order).should('exist')
      if (order) {
        cy.wrap(order.drug).should('equal', 'Insulin')
      }
    })
  })
})
