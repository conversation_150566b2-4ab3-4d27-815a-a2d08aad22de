/* 
 * CSS Design System - Organized by Dimensions
 * Merged from style-guide.html, base.css, base2.css and existing main.css
 */

@import "handlebars.css";
@import "ant-design.css";

/* ==================== */
/* 1. CORE FOUNDATIONS */
/* ==================== */

/* 1.1 Variables & Theming */
:root {
    /* Color System - Merged from style-guide and base.css */
    --primary: #4361ee;
    --primary-light: #4895ef;
    --primary-dark: #3a0ca3;
    --secondary: #7209b7;
    --success: #4cc9f0;
    --danger: #f72585;
    --warning: #f8961e;
    --info: #2ec4b6;
    --light: #f8f9fa;
    --dark: #212529;
    --gray: #6c757d;
    --gray-light: #e9ecef;

    /* From base.css */
    --vt-c-white: #ffffff;
    --vt-c-white-soft: #f8f8f8;
    --vt-c-white-mute: #f2f2f2;
    --vt-c-black: #181818;
    --vt-c-black-soft: #222222;
    --vt-c-black-mute: #282828;
    --vt-c-indigo: #2c3e50;
    --vt-c-divider-light-1: rgba(60, 60, 60, 0.29);
    --vt-c-divider-light-2: rgba(60, 60, 60, 0.12);
    --vt-c-divider-dark-1: rgba(84, 84, 84, 0.65);
    --vt-c-divider-dark-2: rgba(84, 84, 84, 0.48);
    --vt-c-text-light-1: var(--vt-c-indigo);
    --vt-c-text-light-2: rgba(60, 60, 60, 0.66);
    --vt-c-text-dark-1: var(--vt-c-white);
    --vt-c-text-dark-2: rgba(235, 235, 235, 0.64);
    --color-background: var(--vt-c-white);
    --color-background-soft: var(--vt-c-white-soft);
    --color-background-mute: var(--vt-c-white-mute);
    --color-border: var(--vt-c-divider-light-2);
    --color-border-hover: var(--vt-c-divider-light-1);
    --color-heading: var(--vt-c-text-light-1);
    --color-text: var(--vt-c-text-light-1);
    --section-gap: 160px;
    --workspace-background-color: #D5E9FA;

    /* Spacing System */
    --space-xxs: 0.25rem;
    --space-xs: 0.5rem;
    --space-sm: 0.75rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-xxl: 3rem;

    /* Radius System */
    --radius-xs: 0.125rem;
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-full: 50rem;

    /* Typography System */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-xxl: 1.5rem;

    /* Transition System */
    --transition-speed: 0.2s;
    --transition-easing: cubic-bezier(0.4, 0, 0.2, 1);

    /* Shadow System */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

@media (prefers-color-scheme: dark) {
    :root {
        --color-background: var(--vt-c-black);
        --color-background-soft: var(--vt-c-black-soft);
        --color-background-mute: var(--vt-c-black-mute);
        --color-border: var(--vt-c-divider-dark-2);
        --color-border-hover: var(--vt-c-divider-dark-1);
        --color-heading: var(--vt-c-text-dark-1);
        --color-text: var(--vt-c-text-dark-2);
    }
}

[data-theme="epic"] {
    /*--test-color: red;*/
}

/* 1.2 Base Styles - Merged from all sources */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    font-weight: normal;
}

body {
    /*min-height: 100vh;  100vh will make browser vertically scroll */
    color: var(--color-text);
    background: var(--workspace-background-color);
    transition: color 0.5s, background-color 0.5s;
    line-height: 1.6;
    font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    font-size: 15px;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overscroll-behavior: none;
}

/* ==================== */
/* 2. UTILITY CLASSES */
/* ==================== */

/* 2.1 Layout Utilities */
.layout-block {
    display: block;
    width: 100%;
}

.layout-flex {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-xs);
}

/* 2.2 Spacing Utilities */
.p-xs {
    padding: var(--space-xs);
}

.p-sm {
    padding: var(--space-sm);
}

.p-md {
    padding: var(--space-md);
}

.p-lg {
    padding: var(--space-lg);
}

/* 2.3 Text Utilities */
.text-xs {
    font-size: var(--text-xs);
}

.text-sm {
    font-size: var(--text-sm);
}

.text-base {
    font-size: var(--text-base);
}

.text-lg {
    font-size: var(--text-lg);
}

/* From base2.css */
.input-error {
    border: 1px solid rgba(255, 0, 0, 0.51) !important;
}

.error {
    color: red;
}

strong {
    font-weight: bold;
    text-decoration: underline;
}

blockquote {
    font-style: italic;
    color: rgba(119, 7, 7, 0.95);
}

h1 {
    font-size: 10pt;
    color: #020251;
    text-decoration: underline;
    font-weight: bold;
    margin: 3pt 0 2pt 0;
}

h2 {
    font-size: 9pt;
    color: #020251;
    text-decoration: underline;
    margin: 3pt 0 2pt 0;
}

h3 {
    font-size: 9pt;
    text-decoration: underline;
    color: #020251;
    margin: 3pt 0 2pt 0;
}

p {
    word-break: break-word;
}

table.data {
    border-collapse: collapse;
    width: 100%;
    overflow: auto;
    border: 1px solid rgba(173, 216, 230, 0.63);
    max-height: 10rem;
}

.data td {
    border: 1px solid rgba(173, 216, 230, 0.64);
    font-size: 10pt;
}

.data th {
    position: sticky;
    background: lightskyblue;
    text-align: left;
}

textarea {
    border: 1px solid rgba(173, 216, 230, 0.63);
    border-radius: 5px;
    padding: 5px;
}

::-webkit-scrollbar {
    height: 6px;
    width: 6px;
}

::-webkit-scrollbar-track {
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background: lightgray;
}

/* ==================== */
/* 3. COMPONENT SYSTEM */
/* ==================== */

/* 3.1 Base Component */
.ui-component {
    display: block;
    padding: var(--space-sm) var(--space-md);
    font-size: var(--text-base);
    border: 1px solid transparent;
    border-radius: var(--radius-sm);
    transition: all var(--transition-speed) var(--transition-easing);
    cursor: pointer;
    outline: none;
    position: relative;
    overflow: hidden;
}

/* 3.2 Component Modifiers */
/* Size Modifiers */
.size-xs {
    padding: var(--space-xxs) var(--space-xs);
    font-size: var(--text-xs);
}

.size-sm {
    padding: var(--space-xs) var(--space-sm);
    font-size: var(--text-sm);
}

.size-lg {
    padding: var(--space-md) var(--space-lg);
    font-size: var(--text-lg);
}

.size-xl {
    padding: var(--space-lg) var(--space-xl);
    font-size: var(--text-xl);
}

/* Color Modifiers */
.color-primary {
    background-color: var(--primary);
    color: white;
}

.color-secondary {
    background-color: var(--secondary);
    color: white;
}

.color-success {
    background-color: var(--success);
    color: white;
}

.color-danger {
    background-color: var(--danger);
    color: white;
}

.color-warning {
    background-color: var(--warning);
    color: white;
}

.color-info {
    background-color: var(--info);
    color: white;
}

.color-light {
    background-color: var(--light);
    color: var(--dark);
    border-color: var(--gray-light);
}

.color-outline {
    background-color: transparent;
    border: 1px solid currentColor;
}

.color-ghost {
    background-color: transparent;
    color: currentColor;
}

/* State Modifiers */
.state-hover:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.state-active:active {
    transform: translateY(1px);
    box-shadow: var(--shadow-sm);
}

.state-disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

.state-loading::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Shape Modifiers */
.shape-rounded {
    border-radius: var(--radius-md);
}

.shape-pill {
    border-radius: var(--radius-full);
}

.shape-square {
    border-radius: var(--radius-xs);
}

/* ==================== */
/* 4. SPECIFIC COMPONENTS */
/* ==================== */

/* 4.1 Buttons */
.btn {
    text-align: center;
    font-weight: 500;
    user-select: none;
}

/* 4.2 Inputs */
.input {
    background-color: white;
    border: 1px solid var(--gray-light);
    color: var(--dark);
    cursor: text;
}

.input:focus {
    border-color: var(--primary-light);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.2);
}

/* 4.3 Cards */
.card {
    background-color: white;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow);
    overflow: hidden;
    padding: 0;
}

.card-header {
    padding: var(--space-md);
    background-color: var(--light);
    border-bottom: 1px solid var(--gray-light);
}

.card-title {
    /*padding: var(--space-md);*/
    font-size: var(--text-lg);
    font-weight: bold;
    background: none;
}

.card-body {
    padding: var(--space-md);
}

.card-footer {
    padding: var(--space-md);
    background-color: var(--light);
    border-top: 1px solid var(--gray-light);
}

/* 4.4 Select */
.select {
    background-color: white;
    border: 1px solid var(--gray-light);
    position: relative;
}

.select::after {
    content: "▼";
    position: absolute;
    right: var(--space-md);
    top: 50%;
    transform: translateY(-50%);
    font-size: 0.7em;
    pointer-events: none;
}

/* 4.5 Switch */
.switch {
    display: inline-block;
    width: 44px;
    height: 24px;
    position: relative;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--gray-light);
    transition: .4s;
    border-radius: var(--radius-full);
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--primary);
}

input:checked + .slider:before {
    transform: translateX(20px);
}

/* 4.6 Tag */
.tag {
    display: inline-flex;
    align-items: center;
    padding: var(--space-xxs) var(--space-sm);
    font-size: var(--text-xs);
    font-weight: 500;
    border-radius: var(--radius-full);
}

/* 4.7 Badge */
.badge {
    position: absolute;
    top: -6px;
    right: -6px;
    min-width: 20px;
    height: 20px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-xs);
    font-weight: bold;
    padding: 0 var(--space-xs);
}

/* 4.8 Tooltip */
.tooltip {
    position: relative;
}

.tooltip:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    margin-bottom: var(--space-xs);
    padding: var(--space-xxs) var(--space-sm);
    background-color: var(--dark);
    color: white;
    border-radius: var(--radius-sm);
    font-size: var(--text-xs);
    white-space: nowrap;
    z-index: 100;
}

/* ==================== */
/* 5. APPLICATION LAYOUT */
/* ==================== */

#app {
    margin: 0 auto;
    font-weight: normal;
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    font-family: Avenir, Helvetica, Arial, sans-serif;
}

/* ==================== */
/* 6. RESPONSIVE RULES */
/* ==================== */

@media (min-width: 1024px) {
    #app {
        display: grid;
        grid-template-columns: 1fr 1fr;
        padding: 0 2rem;
    }
}

select, input, textarea {
    outline: none;
    outline-offset: -2px;
}

select:focus, input:focus, textarea:focus {
    outline: 1px solid lightblue;
    /*outline-offset: -2px;*/
}

* {
    box-sizing: border-box;
}
