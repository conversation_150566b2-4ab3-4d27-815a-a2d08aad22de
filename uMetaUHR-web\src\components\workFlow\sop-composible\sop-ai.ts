import { provide } from 'vue'
import eventBus from '@/components/mixins/eventBus'
import { getContext } from '@/lib/getContext'
import { execAI2, getFile } from '@/lib/aiLib/ai'
import { compileTemplate2 } from '@/components/action/TextProcessors/ext_handlebars'
import { getClipboardContent } from '@/lib/copypaste'

interface Lab {
  id: number
  name: string
}

interface Context {
  patData?: {
    id: string
  }
  detail?: {
    lab_exam_list?: Lab[]
  }
}

interface Param {
  schema: {
    event?: {
      id: number
      name: string
    }
  }
}

const alert = async (message: string): Promise<void> => {
  console.log(message)
  return window.popAlert({ message })
}

let props: any

const processSummary = async (
  promptName: string,
  context: Context,
  lab: Lab | string
): Promise<void> => {
  const prompt = await getFile('SOP-prompts', promptName)
  if (!prompt) {
    await alert('Prompt sum is not found.')
    return
  }

  const filledPrompt = await compileTemplate2(prompt, { c: context, lab })

  try {
    const { text } = await execAI2(filledPrompt, { context })
    eventBus.emit('tp-summary-in-note', `${text}<br><br>`)
  } catch (error) {
    console.error('Error executing AI:', error)
    await alert('Failed to generate summary.')
  }
}

const dataSummarizeFromLab = async (param: Param): Promise<void> => {
  const context = getContext(props) as Context
  const pat_id = context?.patData?.id
  if (!pat_id) return

  const { id: lab_id = 0 } = param.schema.event || {}
  const { lab_exam_list = [] } = context?.detail || {}
  const lab = lab_exam_list.find((x: Lab) => +x.id === +lab_id)

  if (!lab) {
    await alert('Lab not found.')
    return
  }

  await processSummary('lab-sum', context, lab)
}

const dataSummarizeFromClipboard = async (): Promise<void> => {
  const context = getContext(props) as Context
  const sourceText = (await getClipboardContent()) || ''
  if (!sourceText) return

  await processSummary('lab-sum', context, sourceText)
}

const dataSummarizeFromNote = async (...param: any): Promise<void> => {
  const context = getContext(props) as Context
  await processSummary('note-sum', context, '')
}

function useSopAI(p: any): void {
  provide('data-summarize-from-lab', dataSummarizeFromLab)
  provide('data-summarize-from-note', dataSummarizeFromNote)
  provide('data-summarize-from-clipboard', dataSummarizeFromClipboard)
  props = p
}

export { alert, useSopAI }
