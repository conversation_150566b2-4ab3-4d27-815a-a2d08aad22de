import { isEmpty } from '@/lib/util'
import { getNestedValue } from '@/lib/getContext'
import { htmlToText } from '@/lib/textFormater'
import { compileTemplate2 } from '@/components/action/TextProcessors/ext_handlebars'
import { CommandFlow } from '@/components/action/commandFlow/CommandFlow'
import { execAI2, getFile } from '@/lib/aiLib/ai'

const alert = async (message: string) => {
  console.log(message)
  return window.popAlert({ message })
}

const smartCommand = async (
  data = {} as { [key: string]: string },
  locContext = {},
  schema = {},
  context = {} as any,
  { commandManager } = {} as any
) => {
  if (typeof data !== 'object') {
    await alert('ERROR: ' + JSON.stringify(data))
    return { text: '' }
  }
  const { commands = [] } = data
  if (isEmpty(commands)) return { text: '' }

  const namedText = {}
  let text = ''
  for (const command of commands) {
    // eslint-disable-next-line prefer-const
    let {
      command: { name = '', args = [] },
      content = ''
    } = command || ({} as any)
    if (!name) continue
    content = htmlToText(content)

    if (content.includes('{{')) {
      content = await compileTemplate2(content, context, namedText)
    }

    if (name === '标题') {
      let [title] = args || []
      if (title) {
        title = `<h3>${title}</h3><br>`
        text += title
        data.beforeCursor += title
      }
      if (content) {
        text += content + '\n'
        data.beforeCursor += content + '\n'
      }
      continue
    }

    if (name == '选择病历') {
      const [note_type] = args || []
      commandManager &&
        (await commandManager!.execute(
          'command-select-note',
          new CommandFlow(
            {},
            {
              async request(key: string, note_data: any, ...n: any[]) {
                if (key === 'item_to_select') {
                  const { pat_encounter = '' } = context
                  return note_data.findIndex(
                    (x: any) => x.note_type === note_type && x.pat_encounter === pat_encounter
                  )
                }
              }
            }
          )
        ))
      continue
    }

    if (name == 'SOP') {
      const [title] = args || []
      const content = await getFile('SOP-guideline', title)
      text += content + '\n'
      data.beforeCursor += content + '\n'
      continue
    }

    if (name === '病历') {
      for (const type of args) {
        let para = ''
        if (type === '摘要') {
          const prompt = '{{showObj rc.detail.cn1.data.extNoteData}}'
          const text = compileTemplate2(prompt, { c: context })
          para = `<h3>${type}</h3>${text}<br>`
        } else {
          para = getNestedValue(context.note_map || {}, type)
          para = `<h3>${type}</h3>${para}<br>`
        }
        text += para
        data.beforeCursor += para
      }
      if (content) {
        text += content + '\n'
        data.beforeCursor += content + '\n'
      }
      continue
    }

    if (name === 'ai') {
      const [promptName = 'smart-prompt', contextLength = 200] = args
      const prompt = await getFile('SOP-prompts', promptName)
      if (!prompt) {
        await alert(`Prompt ${args[0] || 'smart-prompt'} is not found.`)
        continue
      }

      const { text: para } = await execAI2(prompt, {
        data: {
          userInput: content,
          beforeCursor: (data?.beforeCursor || '').slice(-+contextLength),
          afterCursor: (data?.afterCursor || '').slice(0, +contextLength)
        },
        c: locContext,
        rc: context
      })
      text += para + '\n'
      data.beforeCursor += para + '\n'
      continue
    }

    await alert(`命令 ${name} 未定义`)
  }

  return { text }
}

export { smartCommand }
