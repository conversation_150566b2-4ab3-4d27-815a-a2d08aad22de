import { defineAsyncComponent, type Component } from 'vue'

const componentGlobs: Record<string, Record<string, () => Promise<{ default: Component }>>> = {
  dataComp: import.meta.glob('./dataComp/**/*.vue') as Record<string, () => Promise<{ default: Component }>>,
  action: import.meta.glob('./action/**/*.vue') as Record<string, () => Promise<{ default: Component }>>,
  containers: import.meta.glob('./containers/**/*.vue') as Record<string, () => Promise<{ default: Component }>>,
  workFlow: import.meta.glob('./workFlow/**/*.vue') as Record<string, () => Promise<{ default: Component }>>,
  workItem: import.meta.glob('./workItem/**/*.vue') as Record<string, () => Promise<{ default: Component }>>
}

export const componentRegistry: { [key: string]: any } = {}

Object.entries(componentGlobs).forEach(([category, modules]) => {
  for (const path in modules) {
    const match = path.match(/\/([^/]+)\.vue$/)
    if (!match) continue

    const componentName = match[1]

    // 可选: 添加类别前缀避免冲突
    // componentName = `${category}-${componentName}`

    if (componentRegistry[componentName]) {
      console.warn(`Duplicate component name detected: ${componentName} in ${category}`)
      continue
    }
    componentRegistry[componentName] = defineAsyncComponent(modules[path])
  }
})

componentRegistry.EmrEditor = defineAsyncComponent(() =>
  import('./action/TextProcessors/EmrEditor.vue')
)

export function loadComponent(type: string | Object) {
  if (!type) return null

  let t2: string = ''
  if (type instanceof Object) {
    ;({ type: t2 } = type as any)
  } else {
    t2 = type
  }

  if (!t2) return null

  t2 = t2[0].toUpperCase() + t2.slice(1)
  const comp = componentRegistry[t2]
  if (comp) return comp
  if (type && !comp)
    console.warn(`Component type ${JSON.stringify(type)} is missing in component registry`)
  return null
}
