{"version": 3, "sources": ["browser-external:dgram", "../../ntp-client/lib/ntp-client.js"], "sourcesContent": ["module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"dgram\" has been externalized for browser compatibility. Cannot access \"dgram.${key}\" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "/*\r\n * ntp-client\r\n * https://github.com/moonpyk/node-ntp-client\r\n *\r\n * Copyright (c) 2013 Clément Bourgeois\r\n * Licensed under the MIT license.\r\n */\r\n\r\n(function (exports) {\r\n    \"use strict\";\r\n\r\n    var dgram = require('dgram');\r\n\r\n    exports.defaultNtpPort = 123;\r\n    exports.defaultNtpServer = \"pool.ntp.org\";\r\n\r\n    /**\r\n     * Amount of acceptable time to await for a response from the remote server.\r\n     * Configured default to 10 seconds.\r\n     */\r\n    exports.ntpReplyTimeout = 10000;\r\n\r\n    /**\r\n     * Fetches the current NTP Time from the given server and port.\r\n     * @param {string} server IP/Hostname of the remote NTP Server\r\n     * @param {number} port Remote NTP Server port number\r\n     * @param {function(Object, Date)} callback(err, date) Async callback for\r\n     * the result date or eventually error.\r\n     */\r\n    exports.getNetworkTime = function (server, port, callback) {\r\n        if (callback === null || typeof callback !== \"function\") {\r\n            return;\r\n        }\r\n\r\n        server = server || exports.defaultNtpServer;\r\n        port = port || exports.defaultNtpPort;\r\n\r\n        var client = dgram.createSocket(\"udp4\"),\r\n            ntpData = new Buffer(48);\r\n\r\n        // RFC 2030 -> LI = 0 (no warning, 2 bits), VN = 3 (IPv4 only, 3 bits), Mode = 3 (Client Mode, 3 bits) -> 1 byte\r\n        // -> rtol(LI, 6) ^ rotl(VN, 3) ^ rotl(Mode, 0)\r\n        // -> = 0x00 ^ 0x18 ^ 0x03\r\n        ntpData[0] = 0x1B;\r\n\r\n        for (var i = 1; i < 48; i++) {\r\n            ntpData[i] = 0;\r\n        }\r\n\r\n        var timeout = setTimeout(function () {\r\n            client.close();\r\n            callback(\"Timeout waiting for NTP response.\", null);\r\n        }, exports.ntpReplyTimeout);\r\n\r\n        // Some errors can happen before/after send() or cause send() to was impossible.\r\n        // Some errors will also be given to the send() callback.\r\n        // We keep a flag, therefore, to prevent multiple callbacks.\r\n        // NOTE : the error callback is not generalised, as the client has to lose the connection also, apparently.\r\n        var errorFired = false;\r\n\r\n        client.on('error', function (err) {\r\n            if (errorFired) {\r\n                return;\r\n            }\r\n\r\n            callback(err, null);\r\n            errorFired = true;\r\n\r\n            clearTimeout(timeout);\r\n        });\r\n\r\n        client.send(ntpData, 0, ntpData.length, port, server, function (err) {\r\n            if (err) {\r\n                if (errorFired) {\r\n                    return;\r\n                }\r\n                clearTimeout(timeout);\r\n                callback(err, null);\r\n                errorFired = true;\r\n                client.close();\r\n                return;\r\n            }\r\n\r\n            client.once('message', function (msg) {\r\n                clearTimeout(timeout);\r\n                client.close();\r\n\r\n                // Offset to get to the \"Transmit Timestamp\" field (time at which the reply\r\n                // departed the server for the client, in 64-bit timestamp format.\"\r\n                var offsetTransmitTime = 40,\r\n                    intpart = 0,\r\n                    fractpart = 0;\r\n\r\n                // Get the seconds part\r\n                for (var i = 0; i <= 3; i++) {\r\n                    intpart = 256 * intpart + msg[offsetTransmitTime + i];\r\n                }\r\n\r\n                // Get the seconds fraction\r\n                for (i = 4; i <= 7; i++) {\r\n                    fractpart = 256 * fractpart + msg[offsetTransmitTime + i];\r\n                }\r\n\r\n                var milliseconds = (intpart * 1000 + (fractpart * 1000) / 0x100000000);\r\n\r\n                // **UTC** time\r\n                var date = new Date(\"Jan 01 1900 GMT\");\r\n                date.setUTCMilliseconds(date.getUTCMilliseconds() + milliseconds);\r\n\r\n                callback(null, date);\r\n            });\r\n        });\r\n    };\r\n\r\n    exports.demo = function (argv) {\r\n        exports.getNetworkTime(\r\n            exports.defaultNtpServer,\r\n            exports.defaultNtpPort,\r\n            function (err, date) {\r\n                if (err) {\r\n                    console.error(err);\r\n                    return;\r\n                }\r\n\r\n                console.log(date);\r\n            });\r\n    };\r\n}(exports));\r\n"], "mappings": ";;;;;AAAA;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,wFAAwF,GAAG,mIAAmI;AAAA,QAC7O;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACXF;AAAA;AAQA,KAAC,SAAUA,UAAS;AAChB;AAEA,UAAI,QAAQ;AAEZ,MAAAA,SAAQ,iBAAiB;AACzB,MAAAA,SAAQ,mBAAmB;AAM3B,MAAAA,SAAQ,kBAAkB;AAS1B,MAAAA,SAAQ,iBAAiB,SAAU,QAAQ,MAAM,UAAU;AACvD,YAAI,aAAa,QAAQ,OAAO,aAAa,YAAY;AACrD;AAAA,QACJ;AAEA,iBAAS,UAAUA,SAAQ;AAC3B,eAAO,QAAQA,SAAQ;AAEvB,YAAI,SAAS,MAAM,aAAa,MAAM,GAClC,UAAU,IAAI,OAAO,EAAE;AAK3B,gBAAQ,CAAC,IAAI;AAEb,iBAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AACzB,kBAAQ,CAAC,IAAI;AAAA,QACjB;AAEA,YAAI,UAAU,WAAW,WAAY;AACjC,iBAAO,MAAM;AACb,mBAAS,qCAAqC,IAAI;AAAA,QACtD,GAAGA,SAAQ,eAAe;AAM1B,YAAI,aAAa;AAEjB,eAAO,GAAG,SAAS,SAAU,KAAK;AAC9B,cAAI,YAAY;AACZ;AAAA,UACJ;AAEA,mBAAS,KAAK,IAAI;AAClB,uBAAa;AAEb,uBAAa,OAAO;AAAA,QACxB,CAAC;AAED,eAAO,KAAK,SAAS,GAAG,QAAQ,QAAQ,MAAM,QAAQ,SAAU,KAAK;AACjE,cAAI,KAAK;AACL,gBAAI,YAAY;AACZ;AAAA,YACJ;AACA,yBAAa,OAAO;AACpB,qBAAS,KAAK,IAAI;AAClB,yBAAa;AACb,mBAAO,MAAM;AACb;AAAA,UACJ;AAEA,iBAAO,KAAK,WAAW,SAAU,KAAK;AAClC,yBAAa,OAAO;AACpB,mBAAO,MAAM;AAIb,gBAAI,qBAAqB,IACrB,UAAU,GACV,YAAY;AAGhB,qBAASC,KAAI,GAAGA,MAAK,GAAGA,MAAK;AACzB,wBAAU,MAAM,UAAU,IAAI,qBAAqBA,EAAC;AAAA,YACxD;AAGA,iBAAKA,KAAI,GAAGA,MAAK,GAAGA,MAAK;AACrB,0BAAY,MAAM,YAAY,IAAI,qBAAqBA,EAAC;AAAA,YAC5D;AAEA,gBAAI,eAAgB,UAAU,MAAQ,YAAY,MAAQ;AAG1D,gBAAI,OAAO,oBAAI,KAAK,iBAAiB;AACrC,iBAAK,mBAAmB,KAAK,mBAAmB,IAAI,YAAY;AAEhE,qBAAS,MAAM,IAAI;AAAA,UACvB,CAAC;AAAA,QACL,CAAC;AAAA,MACL;AAEA,MAAAD,SAAQ,OAAO,SAAU,MAAM;AAC3B,QAAAA,SAAQ;AAAA,UACJA,SAAQ;AAAA,UACRA,SAAQ;AAAA,UACR,SAAU,KAAK,MAAM;AACjB,gBAAI,KAAK;AACL,sBAAQ,MAAM,GAAG;AACjB;AAAA,YACJ;AAEA,oBAAQ,IAAI,IAAI;AAAA,UACpB;AAAA,QAAC;AAAA,MACT;AAAA,IACJ,GAAE,OAAO;AAAA;AAAA;", "names": ["exports", "i"]}