{
  function getDateTime() {
    return options.getDateTime();
  }

  let time = getDateTime();

  function manipulateTime(unit, value, setValue = null) {
    if(value != 0) time = time.plus({ [unit]: value }); // 增加时间
    if (setValue !== null) {
      if (unit === "week") unit = "weekday";
      time = time.set({ [unit]: setValue }); // 设置时间
    }
  }

  // 工具函数：设置日期时间并返回秒数
  function setDateTime(year, month, day, hour, minute) {
    if (year !== null) time = time.set({ year });
    if (month !== null) time = time.set({ month });
    if (day !== null) time = time.set({ day });
    if (hour !== null) time = time.set({ hour });
    if (minute !== null) time = time.set({ minute });
  }
}

  // 根规则
  result = ' '* expr (' '+ expr?)* {
    return time.toSeconds(); // 返回从 UTC 0 以来的秒数
  }

  // 表达式规则
  expr = time / timeSpan / date;

  // 时间操作规则
  timeSpan =
      n:n? 'M' d:d? { return manipulateTime('minute', +n, d); }
    / n:n? 'h' d:d? { return manipulateTime('hour', +n, d); }
    / n:n? 'd' d:d? { return manipulateTime('day', +n, d); }
    / n:n? 'w' d:d? { return manipulateTime('week', +n, d); }
    / n:n? 'm' d:d? { return manipulateTime('month', +n, d); }
    / n:n? 'y' d:d? {
        if(+n>0) time = time.plus({ years: +n });
        if (d !== null) {
          if (d < 30) d += 2000;
          else if (d < 99) d += 1900;
          time = time.set({ year: d });
        }
      }
    / n:n 'z' &{ return +n > 0; } { time = options.countWorkDays(time,+n);  }
    / 'p' { options.previousDateTime();  }
    / 'n' { time = getDateTime(); }
    / 'ad' { options.admitTime();  }
    ;

  // 数字解析规则
  n = x:$([+-]?[0-9.]*) { return (+x) || 0; };
  d = x:$([0-9]+) { return (+x); };

  // 日期解析规则
  date =
  y:year '/' m:month '/' d:day &{ return y > 0 && m > 0 && d > 0; } { return setDateTime(y, m, d, null, null); } // 1955/8/8 格式
  / y:year '-' m:month '-' d:day &{ return y > 0 && m > 0 && d > 0; } { return setDateTime(y, m, d, null, null); }
  / m:month ('-'/'/') d:day &{ return m > 0 && d > 0; } { return setDateTime(null, m, d, null, null); }
    ;

  year =
    x:$([0-9]|4|) &{ return +x >= 1900 && +x <= 2035; } { return +x; }
  / x:$([0-9]|2|) &{ return +x >= 0 && +x <= 99; } { x = +x + 2000; if (x > 2030) x -= 100; return x; }
    ;

  month = x:$([0-9]+) &{ return +x >= 1 && +x <= 12; } { return +x; };
  day = x:$([0-9]+) &{ return +x >= 1 && +x <= 31; } { return +x; };

  // 时间解析规则
  time =
    h:hour ':' m:minute { return setDateTime(null, null, null, h, m); } // HH:mm 格式
  / hmm:$([0-9]|3..4|) {                                              // Hmm 或 HHmm 格式
        let h = Math.floor(hmm / 100);
        let m = hmm % 100;
        if (h >= 0 && h <= 23 && m >= 0 && m <= 59) {
          return setDateTime(null, null, null, h, m);
        }
      }
    ;

  // 24 小时制的小时和分钟解析规则
  hour = x:$([0-9]+) &{ return +x >= 0 && +x <= 23; } { return +x; };
  minute = x:$([0-9]+) &{ return +x >= 0 && +x <= 59; } { return +x; };