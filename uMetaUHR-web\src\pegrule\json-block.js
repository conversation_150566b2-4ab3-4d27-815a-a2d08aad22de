// @generated by Peggy 4.2.0.
//
// https://peggyjs.org/

function peg$subclass(child, parent) {
  function C() {
    this.constructor = child
  }

  C.prototype = parent.prototype
  child.prototype = new C()
}

function peg$SyntaxError(message, expected, found, location) {
  var self = Error.call(this, message)
  // istanbul ignore next Check is a necessary evil to support older environments
  if (Object.setPrototypeOf) {
    Object.setPrototypeOf(self, peg$SyntaxError.prototype)
  }
  self.expected = expected
  self.found = found
  self.location = location
  self.name = 'SyntaxError'
  return self
}

peg$subclass(peg$SyntaxError, Error)

function peg$padEnd(str, targetLength, padString) {
  padString = padString || ' '
  if (str.length > targetLength) {
    return str
  }
  targetLength -= str.length
  padString += padString.repeat(targetLength)
  return str + padString.slice(0, targetLength)
}

peg$SyntaxError.prototype.format = function (sources) {
  var str = 'Error: ' + this.message
  if (this.location) {
    var src = null
    var k
    for (k = 0; k < sources.length; k++) {
      if (sources[k].source === this.location.source) {
        src = sources[k].text.split(/\r\n|\n|\r/g)
        break
      }
    }
    var s = this.location.start
    var offset_s =
      this.location.source && typeof this.location.source.offset === 'function'
        ? this.location.source.offset(s)
        : s
    var loc = this.location.source + ':' + offset_s.line + ':' + offset_s.column
    if (src) {
      var e = this.location.end
      var filler = peg$padEnd('', offset_s.line.toString().length, ' ')
      var line = src[s.line - 1]
      var last = s.line === e.line ? e.column : line.length + 1
      var hatLen = last - s.column || 1
      str +=
        '\n --> ' +
        loc +
        '\n' +
        filler +
        ' |\n' +
        offset_s.line +
        ' | ' +
        line +
        '\n' +
        filler +
        ' | ' +
        peg$padEnd('', s.column - 1, ' ') +
        peg$padEnd('', hatLen, '^')
    } else {
      str += '\n at ' + loc
    }
  }
  return str
}

peg$SyntaxError.buildMessage = function (expected, found) {
  var DESCRIBE_EXPECTATION_FNS = {
    literal: function (expectation) {
      return '"' + literalEscape(expectation.text) + '"'
    },

    class: function (expectation) {
      var escapedParts = expectation.parts.map(function (part) {
        return Array.isArray(part)
          ? classEscape(part[0]) + '-' + classEscape(part[1])
          : classEscape(part)
      })

      return '[' + (expectation.inverted ? '^' : '') + escapedParts.join('') + ']'
    },

    any: function () {
      return 'any character'
    },

    end: function () {
      return 'end of input'
    },

    other: function (expectation) {
      return expectation.description
    }
  }

  function hex(ch) {
    return ch.charCodeAt(0).toString(16).toUpperCase()
  }

  function literalEscape(s) {
    return s
      .replace(/\\/g, '\\\\')
      .replace(/"/g, '\\"')
      .replace(/\0/g, '\\0')
      .replace(/\t/g, '\\t')
      .replace(/\n/g, '\\n')
      .replace(/\r/g, '\\r')
      .replace(/[\x00-\x0F]/g, function (ch) {
        return '\\x0' + hex(ch)
      })
      .replace(/[\x10-\x1F\x7F-\x9F]/g, function (ch) {
        return '\\x' + hex(ch)
      })
  }

  function classEscape(s) {
    return s
      .replace(/\\/g, '\\\\')
      .replace(/\]/g, '\\]')
      .replace(/\^/g, '\\^')
      .replace(/-/g, '\\-')
      .replace(/\0/g, '\\0')
      .replace(/\t/g, '\\t')
      .replace(/\n/g, '\\n')
      .replace(/\r/g, '\\r')
      .replace(/[\x00-\x0F]/g, function (ch) {
        return '\\x0' + hex(ch)
      })
      .replace(/[\x10-\x1F\x7F-\x9F]/g, function (ch) {
        return '\\x' + hex(ch)
      })
  }

  function describeExpectation(expectation) {
    return DESCRIBE_EXPECTATION_FNS[expectation.type](expectation)
  }

  function describeExpected(expected) {
    var descriptions = expected.map(describeExpectation)
    var i, j

    descriptions.sort()

    if (descriptions.length > 0) {
      for (i = 1, j = 1; i < descriptions.length; i++) {
        if (descriptions[i - 1] !== descriptions[i]) {
          descriptions[j] = descriptions[i]
          j++
        }
      }
      descriptions.length = j
    }

    switch (descriptions.length) {
      case 1:
        return descriptions[0]

      case 2:
        return descriptions[0] + ' or ' + descriptions[1]

      default:
        return (
          descriptions.slice(0, -1).join(', ') + ', or ' + descriptions[descriptions.length - 1]
        )
    }
  }

  function describeFound(found) {
    return found ? '"' + literalEscape(found) + '"' : 'end of input'
  }

  return 'Expected ' + describeExpected(expected) + ' but ' + describeFound(found) + ' found.'
}

function peg$parse(input, options) {
  options = options !== undefined ? options : {}

  var peg$FAILED = {}
  var peg$source = options.grammarSource

  var peg$startRuleFunctions = { Start: peg$parseStart }
  var peg$startRuleFunction = peg$parseStart

  var peg$c0 = '```'
  var peg$c1 = 'json'
  var peg$c2 = '`'

  var peg$e0 = peg$literalExpectation('```', false)
  var peg$e1 = peg$literalExpectation('json', false)
  var peg$e2 = peg$literalExpectation('`', false)
  var peg$e3 = peg$anyExpectation()

  var peg$f0 = function (blocks) {
    return blocks
  }
  var peg$f1 = function (content) {
    return content
  }
  var peg$f2 = function (chars) {
    return chars.join('')
  }
  var peg$currPos = options.peg$currPos | 0
  var peg$savedPos = peg$currPos
  var peg$posDetailsCache = [{ line: 1, column: 1 }]
  var peg$maxFailPos = peg$currPos
  var peg$maxFailExpected = options.peg$maxFailExpected || []
  var peg$silentFails = options.peg$silentFails | 0

  var peg$result

  if (options.startRule) {
    if (!(options.startRule in peg$startRuleFunctions)) {
      throw new Error('Can\'t start parsing from rule "' + options.startRule + '".')
    }

    peg$startRuleFunction = peg$startRuleFunctions[options.startRule]
  }

  function text() {
    return input.substring(peg$savedPos, peg$currPos)
  }

  function offset() {
    return peg$savedPos
  }

  function range() {
    return {
      source: peg$source,
      start: peg$savedPos,
      end: peg$currPos
    }
  }

  function location() {
    return peg$computeLocation(peg$savedPos, peg$currPos)
  }

  function expected(description, location) {
    location = location !== undefined ? location : peg$computeLocation(peg$savedPos, peg$currPos)

    throw peg$buildStructuredError(
      [peg$otherExpectation(description)],
      input.substring(peg$savedPos, peg$currPos),
      location
    )
  }

  function error(message, location) {
    location = location !== undefined ? location : peg$computeLocation(peg$savedPos, peg$currPos)

    throw peg$buildSimpleError(message, location)
  }

  function peg$literalExpectation(text, ignoreCase) {
    return { type: 'literal', text: text, ignoreCase: ignoreCase }
  }

  function peg$classExpectation(parts, inverted, ignoreCase) {
    return { type: 'class', parts: parts, inverted: inverted, ignoreCase: ignoreCase }
  }

  function peg$anyExpectation() {
    return { type: 'any' }
  }

  function peg$endExpectation() {
    return { type: 'end' }
  }

  function peg$otherExpectation(description) {
    return { type: 'other', description: description }
  }

  function peg$computePosDetails(pos) {
    var details = peg$posDetailsCache[pos]
    var p

    if (details) {
      return details
    } else {
      if (pos >= peg$posDetailsCache.length) {
        p = peg$posDetailsCache.length - 1
      } else {
        p = pos
        while (!peg$posDetailsCache[--p]) {}
      }

      details = peg$posDetailsCache[p]
      details = {
        line: details.line,
        column: details.column
      }

      while (p < pos) {
        if (input.charCodeAt(p) === 10) {
          details.line++
          details.column = 1
        } else {
          details.column++
        }

        p++
      }

      peg$posDetailsCache[pos] = details

      return details
    }
  }

  function peg$computeLocation(startPos, endPos, offset) {
    var startPosDetails = peg$computePosDetails(startPos)
    var endPosDetails = peg$computePosDetails(endPos)

    var res = {
      source: peg$source,
      start: {
        offset: startPos,
        line: startPosDetails.line,
        column: startPosDetails.column
      },
      end: {
        offset: endPos,
        line: endPosDetails.line,
        column: endPosDetails.column
      }
    }
    if (offset && peg$source && typeof peg$source.offset === 'function') {
      res.start = peg$source.offset(res.start)
      res.end = peg$source.offset(res.end)
    }
    return res
  }

  function peg$fail(expected) {
    if (peg$currPos < peg$maxFailPos) {
      return
    }

    if (peg$currPos > peg$maxFailPos) {
      peg$maxFailPos = peg$currPos
      peg$maxFailExpected = []
    }

    peg$maxFailExpected.push(expected)
  }

  function peg$buildSimpleError(message, location) {
    return new peg$SyntaxError(message, null, null, location)
  }

  function peg$buildStructuredError(expected, found, location) {
    return new peg$SyntaxError(
      peg$SyntaxError.buildMessage(expected, found),
      expected,
      found,
      location
    )
  }

  function peg$parseStart() {
    var s0, s1, s2

    s0 = peg$currPos
    s1 = []
    s2 = peg$parseJsonBlock()
    if (s2 !== peg$FAILED) {
      while (s2 !== peg$FAILED) {
        s1.push(s2)
        s2 = peg$parseJsonBlock()
      }
    } else {
      s1 = peg$FAILED
    }
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0
      s1 = peg$f0(s1)
    }
    s0 = s1

    return s0
  }

  function peg$parseJsonBlock() {
    var s0, s1, s2, s3, s4

    s0 = peg$currPos
    if (input.substr(peg$currPos, 3) === peg$c0) {
      s1 = peg$c0
      peg$currPos += 3
    } else {
      s1 = peg$FAILED
      if (peg$silentFails === 0) {
        peg$fail(peg$e0)
      }
    }
    if (s1 !== peg$FAILED) {
      if (input.substr(peg$currPos, 4) === peg$c1) {
        s2 = peg$c1
        peg$currPos += 4
      } else {
        s2 = peg$FAILED
        if (peg$silentFails === 0) {
          peg$fail(peg$e1)
        }
      }
      if (s2 !== peg$FAILED) {
        s3 = peg$parseContent()
        if (input.substr(peg$currPos, 3) === peg$c0) {
          s4 = peg$c0
          peg$currPos += 3
        } else {
          s4 = peg$FAILED
          if (peg$silentFails === 0) {
            peg$fail(peg$e0)
          }
        }
        if (s4 === peg$FAILED) {
          s4 = peg$parseEOF()
        }
        if (s4 !== peg$FAILED) {
          peg$savedPos = s0
          s0 = peg$f1(s3)
        } else {
          peg$currPos = s0
          s0 = peg$FAILED
        }
      } else {
        peg$currPos = s0
        s0 = peg$FAILED
      }
    } else {
      peg$currPos = s0
      s0 = peg$FAILED
    }

    return s0
  }

  function peg$parseContent() {
    var s0, s1, s2, s3, s4

    s0 = peg$currPos
    s1 = []
    s2 = peg$currPos
    s3 = peg$currPos
    peg$silentFails++
    if (input.charCodeAt(peg$currPos) === 96) {
      s4 = peg$c2
      peg$currPos++
    } else {
      s4 = peg$FAILED
      if (peg$silentFails === 0) {
        peg$fail(peg$e2)
      }
    }
    peg$silentFails--
    if (s4 === peg$FAILED) {
      s3 = undefined
    } else {
      peg$currPos = s3
      s3 = peg$FAILED
    }
    if (s3 !== peg$FAILED) {
      if (input.length > peg$currPos) {
        s4 = input.charAt(peg$currPos)
        peg$currPos++
      } else {
        s4 = peg$FAILED
        if (peg$silentFails === 0) {
          peg$fail(peg$e3)
        }
      }
      if (s4 !== peg$FAILED) {
        s3 = [s3, s4]
        s2 = s3
      } else {
        peg$currPos = s2
        s2 = peg$FAILED
      }
    } else {
      peg$currPos = s2
      s2 = peg$FAILED
    }
    while (s2 !== peg$FAILED) {
      s1.push(s2)
      s2 = peg$currPos
      s3 = peg$currPos
      peg$silentFails++
      if (input.charCodeAt(peg$currPos) === 96) {
        s4 = peg$c2
        peg$currPos++
      } else {
        s4 = peg$FAILED
        if (peg$silentFails === 0) {
          peg$fail(peg$e2)
        }
      }
      peg$silentFails--
      if (s4 === peg$FAILED) {
        s3 = undefined
      } else {
        peg$currPos = s3
        s3 = peg$FAILED
      }
      if (s3 !== peg$FAILED) {
        if (input.length > peg$currPos) {
          s4 = input.charAt(peg$currPos)
          peg$currPos++
        } else {
          s4 = peg$FAILED
          if (peg$silentFails === 0) {
            peg$fail(peg$e3)
          }
        }
        if (s4 !== peg$FAILED) {
          s3 = [s3, s4]
          s2 = s3
        } else {
          peg$currPos = s2
          s2 = peg$FAILED
        }
      } else {
        peg$currPos = s2
        s2 = peg$FAILED
      }
    }
    peg$savedPos = s0
    s1 = peg$f2(s1)
    s0 = s1

    return s0
  }

  function peg$parseEOF() {
    var s0, s1

    s0 = peg$currPos
    peg$silentFails++
    if (input.length > peg$currPos) {
      s1 = input.charAt(peg$currPos)
      peg$currPos++
    } else {
      s1 = peg$FAILED
      if (peg$silentFails === 0) {
        peg$fail(peg$e3)
      }
    }
    peg$silentFails--
    if (s1 === peg$FAILED) {
      s0 = undefined
    } else {
      peg$currPos = s0
      s0 = peg$FAILED
    }

    return s0
  }

  // No initializer code needed

  peg$result = peg$startRuleFunction()

  if (options.peg$library) {
    return /** @type {any} */ ({
      peg$result,
      peg$currPos,
      peg$FAILED,
      peg$maxFailExpected,
      peg$maxFailPos
    })
  }
  if (peg$result !== peg$FAILED && peg$currPos === input.length) {
    return peg$result
  } else {
    if (peg$result !== peg$FAILED && peg$currPos < input.length) {
      peg$fail(peg$endExpectation())
    }

    throw peg$buildStructuredError(
      peg$maxFailExpected,
      peg$maxFailPos < input.length ? input.charAt(peg$maxFailPos) : null,
      peg$maxFailPos < input.length
        ? peg$computeLocation(peg$maxFailPos, peg$maxFailPos + 1)
        : peg$computeLocation(peg$maxFailPos, peg$maxFailPos)
    )
  }
}

const peg$allowedStartRules = ['Start']

export { peg$allowedStartRules as StartRules, peg$SyntaxError as SyntaxError, peg$parse as parse }
