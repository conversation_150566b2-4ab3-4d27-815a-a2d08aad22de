<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div
    :class="`field-w-stack tab-pos-${menu.length > 1 ? tabPosition : 0} ${theme} ${closed ? 'menu-closed' : ''}`"
    :style="{ ...schema?.style?.main, ...schema?.style?.vars?.theme }"
  >
    <div v-if="menu.length > 1" class="toolbar" :style="schema?.style?.toolbar">
      <div class="menu" :style="schema?.style?.menu">
        <template v-for="(m, index) in menu" :key="`stack-${index}`">
          <div
            v-if="m?.id"
            :class="['menu-item', activeMenuId === m?.id ? 'active' : '']"
            :data-menu-id="m?.id"
            @click="clickToPickItem"
          >
            <img
              :src="`${BASE}${m?.customIcon.src}`"
              alt=""
              v-if="m?.customIcon"
              :style="{ 'pointer-events': 'none', ...(m?.customIcon?.style || {}) }"
              class="icon-cls"
            />
            <font-awesome-icon
              v-if="m?.icon"
              :icon="['fas', m?.icon]"
              style="pointer-events: none"
              class="icon-cls"
              @click="clickToPickItem"
            />
            {{ m?.displayName || '' }}
          </div>
          <component
            :is="loadComponent(m)"
            v-else-if="m?.type"
            :context="getContext(props)"
            :schema="m"
          />
        </template>
      </div>
      <template v-if="configCommand">
        <component
          :is="loadComponent(configCommand.type)"
          v-if="configCommand?.type"
          :context="getContext(props)"
          :schema="configCommand"
          class="config-command"
        />
      </template>
    </div>
    <div :class="['menu-panels', closed && tcsFromSchema ? 'menu-closed' : '']">
      <div
        v-for="(child, index) in schema?.children"
        :key="`panel-${index}`"
        :class="`panel-cnt ${activeMenuId === '' + index ? 'active' : ''}`"
        :style="schema?.style?.content"
      >
        <div v-if="!child?.type">Not defined.</div>
        <component
          :is="loadComponent(child.type)"
          v-else-if="componentActivated[index] || activeMenuId === '' + index"
          :ref="(ref2: ComponentPublicInstance | null) => ref2 && registerStackItem(ref2, index)"
          :context="getContext(props)"
          :schema="child"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  type ComponentPublicInstance,
  getCurrentInstance,
  inject,
  onMounted,
  onUnmounted,
  type PropType,
  provide,
  reactive,
  ref,
  type Ref
} from 'vue'
import hotkeys from 'hotkeys-js'
import { loadComponent } from '@/components/componentRegistry'
import { getContext } from '@/lib/getContext'
import { CacheManager } from '@/lib/CacheManager'
import { useEventBus } from '@/components/mixins/eventBus'

const eventBus = useEventBus()
const BASE = import.meta.env.BASE_URL || ''

interface MenuItem {
  id?: string
  displayName: string
  type?: string
  icon?: string,
  customIcon?: string
}

interface SchemaType {
  menu?: MenuItem[]
  children?: Array<{ type: string }>
  style?: {
    main?: Record<string, string | number>
    content?: Record<string, string | number>
    toolbar?: Record<string, string | number>
    menu?: Record<string, string | number>
    vars?: {
      theme?: {
        themeColor?: string
        themeBC?: string
        themeActiveColor?: string
        themeActiveBC?: string
        themeBorderColor?: string
        themeContentBC?: string
      }
    }
  }
  event?: {
    messageEndPoint?: string
    beforeSwitchTab?: { name?: string }
    afterSwitchTab?: { name?: string }
  }
  configCommand?: {
    type: string
  }
  activeMenuId?: string
  myFavoriteId?: string
  hotkeys?: Array<{
    key: string
    scope: string
    menuId: string
  }>
  toggleCloseStatus?: boolean
  theme?: string
  panels?: unknown[]
  tabPosition?: string
}

interface ComponentInstanceExtensions {
  canFlowDeactivate?: () => boolean | Promise<boolean>
  canFlowActivate?: () => boolean | Promise<boolean>
  flowDeactivate?: () => Promise<void>
  comp?: {
    flowActivate?: () => Promise<void>
  }
}

const props = defineProps({
  model: { type: Object as PropType<Record<string, unknown>>, required: false },
  schema: { type: Object as PropType<SchemaType>, required: true },
  context: { type: Object as PropType<Record<string, unknown>>, required: false }
})

interface StackEmits {
  (e: 'stack-collapse-status', value: boolean): void
}

const emit = defineEmits<StackEmits>()

const { schema } = props
const activeMenuId: Ref<string> = ref('')
const componentActivated: Record<string, boolean> = reactive({})
const closed: Ref<boolean> = ref(false)
const tcsFromSchema: Ref<boolean> = ref(true)
const menu: Ref<MenuItem[]> = ref([])
const panels: Ref<unknown[]> = ref([])
const tabPosition: Ref<string> = ref('top')
const theme: Ref<string> = ref('')
const configCommand: Ref<{ type: string } | undefined> = ref(undefined)
const style: Ref<Record<string, unknown>> = ref({})
const stackItems: Ref<Record<string, ComponentPublicInstance & ComponentInstanceExtensions>> = ref(
  {}
)

function registerStackItem(componentRef: ComponentPublicInstance | null, index: number): void {
  if (componentRef) {
    stackItems.value[index] = componentRef
  }
}

type FavType = {
  openStatus?: boolean
  selectedMenuItem?: string
}
const cacheManager = CacheManager.getInstance<FavType>({
  strategy: 'indexeddb',
  ttl: 60000 * 60 * 10
})

const comp = getCurrentInstance()

interface CallbackData {
  cb?: (context: { comp: Record<string, unknown> }) => void
}

const callBackWithContext = (event: unknown): void => {
  const data = event as CallbackData
  const { cb } = data || {}
  if (!cb) return
  cb({ comp: comp?.exposed || {} })
}

onMounted(async () => {
  const {
    activeMenuId: a1,
    myFavoriteId,
    hotkeys: hkeys = [],
    style: styleVal = {},
    theme: themeVal = '',
    menu: menuVal = [],
    panels: panelsVal = [],
    tabPosition: tabPositionVal = 'top',
    configCommand: configCommandVal
  } = schema || {}

  style.value = styleVal
  theme.value = themeVal
  menu.value = menuVal
  panels.value = panelsVal
  tabPosition.value = tabPositionVal
  configCommand.value = configCommandVal

  const [{ id: a2 = '' } = {}] = menu.value
  let { openStatus = closed.value ?? false, selectedMenuItem } =
    (await cacheManager.get(myFavoriteId || '')) || {}
  closed.value = openStatus
  emit('stack-collapse-status', closed.value)
  selectedMenuItem = (selectedMenuItem ?? a1 ?? a2 ?? menu?.value[0]?.id) || ''

  if (hkeys?.length > 0) {
    for (let { key, scope, menuId } of hkeys) {
      if (!key || !menuId) continue
      hotkeys.unbind(key, scope)
      hotkeys(key, scope, () => {
        pickItem({ menuId }, true, true)
      })
    }
  }

  await pickItem({ menuId: selectedMenuItem }, false, false)

  const messageEndPoint = props.schema?.event?.messageEndPoint
  if (!messageEndPoint) return

  eventBus.on(messageEndPoint as string, callBackWithContext)
})

onUnmounted(() => {
  const messageEndPoint = props.schema?.event?.messageEndPoint
  if (!messageEndPoint) return
  eventBus.off(messageEndPoint as string, callBackWithContext)
})

type SwitchTabHandler = (params: { menuId: string }) => Promise<void> | void
const beforeSwitchTab = inject<SwitchTabHandler>('before-switch-stack-tab', () => Promise.resolve())
const afterSwitchTab = inject<SwitchTabHandler>('after-switch-stack-tab', () => Promise.resolve())

interface PickItemParams {
  menuId: string
  name?: string | null
}

async function pickItem(
  { menuId, name = null }: PickItemParams,
  toggleCloseStatus = true,
  saveFav = false
): Promise<void> {
  if (!menuId) return
  const menu = props.schema.menu?.find((e) => e.id === menuId)
  if (!menu) return
  if (name) menu.displayName = name

  const { myFavoriteId, toggleCloseStatus: tcsFromSchemaFlag = true } = schema || {}
  tcsFromSchema.value = tcsFromSchemaFlag
  if (toggleCloseStatus) {
    if (activeMenuId.value === menuId) {
      if (tcsFromSchema.value) {
        closed.value = !closed.value
        emit('stack-collapse-status', closed.value)
      }
      if (saveFav && myFavoriteId) {
        await cacheManager.set(myFavoriteId, {
          selectedMenuItem: menuId,
          openStatus: closed.value
        })
      }
      return
    }

    closed.value = false
    emit('stack-collapse-status', closed.value)
  }

  const previousComp = stackItems.value[activeMenuId.value]
  const currentComp = stackItems.value[menuId]

  if (previousComp?.canFlowDeactivate && (await previousComp?.canFlowDeactivate?.()) === false)
    return
  if (currentComp?.canFlowActivate && (await currentComp?.canFlowActivate?.()) === false) return

  await previousComp?.flowDeactivate?.()
  activeMenuId.value = menuId
  componentActivated[menuId] = true

  if (saveFav && myFavoriteId) {
    await cacheManager.set(myFavoriteId, {
      selectedMenuItem: menuId,
      openStatus: closed.value
    })
  }

  await beforeSwitchTab({ menuId })
  await currentComp?.comp?.flowActivate?.()
  await afterSwitchTab({ menuId })
}

function clickToPickItem(event: MouseEvent): void {
  event.preventDefault()
  event.stopPropagation()
  const { menuId } = (event.target as HTMLElement).dataset
  if (menuId) {
    pickItem({ menuId }, true, true)
  }
}

function toggleCollapse(): void {
  const menuId = activeMenuId.value
  pickItem({ menuId }, true, true)
}

provide('append-menu-item', (): void => {})
defineExpose({ compName: 'stack', toggleCollapse, pickItem })
</script>

<style lang="scss" scoped>
// ===== 工具混合 =====
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin full-absolute {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.field-w-stack {
  --theme-color: v-bind('schema?.style?.vars?.themeColor || "black"');
  --theme-background-color: v-bind('schema?.style?.vars?.themeBC || "white"');
  --theme-active-color: v-bind('schema?.style?.vars?.themeActiveColor || "black"');
  --theme-active-background-color: v-bind('schema?.style?.vars?.themeActiveBC || "white"');
  --theme-border-color: v-bind('schema?.style?.vars?.themeBorderColor || "blue"');
  --theme-content-background-color: v-bind('schema?.style?.vars?.themeContentBC || "white"');

  display: grid;
  flex: 1 1 auto;
  width: 100%;
  height: 100%;
  grid-template: 'menu' 'stack-body' 1fr / 1fr;
  padding: 2px;
  border: none;
}

.menu {
  display: flex;
  flex: 0 0 auto;
  z-index: 10;
  height: 2rem;
  position: relative;

  .menu-item {
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid transparent;
    cursor: pointer;
    font-size: 14px;
    user-select: none;
    position: relative;
    background: var(--theme-background-color);
    min-height: 2em;
    margin-right: 5px;
    border-radius: 4px 4px 0 0;
    padding: 8px 16px;
    gap: 10px;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-weight: 400;

    //&:hover {
    //  &:not(.active) {
    //    background: var(--theme-active-background-color);
    //  }
    //}

    &.active {
      color: var(--theme-active-color);
      background: var(--theme-active-background-color);
      bottom: -1px;
      border: 1px solid var(--theme-border-color);
      border-top: 5px solid var(--theme-border-color);
      border-bottom: none;
      font-weight: 700;
    }

    .close-cls {
      width: 20px;
      height: 20px;
    }
  }

  .icon-cls {
    width: 20px;
    height: 20px;
  }

  .config-command {
    display: flex;
    flex: 0 0 auto;
    align-items: center;
    justify-content: center;
    background: none;
    cursor: pointer;
  }
}

// 面板区域
.menu-panels {
  grid-area: stack-body;
  display: flex;
  border: 1px solid var(--theme-border-color); // 使用主题边框色
  flex: 1 1 auto;
  position: relative;
  min-width: 100px;
  min-height: 100px;
  z-index: 0;

  > .panel-cnt {
    display: flex;
    background: var(--theme-background-color);
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 0;
    overflow-y: auto;

    &.active {
      z-index: 1;
    }
  }
}

// ===== 布局变体 =====
// 左侧标签
.tab-pos-left {
  grid-template: 'menu stack-body' / 6rem 1fr;

  > .toolbar {
    align-items: flex-start;

    > .menu {
      flex-direction: column;

      .menu-item {
        text-align: right;
        white-space: nowrap;
        border: 1px solid transparent;
        border-left: 5px solid transparent;
        border-right: none;
        right: -3px;

        &.active {
          border-color: var(--theme-border-color);
          right: -4px;
        }
      }
    }
  }
}

// 右侧标签
.tab-pos-right {
  grid-template: 'stack-body menu' / 1fr 2.2rem;
  display: flex;
  justify-content: flex-start;
  flex-direction: row-reverse;

  > .toolbar {
    align-items: flex-start;
    display: block;

    > .menu {
      flex-direction: column;
      padding: 2px;
      white-space: nowrap;

      .menu-item {
        writing-mode: vertical-lr;
        padding: 1rem 0;
        min-height: 4rem;
        border: 1px solid transparent;
        border-right: 2px solid transparent;

        &.active {
          border-color: var(--theme-border-color);
        }
      }
    }
  }

  > .menu-panels {
    border: none;

    .field-w-panel {
      border: none;
      overflow: auto;
    }

    &.menu-closed {
      display: none;
    }
  }
}

// 顶部标签 (标准)
.tab-pos-top {
  display: flex;
  flex-direction: column;
  background: var(--theme-background-color);

  > .toolbar > .menu {
    flex-direction: row;

    .menu-item {
      min-width: 4rem;
      border-bottom: none;
      padding: 0 5px;
    }
  }
}

// 顶部标签 (导航样式)
.tab-pos-top-navi {
  background: var(--theme-background-color);

  > .toolbar > .menu {
    border: none;
    margin: 2px;

    .menu-item {
      border-radius: 5px;
      border: none;
      bottom: 0;
      color: var(--theme-color);
      background: none;

      &.active {
        color: var(--theme-active-color);
        background: var(--theme-active-background-color);
      }
    }
  }

  > .menu-panels {
    border: none;

    > .panel-cnt {
      background: var(--theme-content-background-color);
    }
  }
}
</style>
