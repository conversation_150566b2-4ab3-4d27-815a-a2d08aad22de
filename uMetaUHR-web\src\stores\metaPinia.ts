/**
 * 极简状态管理 - 符合MetaPinia设计规范
 * 纯POJO操作 + 路径化访问 + 医疗专用API
 */

import type { 
  MetaPiniaData, 
  Patient, 
  AuditEntry, 
  CurrentUser, 
  Order, 
  MetaPiniaInstance,
  OrderOperations,
  LabTest
} from './metaPinia-types'

let currentUser: CurrentUser = { id: 'system', name: 'System' }

function createMetaPinia<T extends MetaPiniaData>(initialData: T) {
  const data = deepClone(initialData)
  const auditLog: AuditEntry[] = []
  
  const api = {
    get data() {
      return data
    },

    use(path: string, p: { reactive: boolean }) {
      return path.split('.').reduce((obj, key) => {
        if (key.includes('[')) return handleArrayPath(obj, key)
        return obj?.[key]
      }, data)
    },

    // 医疗专用快捷方法
    getCurrentPatient() {
      return data.currentPatient
    },

    switchPatient(patientId: string) {
      const patient = data.patients?.find((p: Patient) => p.id === patientId)
      if (patient) {
        data.currentPatient = patient
        this.logAction(`切换患者 ${patientId}`)
      }
    },

    // 审计日志
    logAction(action: string) {
      auditLog.push({
        timestamp: new Date(),
        action,
        user: currentUser.name
      })
    },

    // 用户管理
    setCurrentUser(user: CurrentUser) {
      currentUser = user
    },

    getAuditLog() {
      return auditLog
    }
  }

  // Add Lab operations
  const labs = {
    addLabResult(test: LabTest) {
      if (!data.currentPatient) {
        throw new Error('No current patient selected')
      }
      if (!data.currentPatient.labs) {
        data.currentPatient.labs = []
      }
      data.currentPatient.labs.push(test)
      api.logAction(`添加检验结果 ${test.id}`)
    },

    flagAbnormalResults() {
      if (!data.currentPatient?.labs) return []
      return data.currentPatient.labs.filter(lab => {
        // Simple example - in real app would check reference ranges
        return typeof lab.result === 'number' && lab.result > 100
      })
    }
  }

  return {
    ...api,
    labs
  } as unknown as Omit<MetaPiniaInstance<T>, 'orders'>
}

// 辅助函数
function handleArrayPath(obj: any, path: string) {
  const match = path.match(/(\w+)\[([^\]]+)\]/)
  if (!match) return undefined
  
  const [, arrayKey, index] = match
  const array = obj[arrayKey]
  
  if (index === '-1') return array[array.length - 1]
  if (index.startsWith('find')) {
    const fn = new Function(`return ${index.replace('find', '')}`)()
    return array.find(fn)
  }
  return array[index]
}

function deepClone<T>(obj: T): T {
  return JSON.parse(JSON.stringify(obj))
}

export { createMetaPinia }
