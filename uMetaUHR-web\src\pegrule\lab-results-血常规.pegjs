start
  = result:labResults {
    return {result}
  }

labResults
  = result:(@testData separator+)+ {
    return result
  }

testData
  = name:name cmpt:(@[<>=]+)? value:number unit:unit? flag:flag? {
    cmpt = (cmpt||[]).join("")
    return {name,cmpt,value,unit,flag}
  }
  / separator {
    console.warn(text())
    return text()
  }

value =
    number
  / cat {return text();}

cat =
    "阴性" 
  / "阳性"
  / "A型"
  / "B型"
  / "O型"


name =
    "鳞癌抗原" { return { name: text(), symbol: 'SCC' } }
  / "高铁血红蛋白" { return { name: text(), symbol: 'MetHb' } }
  / "高密度脂蛋白" { return { name: text(), symbol: 'HDL' } }
  / "骨钙素N端中分子片段测定" { return { name: text(), symbol: 'Osteocalcin_N' } }
  / "镁" { return { name: text(), symbol: 'Mg2+' } }
  / "铁蛋白" { return { name: text(), symbol: 'Ferritin' } }
  / "钾" { return { name: text(), symbol: 'K+' } }
  / "钠" { return { name: text(), symbol: 'Na+' } }
  / "钙" { return { name: text(), symbol: 'Ca2+' } }
  / "还原血红蛋白" { return { name: text(), symbol: 'RHb' } }
  / "载脂蛋白E" { return { name: text(), symbol: 'Apo E' } }
  / "载脂蛋白B" { return { name: text(), symbol: 'Apo B' } }
  / "载脂蛋白A" { return { name: text(), symbol: 'Apo A' } }
  / "谷草转氨酶同工酶" { return { name: text(), symbol: 'AST-iso' } }
  / "谷草转氨酶" { return { name: text(), symbol: 'AST' } }
  / "谷丙转氨酶" { return { name: text(), symbol: 'ALT' } }
  / "视黄醇结合蛋白" { return { name: text(), symbol: 'RBP' } }
  / "血红蛋白总量" { return { name: text(), symbol: 'HB' } }
  / "血红蛋白" { return { name: text(), symbol: 'HB' } }
  / "血糖" { return { name: text(), symbol: 'GLU' } }
  / "血管紧张素转化酶" { return { name: text(), symbol: 'ACE' } }
  / "血清淀粉样蛋白A" { return { name: text(), symbol: 'SAAA' } }
  / "血清淀粉样蛋白" { return { name: text(), symbol: 'SAA' } }
  / "血小板比积" { return { name: text(), symbol: 'PCT' } }
  / "血小板平均体积" { return { name: text(), symbol: 'MPV' } }
  / "血小板压积" { return { name: text(), symbol: 'PCT' } }
  / "血小板分布宽度" { return { name: text(), symbol: 'PDW' } }
  / "血小板" { return { name: text(), symbol: 'PLT' } }
  / "葡萄糖（胸腹水）" { return { name: text(), symbol: 'GLU' } }
  / "葡萄糖" { return { name: text(), symbol: 'GLU' } }
  / "腺苷脱氨酶" { return { name: text(), symbol: 'ADA' } }
  / "脂蛋白（a）" { return { name: text(), symbol: 'Lp(a)' } }
  / "胱抑素C" { return { name: text(), symbol: 'Cystatin C' } }
  / "胃泌素释放肽前体" { return { name: text(), symbol: 'ProGRP' } }
  / "肺泡动脉氧分压差" { return { name: text(), symbol: 'A-aDO2' } }
  / "肌钙蛋白T" { return { name: text(), symbol: 'cTnT' } }
  / "肌酸激酶同工酶" { return { name: text(), symbol: 'CK-iso' } }
  / "肌酸激酶MB同工酶(质量)" { return { name: text(), symbol: 'CK-MB' } }
  / "肌酸激酶" { return { name: text(), symbol: 'CK' } }
  / "肌酐" { return { name: text(), symbol: 'Cr' } }
  / "肌红蛋白" { return { name: text(), symbol: 'Mb' } }
  / "维生素E" { return { name: text(), symbol: 'VitE' } }
  / "维生素D" { return { name: text(), symbol: 'VitD' } }
  / "维生素C" { return { name: text(), symbol: 'VitC' } }
  / "维生素B9" { return { name: text(), symbol: 'VitB9' } }
  / "维生素B6" { return { name: text(), symbol: 'VitB6' } }
  / "维生素B2" { return { name: text(), symbol: 'VitB2' } }
  / "维生素B12" { return { name: text(), symbol: 'VitB12' } }
  / "维生素B1" { return { name: text(), symbol: 'VitB1' } }
  / "维生素A" { return { name: text(), symbol: 'VitA' } }
  / "纤维蛋白原测定" { return { name: text(), symbol: 'FIB' } }
  / "纤维蛋白原" { return { name: text(), symbol: 'FIB' } }
  / "纤维蛋白（原）降解产物" { return { name: text(), symbol: 'FDP' } }
  / "纤维蛋白(原)降解产物" { return { name: text(), symbol: 'FDP' } }
  / "红细胞计数" { return { name: text(), symbol: 'RBC' } }
  / "红细胞平均血红蛋白浓度" { return { name: text(), symbol: 'MCHC' } }
  / "红细胞平均血红蛋白含量" { return { name: text(), symbol: 'MCH' } }
  / "红细胞平均体积" { return { name: text(), symbol: 'MCV' } }
  / "红细胞压积" { return { name: text(), symbol: 'HCT' } }
  / "红细胞分布宽度SD" { return { name: text(), symbol: 'RDW-SD' } }
  / "红细胞分布宽度CV" { return { name: text(), symbol: 'RDW-CV' } }
  / "红细胞分布宽度" { return { name: text(), symbol: 'RDW' } }
  / "红细胞" { return { name: text(), symbol: 'RBC' } }
  / "糖类抗原CA72-4" { return { name: text(), symbol: 'CA72-4' } }
  / "糖类抗原CA242" { return { name: text(), symbol: 'CA242' } }
  / "糖类抗原CA199" { return { name: text(), symbol: 'CA199' } }
  / "糖抗原CA153" { return { name: text(), symbol: 'CA153' } }
  / "糖抗原CA15-3" { return { name: text(), symbol: 'CA15-3' } }
  / "神经烯醇化酶" { return { name: text(), symbol: 'NSE' } }
  / "磷" { return { name: text(), symbol: 'P' } }
  / "碳酸氢根" { return { name: text(), symbol: 'HCO3-' } }
  / "碳氧血红蛋白" { return { name: text(), symbol: 'COHb' } }
  / "碱性磷酸酶" { return { name: text(), symbol: 'ALP' } }
  / "直接胆红素" { return { name: text(), symbol: 'DBIL' } }
  / "白蛋白" { return { name: text(), symbol: 'ALB' } }
  / "白细胞计数" { return { name: text(), symbol: 'WBC' } }
  / "白细胞" { return { name: text(), symbol: 'WBC' } }
  / "白球比" { return { name: text(), symbol: 'A/G' } }
  / "癌胚抗原" { return { name: text(), symbol: 'CEA' } }
  / "甲胎蛋白" { return { name: text(), symbol: 'AFP' } }
  / "甲肝抗体IGM" { return { name: text(), symbol: 'HAV IgM' } }
  / "甘油三脂" { return { name: text(), symbol: 'TG' } }
  / "球蛋白" { return { name: text(), symbol: 'GLOB' } }
  / "淋巴细胞绝对数" { return { name: text(), symbol: 'LYM_ABS' } }
  / "淋巴细胞绝对值" { return { name: text(), symbol: 'LYM#' } }
  / "淋巴细胞百分比" { return { name: text(), symbol: 'LYM%' } }
  / "淋巴细胞数绝对数" { return { name: text(), symbol: 'LYM_ABS' } }
  / "淋巴细胞%" { return { name: text(), symbol: 'LYM' } }
  / "活化部分凝血活酶时间" { return { name: text(), symbol: 'APTT' } }
  / "氯（胸腹水）" { return { name: text(), symbol: 'Cl-' } }
  / "氯" { return { name: text(), symbol: 'Cl-' } }
  / "氧饱和度" { return { name: text(), symbol: 'SpO2' } }
  / "氧合血红蛋白" { return { name: text(), symbol: 'HbO2' } }
  / "氧分压" { return { name: text(), symbol: 'pO2' } }
  / "梅毒(化学发光)" { return { name: text(), symbol: 'Syphilis' } }
  / "标准碳酸氢盐" { return { name: text(), symbol: 'S-HCO3' } }
  / "标准碱剩余" { return { name: text(), symbol: 'SBE' } }
  / "抗凝血酶Ⅲ" { return { name: text(), symbol: 'AT-III' } }
  / "抗凝血酶-Ⅲ" { return { name: text(), symbol: 'ATIII' } }
  / "抗体筛选试验" { return { name: text(), symbol: 'Ab Screening' } }
  / "戊肝病毒抗体IGM" { return { name: text(), symbol: 'HEV IgM' } }
  / "总蛋白" { return { name: text(), symbol: 'TP' } }
  / "总胆红素" { return { name: text(), symbol: 'TBIL' } }
  / "总胆汁酸" { return { name: text(), symbol: 'TBA' } }
  / "总胆固醇" { return { name: text(), symbol: 'TC' } }
  / "平均血红蛋白量" { return { name: text(), symbol: 'MCH' } }
  / "平均血红蛋白浓度" { return { name: text(), symbol: 'MCHC' } }
  / "平均血小板体积" { return { name: text(), symbol: 'MPV' } }
  / "平均红细胞体积" { return { name: text(), symbol: 'MCV' } }
  / "尿酸" { return { name: text(), symbol: 'UA' } }
  / "尿素氮" { return { name: text(), symbol: 'BUN' } }
  / "实际碳酸氢盐" { return { name: text(), symbol: 'HCO3' } }
  / "实际碱剩余" { return { name: text(), symbol: 'ABE' } }
  / "大血小板比率" { return { name: text(), symbol: 'MPV' } }
  / "大型血小板比率" { return { name: text(), symbol: 'MPV%' } }
  / "国际标准化比值" { return { name: text(), symbol: 'INR' } }
  / "嗜酸性粒细胞绝对数" { return { name: text(), symbol: 'EOS_ABS' } }
  / "嗜酸性粒细胞绝对值" { return { name: text(), symbol: 'EO#' } }
  / "嗜酸性粒细胞百分比" { return { name: text(), symbol: 'EO%' } }
  / "嗜酸性粒细胞%" { return { name: text(), symbol: 'EOS' } }
  / "嗜碱性粒细胞绝对数" { return { name: text(), symbol: 'BAS_ABS' } }
  / "嗜碱性粒细胞绝对值" { return { name: text(), symbol: 'BA#' } }
  / "嗜碱性粒细胞百分比" { return { name: text(), symbol: 'BA%' } }
  / "嗜碱性粒细胞%" { return { name: text(), symbol: 'BAS' } }
  / "同型半胱氨酸" { return { name: text(), symbol: 'Hcy' } }
  / "单核细胞绝对数" { return { name: text(), symbol: 'MON_ABS' } }
  / "单核细胞绝对值" { return { name: text(), symbol: 'MON#' } }
  / "单核细胞百分比" { return { name: text(), symbol: 'MON%' } }
  / "单核细胞%" { return { name: text(), symbol: 'MON' } }
  / "前白蛋白" { return { name: text(), symbol: 'PAB' } }
  / "凝血酶时间" { return { name: text(), symbol: 'TT' } }
  / "凝血酶原时间" { return { name: text(), symbol: 'PT' } }
  / "低密度脂蛋白" { return { name: text(), symbol: 'LDL' } }
  / "二氧化碳分压" { return { name: text(), symbol: 'pCO2' } }
  / "乳酸脱氢酶（胸腹水）" { return { name: text(), symbol: 'LDH' } }
  / "乳酸脱氢酶" { return { name: text(), symbol: 'LDH' } }
  / "乳酸" { return { name: text(), symbol: 'Lactate' } }
  / "乙肝表面抗原" { return { name: text(), symbol: 'HBsAg' } }
  / "乙肝表面抗体" { return { name: text(), symbol: 'HBsAb' } }
  / "乙肝核心抗体" { return { name: text(), symbol: 'HBcAb' } }
  / "乙肝e抗原" { return { name: text(), symbol: 'HBeAg' } }
  / "乙肝e抗体" { return { name: text(), symbol: 'HBeAb' } }
  / "中性细胞绝对数" { return { name: text(), symbol: 'NEU_ABS' } }
  / "中性细胞绝对值" { return { name: text(), symbol: 'NEU_ABS' } }
  / "中性细胞%" { return { name: text(), symbol: 'NEU' } }
  / "中性粒细胞绝对值" { return { name: text(), symbol: 'NEU#' } }
  / "中性粒细胞百分比" { return { name: text(), symbol: 'NEU%' } }
  / "丙肝病毒抗体" { return { name: text(), symbol: 'HCV Ab' } }
  / "Rh血型" { return { name: text(), symbol: 'Rh' } }
  / "r-谷氨酰转移酶" { return { name: text(), symbol: 'GGT' } }
  / "pH" { return { name: text(), symbol: 'pH' } }
  / "N末端脑纳素原" { return { name: text(), symbol: 'NT-proBNP' } }
  / "N末端前B型钠尿肽" { return { name: text(), symbol: 'NT-proBNP' } }
  / "HIV抗体" { return { name: text(), symbol: 'HIV Ab' } }
  / "D-二聚体" { return { name: text(), symbol: 'D-Dimer' } }
  / "D-D二聚体" { return { name: text(), symbol: 'D-Dimer' } }
  / "CYFRA21-1" { return { name: text(), symbol: 'CYFRA21-1' } }
  / "CA50" { return { name: text(), symbol: 'CA50' } }
  / "B2-微球蛋白" { return { name: text(), symbol: 'B2M' } }
  / "AST" { return { name: text(), symbol: 'AST' } }
  / "ALT" { return { name: text(), symbol: 'ALT' } }
  / "ABO血型" { return { name: text(), symbol: 'ABO' } }
  / "a-L-岩藻糖苷酶" { return { name: text(), symbol: 'Alpha-L-Fucosidase' } }

number
  = a:[0-9]+ b:("." @[0-9]+)? { return +[...a,...(b ? [".", ...b] : [])].join(""); }
  / "-" n:number { return -n;}

unit
  = compoundUnit

compoundUnit
  = factor? baseUnit? (unitOperator compoundUnit)? {
    return text();
  }
  / "(" compoundUnit ")"

factor = "*" ws* "10" ws* "^" ws* number {
    return text()
}

flag = "↑" / "↓"

baseUnit =
   "μmol" 
  / "μg" 
  / "ug" 
  / "umol"
  / "U" 
  / "u" 
  / "sec" 
  / "R" // 基本单位
  / "pg" 
  / "ng" 
  / "mol" 
  / "mmol" 
  / "mL" 
  / "ml" 
  / "min" 
  / "mg" 
  / "mEq" 
  / "L" 
  / "l" 
  / "IU" 
  / "g" 
  / "fL" 
  / "dL" 
  / "%" 
  / "mmHg"

unitOperator
  = "/" / "*" / "^"  // 支持单位的运算符，支持 /, *, ^

separator
  = (!name .)+ {
    if(text() && text().length > 3) console.warn(text())
    return text()
  }  // 匹配不是 `name` 的部分，直到遇到下一个 `name` 开始

ws = [ \t]