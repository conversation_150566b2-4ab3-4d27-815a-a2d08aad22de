{
  type: 'todo',
  ui: {
    type: 'layout',
    theme: 'side_by_side_2',
    style: {
      child2: {
        border: 'none',
        width: '30rem'
      }
    },
    children: [
      {
        type: 'tocDetail',
        myFavoriteId: '01300-Todo.tocDetail.076',
        toc: {
          type: 'calendar',
          myFavoriteId: '01300-Todo-calendar-018',
          view: {
            type: 'tag',
            model: 'calendarView',
            myFavoriteId: '01300-Todo-tag-022',
            event: {
              name: 'change-display-mode-of-calendar'
            },
            options: [
              {
                value: 'day',
                label: '天'
              },
              {
                value: 'week',
                label: '周'
              },
              {
                value: 'month',
                label: '月'
              }
            ],
            mode: 'single'
          }
        },
        detail: {
          type: 'window',
          style: {
            main: {
              display: 'flex',
              flex: '1 1 auto'
            },
            body: {
              display: 'flex',
              flex: '1 1 auto'
            }
          },
          head: [
            {
              type: 'toolbar',
              children: [
                {
                  type: 'button',
                  event: {
                    name: 'save-calendar-event'
                  },
                  label: '保存'
                },
                {
                  type: 'button',
                  event: {
                    name: 'delete-calendar-event'
                  },
                  label: '删除'
                }
              ]
            }
          ],
          body: [
            {
              type: 'stack',
              menu: [
                {
                  id: 'basicInfo',
                  displayName: '基本信息'
                },
                {
                  id: 'work',
                  displayName: '工作内容'
                }
              ],
              myFavoriteId: '01300-Todo-stack-089',
              tabPosition: 'top',
              children: {
                basicInfo: {
                  type: 'window',
                  body: [
                    {
                      type: 'section',
                      title: '编辑',
                      collapsible: true,
                      myFavoriteId: '01300-Todo-section-099',
                      children: [
                        {
                          label: '名称',
                          type: 'inputText',
                          model: 'event.title'
                        },
                        {
                          label: '开始时间',
                          type: 'dateTime',
                          model: 'event.startTime'
                        },
                        {
                          label: '持续时间',
                          type: 'inputText',
                          dataType: 'n',
                          model: 'event.duration'
                        }
                      ]
                    },
                    {
                      type: 'section',
                      title: '编辑',
                      collapsible: true,
                      myFavoriteId: '01300-Todo.section-123',
                      children: [
                        {
                          type: 'dataViewer',
                          model: ''
                        }
                      ]
                    }
                  ]
                },
                work: {
                  type: 'window',
                  body: [
                    {
                      type: 'section',
                      title: '工作内容',
                      collapsible: true,
                      myFavoriteId: '01300-Todo.section-140',
                      children: [
                        {
                          type: 'toolbar',
                          children: [
                            {
                              type: 'tag',
                              label: '重要',
                              model: 'event.data.importance',
                              style: {
                                label: {
                                  'min-width': 'auto',
                                  padding: '0 0.2rem 0 0.5rem'
                                }
                              },
                              options: [
                                {
                                  value: '1',
                                  label: '1'
                                },
                                {
                                  value: '2',
                                  label: '2'
                                }
                              ],
                              mode: 'single'
                            },
                            {
                              type: 'tag',
                              label: '紧急',
                              model: 'event.data.urgency',
                              style: {
                                label: {
                                  'min-width': 'auto',
                                  padding: '0 0.2rem 0 0.5rem'
                                }
                              },
                              options: [
                                {
                                  value: '1',
                                  label: '1'
                                },
                                {
                                  value: '2',
                                  label: '2'
                                }
                              ],
                              mode: 'single'
                            },
                            {
                              type: 'tag',
                              label: '进度',
                              model: 'event.data.progress',
                              style: {
                                label: {
                                  'min-width': 'auto',
                                  padding: '0 0.2rem 0 0.5rem'
                                }
                              },
                              options: [
                                {
                                  value: '1',
                                  label: '1'
                                },
                                {
                                  value: '2',
                                  label: '2'
                                },
                                {
                                  value: '3',
                                  label: '3'
                                }
                              ],
                              mode: 'single'
                            }
                          ]
                        },
                        {
                          type: 'richTextEditor',
                          model: 'event.data.note',
                          label: '内容'
                        }
                      ]
                    }
                  ]
                }
              }
            }
          ],
          subContext: {
            path: 'detail',
            default: {}
          }
        }
      },
      {
        menu: [
          {
            id: 'data',
            displayName: '数据'
          },
          {
            id: 'data2',
            displayName: '数据2'
          }
        ],
        type: 'stack',
        myFavoriteId: '01300-Todo-stack.247',
        children: {
          data: {
            type: 'section',
            title: '数据',
            children: [
              {
                label: '上下文数据',
                type: 'dataViewer',
                model: ''
              }
            ]
          }
        },
        tabPosition: 'right'
      }
    ],
    myFavoriteId: '01300-Todo-layout.264'
  },
  subContext: {
    path: '/temp.todo',
    default: {}
  }
}
