import type {
  DiagnosisListParams,
  DiagnosisListResponse,
  DiagnosisSearchParams,
  DiagnosisSearchResponse,
  DiagnosisAddParams,
  DiagnosisAddResponse,
  DiagnosisUpdateAffixParams,
  DiagnosisUpdateAffixResponse,
  DiagnosisDeleteParams,
  DiagnosisDeleteResponse,
} from '../../types';
import { BaseApis } from "../base/api";


const Apis = {

  // 诊断列表
  listDiagnosis: async (params: DiagnosisListParams) => {
    return BaseApis.listDiagnosis({
      ...params,
    }) as Promise<DiagnosisListResponse>;
  },

  // 搜索诊断
  searchDiagnosis: async (params: DiagnosisSearchParams) => {
    return BaseApis.searchDiagnosis({
      ...params,
    }) as Promise<DiagnosisSearchResponse>;
  },

  // 新增诊断
  addDiagnosis: async (params: DiagnosisAddParams) => {
    return BaseApis.addDiagnosis({
      ...params,
    }) as Promise<DiagnosisAddResponse>;
  },

  // 修改诊断前后缀
  updateDiagnosisAffix: async (params: DiagnosisUpdateAffixParams) => {
    return BaseApis.updateDiagnosisAffix({
      ...params,
    }) as Promise<DiagnosisUpdateAffixResponse>;
  },

  // 删除诊断
  deleteDiagnosis: async (params: DiagnosisDeleteParams) => {
    return BaseApis.deleteDiagnosis({
      ...params,
    }) as Promise<DiagnosisDeleteResponse>;
  },
}

export default Apis;