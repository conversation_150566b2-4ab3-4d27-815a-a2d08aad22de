{
  ui: {
    type: 'tocDetail',
    style: {
      toc: {
        width: '10rem'
      }
    },
    myFavoriteId: '01100-DbFile-toc-detail-09',
    toc: {
      type: 'toc',
      myFavoriteId: '01100-DbFile-toc-10',
      event: {
        listItems: 'list-db-file',
        selectItem: 'select-db-file'
      },
      columnDef: [
        {
          field: 'name',
          displayName: '名称'
        },
        {
          field: 'servicetime',
          format: 'tms',
          displayName: '时间'
        }
      ],
      selectedLine: 0
    },
    detail: {
      body: [
        {
          type: 'window',
          body: [
            {
              type: 'inputText',
              label: '名称',
              model: 'dbFile.name'
            },
            {
              type: 'dateTime',
              label: '时间',
              model: 'dbFile.servicetime'
            },
            {
              type: 'tag',
              label: '标签',
              model: 'dbFile.tag2.tags'
            },
            {
              type: 'richTextEditor',
              style: {
                main: {
                  margin: '2px'
                }
              },
              shouldNotGroupWhenFull: true,
              model: 'dbFile.data.content'
            }
          ],
          subContext: {
            path: 'detail',
            default: {}
          }
        }
      ],
      head: [
        {
          type: 'toolbar',
          children: [
            {
              type: 'button',
              event: {
                name: 'new-db-file'
              },
              label: '新建'
            },
            {
              type: 'button',
              event: {
                name: 'save-db-file'
              },
              label: '保存'
            },
            {
              type: 'button',
              event: {
                name: 'delete-db-file'
              },
              label: '删除'
            }
          ]
        }
      ],
      type: 'window'
    }
  },
  hkeys: [
    {
      key: 'ctrl+s'
    },
    {
      key: 'command+s'
    }
  ]
}
