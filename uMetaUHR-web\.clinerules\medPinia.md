在使用任何 store 时，遵循如下的说明：


# MetaPinia 设计

## 1. 核心设计思想

**设计原则**：
- 不要包含或者引用Pinia库。仅仅参考其API设计方法。
- 纯POJO操作（无reactive/ref等包装）
- 路径化数据访问（`patients[0].orders`）
- 医疗操作友好API
- 零配置起步，按需增强
- 按照 EMR, patient, order, note, diagnosis 等场景拆分。
- 按照本文件的例子，生成测试用例 （./test）

## 2. 完整API设计

### 2.1 核心方法

```typescript
interface MetaPinia {
  // 初始化医疗数据
  createMetaPinia<T extends MetaPiniaData>(initialData: T): MetaPiniaInstance<T>
  
  // 数据访问
  useMetaPinia<T = any>(path: string, options?: {
    reactive?: boolean   // 是否启用响应式
    audit?: boolean      // 是否记录操作日志
  }): T
}
```

### 2.2 数据类型

```typescript
interface MetaPiniaData {
  [key: string]: any
  patients?: Patient[]
  currentPatient?: Patient | null
}

interface Patient {
  id: string
  name: string
  orders?: Order[]
  labs?: LabTest[]
}

interface Order {
  id: string
  drug: string
  dose: string
  frequency: string
}
```

## 3. 总实现代码（供参考）

```typescript
function createMetaPinia<T extends MetaPiniaData>(initialData: T) {
  const data = deepClone(initialData)
  const auditLog: AuditEntry[] = []
  
  const api = {
    get data() {
      return data
    },
    
    use(path: string, options = { reactive: false }) {
      const result = path.split('.').reduce((obj, key) => {
        if (key.includes('[')) return handleArrayPath(obj, key)
        return obj?.[key]
      }, data)
      
      return options.reactive ? reactive(result) : result
    },
    
    // 医疗专用快捷方法
    getCurrentPatient() {
      return data.currentPatient
    },
    
    switchPatient(patientId: string) {
      const patient = data.patients?.find(p => p.id === patientId)
      if (patient) {
        data.currentPatient = patient
        this.logAction(`切换患者 ${patientId}`)
      }
    },
    
    // 审计日志
    logAction(action: string) {
      auditLog.push({
        timestamp: new Date(),
        action,
        user: currentUser
      })
    },
    
    getAuditLog() {
      return auditLog
    }
  }
  
  return api
}

// 辅助函数
function handleArrayPath(obj: any, path: string) {
  const match = path.match(/(\w+)\[([^\]]+)\]/)
  if (!match) return undefined
  
  const [, arrayKey, index] = match
  const array = obj[arrayKey]
  
  if (index === '-1') return array[array.length - 1]
  if (index.startsWith('find')) {
    const fn = new Function(`return ${index.replace('find', '')}`)()
    return array.find(fn)
  }
  return array[index]
}
```

## 4. 医疗场景扩展方法

```typescript
// 医嘱专用操作扩展
interface OrderOperations {
  createOrder(order: Order): void
  updateDose(orderId: string, newDose: string): void
  deleteOrder(orderId: string): void
}

// 检验结果扩展
interface LabOperations {
  addLabResult(test: LabTest): void
  flagAbnormalResults(): LabTest[]
}

// 合并扩展
type MetaPiniaInstance<T> = T & {
  use: (path: string, options?: any) => any
  orders: OrderOperations
  labs: LabOperations
}
```

## 5. 完整使用示例

### 5.1 初始化医疗系统

```typescript
const med = createMetaPinia({
  patients: [
    {
      id: 'p001',
      name: '王五',
      orders: [
        { id: 'o001', drug: '胰岛素', dose: '10IU' }
      ]
    }
  ],
  currentPatient: null
})
```

### 5.2 全流程操作

```typescript
// 1. 获取患者列表
const patientList = med.use('patients')

// 2. 打开患者
med.switchPatient('p001')

// 3. 查看当前患者医嘱
const orders = med.use('currentPatient.orders')

// 4. 创建新医嘱 (两种方式)
// 方式一：直接操作
med.data.currentPatient?.orders?.push({
  id: 'o002',
  drug: '二甲双胍',
  dose: '500mg'
})

// 方式二：使用扩展API
med.orders.createOrder({
  id: 'o003',
  drug: '阿卡波糖',
  dose: '50mg'
})

// 5. 修改医嘱剂量
med.orders.updateDose('o001', '15IU')

// 6. 删除医嘱
med.orders.deleteOrder('o002')

// 7. 关闭患者
med.data.currentPatient = null

// 8. 查看操作日志
console.log(med.getAuditLog())
```

## 6. React/Vue 集成示例

### 6.2 Vue 集成

```vue

<template>
  <div v-if="currentPatient">
    <h2>{{ currentPatient.name }}的检验结果</h2>
    <table>
      <tr v-for="lab in labs" :key="lab.id">
        <td>{{ lab.name }}</td>
        <td>{{ lab.result }}</td>
      </tr>
    </table>
  </div>
</template>

<script setup>
  const currentPatient = med.use('currentPatient', { reactive: true })
  const labs = computed(() => currentPatient.value?.labs || [])
</script>
```

## 7. 性能优化设计

```typescript
// 懒加载大数据
function setupLazyLoading() {
  const loadedData = new WeakMap()
  
  return {
    load(path: string) {
      if (!loadedData.has(path)) {
        loadedData.set(path, fetchMedicalData(path))
      }
      return loadedData.get(path)
    }
  }
}

// 使用示例
const labResults = await med.lazy.load('currentPatient.labs[large]')
```

## 8. 医疗校验规则

```typescript
// 内置医疗校验
interface ValidationRule {
  path: string
  validate: (value: any) => boolean
  message: string
}

function addValidation(rules: ValidationRule[]) {
  return {
    check() {
      return rules.map(rule => ({
        ...rule,
        valid: rule.validate(med.use(rule.path))
      }))
    }
  }
}

// 使用示例
const validator = addValidation([
  {
    path: 'currentPatient.orders[].dose',
    validate: dose => /^\d+(mg|IU|g)$/.test(dose),
    message: '剂量格式错误'
  }
])

validator.check()
```

## 9. 设计权衡说明

| 传统方案          | MetaPinia方案        | 医疗场景优势               |
|-------------------|----------------------|--------------------------|
| 需要定义store结构 | 直接使用任意POJO      | 快速适配医疗数据结构变化    |
| 严格类型定义      | 动态路径访问          | 方便处理不规则医疗数据      |
| 集中式状态管理    | 层级化自然分区        | 符合医疗数据组织方式        |
| 通用API           | 医疗专用快捷方法      | 提升医嘱等高频操作效率      |

## 10. 典型医疗场景覆盖率

1. **门诊流程**
    - 患者挂号 → 医嘱开立 → 检验申请 → 处方发药
2. **住院流程**
    - 入院评估 → 长期医嘱 → 护理记录 → 出院带药
3. **急诊流程**
    - 分诊评估 → 紧急处置 → 抢救记录 → 转归处理
4. **医技流程**
    - 检查申请 → 影像采集 → 报告撰写 → 结果推送

这个设计通过极简的API表面，内部实现了医疗场景需要的所有关键功能，既保持了开发的简单性，又能满足医疗信息系统的专业需求。