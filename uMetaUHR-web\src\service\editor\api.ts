/**
 * 编辑器数据同步相关API封装
 * 基于BaseApis进行二次封装，提供编辑器专用的数据查询接口
 */

import { BaseApis } from '@/service/base/api'
import { DataTablesEnum } from '@/enums/tables'
import { QueryOrder } from '@/types/api'

// 同步数据请求项
export interface SyncDataItem {
  index: number            // 序号，确保返回顺序
  name: string            // 唯一标识（对应编辑器元素name）
  sourceKey: string       // dynamicConfig.sourceKey
  table: string          // 数据库表名
  column: string         // 数据库字段名
  condition?: string     // 查询条件
}

// 同步数据请求
export interface SyncDataRequest {
  items: SyncDataItem[]
  patientId: string      // 患者ID，用于构建查询条件
}

// 同步数据结果
export interface SyncDataResult {
  index: number          // 对应请求的序号
  name: string          // 对应请求的name
  text: string          // 查询结果的文本值
  success: boolean      // 查询是否成功
  error?: string        // 错误信息
}

// 批量同步数据响应
export interface SyncDataResponse {
  items: SyncDataResult[]
  success: boolean
  message?: string
}

// 单个字段查询请求
export interface SingleFieldRequest {
  table: string
  column: string
  condition: string
}

// 单个字段查询响应
export interface SingleFieldResponse {
  value: string
  success: boolean
  error?: string
}

/**
 * 编辑器数据API类
 */
class EditorDataApis {
  
  /**
   * 批量查询同步数据
   * @param request 批量查询请求
   * @returns 批量查询结果
   */
  static async batchSyncData(request: SyncDataRequest): Promise<SyncDataResponse> {
    try {
      // 构建批量查询参数
      const queryPromises = request.items.map(async (item, index) => {
        try {
          // 构建查询条件
          const condition = item.condition || `patient_id = '${request.patientId}'`
          
          // 执行单个查询
          const result = await BaseApis.listData({
            table: item.table as any,
            columns: [item.column],
            condition: condition,
            limit: 1
          })
          
          // 提取结果
          const value = result && result.length > 0 ? result[0][item.column] : ''
          
          return {
            index: item.index,
            name: item.name,
            text: String(value || ''),
            success: true
          } as SyncDataResult
          
        } catch (error) {
          return {
            index: item.index,
            name: item.name,
            text: '',
            success: false,
            error: error instanceof Error ? error.message : '查询失败'
          } as SyncDataResult
        }
      })
      
      // 等待所有查询完成
      const results = await Promise.all(queryPromises)
      
      // 按index排序确保顺序
      const sortedResults = results.sort((a, b) => a.index - b.index)
      
      return {
        items: sortedResults,
        success: true
      }
      
    } catch (error) {
      console.error('批量同步数据失败:', error)
      return {
        items: [],
        success: false,
        message: error instanceof Error ? error.message : '批量查询失败'
      }
    }
  }
  
  /**
   * 单个字段查询
   * @param request 单个字段查询请求
   * @returns 查询结果
   */
  static async querySingleField(request: SingleFieldRequest): Promise<SingleFieldResponse> {
    try {
      const result = await BaseApis.listData({
        table: request.table as any,
        columns: [request.column],
        condition: request.condition,
        limit: 1
      })
      
      const value = result && result.length > 0 ? result[0][request.column] : ''
      
      return {
        value: String(value || ''),
        success: true
      }
      
    } catch (error) {
      console.error('单个字段查询失败:', error)
      return {
        value: '',
        success: false,
        error: error instanceof Error ? error.message : '查询失败'
      }
    }
  }
  
  /**
   * 查询患者基本信息
   * @param patientId 患者ID
   * @param columns 需要查询的字段列表
   * @returns 患者信息
   */
  static async getPatientInfo(patientId: string, columns: string[] = ['*']) {
    return BaseApis.listData({
      table: DataTablesEnum.PATIENT_BASIC_INFO,
      columns: columns,
      condition: `patient_id = '${patientId}'`,
      limit: 1
    })
  }
  
  /**
   * 查询患者扩展信息
   * @param patientId 患者ID
   * @param columns 需要查询的字段列表
   * @returns 患者扩展信息
   */
  static async getPatientExtInfo(patientId: string, columns: string[] = ['*']) {
    return BaseApis.listData({
      table: DataTablesEnum.PATIENT_EXTENSION_INFO,
      columns: columns,
      condition: `patient_id = '${patientId}'`,
      limit: 1
    })
  }
  
  /**
   * 查询患者医嘱信息
   * @param patientId 患者ID
   * @param columns 需要查询的字段列表
   * @param limit 限制条数
   * @returns 医嘱信息列表
   */
  static async getPatientOrders(patientId: string, columns: string[] = ['*'], limit: number = 50) {
    return BaseApis.listData({
      table: DataTablesEnum.ORDER_PROC,
      columns: columns,
      condition: `patient_id = '${patientId}' AND status = 'active'`,
      orderBy: [{ field: 'created_time', direction: QueryOrder.DESC }],
      limit: limit
    })
  }
  
  /**
   * 查询患者诊断信息
   * @param patientId 患者ID
   * @param columns 需要查询的字段列表
   * @param limit 限制条数
   * @returns 诊断信息列表
   */
  static async getPatientDiagnosis(patientId: string, columns: string[] = ['*'], limit: number = 20) {
    return BaseApis.listData({
      table: DataTablesEnum.DIAGNOSIS,
      columns: columns,
      condition: `patient_id = '${patientId}'`,
      orderBy: [{ field: 'recorded_date', direction: QueryOrder.DESC }],
      limit: limit
    })
  }
  
  /**
   * 通用的患者数据查询
   * @param patientId 患者ID
   * @param table 表名
   * @param columns 字段列表
   * @param additionalCondition 额外查询条件
   * @param limit 限制条数
   * @returns 查询结果
   */
  static async getPatientData(
    patientId: string, 
    table: string, 
    columns: string[] = ['*'],
    additionalCondition?: string,
    limit?: number
  ) {
    let condition = `patient_id = '${patientId}'`
    if (additionalCondition) {
      condition += ` AND ${additionalCondition}`
    }
    
    return BaseApis.listData({
      table: table as any,
      columns: columns,
      condition: condition,
      limit: limit
    })
  }
}

export { EditorDataApis }
