/**
 * 编辑器数据同步相关API封装
 * 基于BaseApis进行二次封装，只提供单次和批量查询接口
 */

import { BaseApis } from '@/service/base/api'

// 同步数据请求项
export interface SyncDataItem {
  index: number            // 序号，确保返回顺序
  name: string            // 唯一标识（对应编辑器元素name）
  sourceKey: string       // dynamicConfig.sourceKey
  table: string          // 数据库表名
  column: string         // 数据库字段名
  condition?: string     // 查询条件
}

// 批量同步数据请求
export interface SyncDataRequest {
  items: SyncDataItem[]
  patientId: string      // 患者ID，用于构建查询条件
}

// 同步数据结果
export interface SyncDataResult {
  index: number          // 对应请求的序号
  name: string          // 对应请求的name
  text: string          // 查询结果的文本值
  success: boolean      // 查询是否成功
  error?: string        // 错误信息
}

// 批量同步数据响应
export interface SyncDataResponse {
  items: SyncDataResult[]
  success: boolean
  message?: string
}

// 单个字段查询请求
export interface SingleFieldRequest {
  table: string
  column: string
  condition: string
}

// 单个字段查询响应
export interface SingleFieldResponse {
  value: string
  success: boolean
  error?: string
}

/**
 * 编辑器数据API类
 */
class EditorDataApis {

  /**
   * 批量查询同步数据
   * 基于BaseApis.listData进行批量调用，保证返回顺序
   */
  static async batchSyncData(request: SyncDataRequest): Promise<SyncDataResponse> {
    try {
      // 构建批量查询参数
      const queryPromises = request.items.map(async (item) => {
        try {
          // 构建查询条件
          const condition = item.condition || `patient_id = '${request.patientId}'`

          // 使用现有的BaseApis.listData
          const result = await BaseApis.listData({
            table: item.table as any,
            columns: [item.column],
            condition: condition,
            limit: 1
          }) as any[]

          // 提取结果
          const value = result && result.length > 0 ? result[0][item.column] : ''

          return {
            index: item.index,
            name: item.name,
            text: String(value || ''),
            success: true
          } as SyncDataResult

        } catch (error) {
          return {
            index: item.index,
            name: item.name,
            text: '',
            success: false,
            error: error instanceof Error ? error.message : '查询失败'
          } as SyncDataResult
        }
      })

      // 等待所有查询完成
      const results = await Promise.all(queryPromises)

      // 按index排序确保顺序
      const sortedResults = results.sort((a, b) => a.index - b.index)

      return {
        items: sortedResults,
        success: true
      }

    } catch (error) {
      console.error('批量同步数据失败:', error)
      return {
        items: [],
        success: false,
        message: error instanceof Error ? error.message : '批量查询失败'
      }
    }
  }

  /**
   * 单个字段查询
   * 基于BaseApis.listData进行单次调用
   */
  static async querySingleField(request: SingleFieldRequest): Promise<SingleFieldResponse> {
    try {
      // 使用现有的BaseApis.listData
      const result = await BaseApis.listData({
        table: request.table as any,
        columns: [request.column],
        condition: request.condition,
        limit: 1
      }) as any[]

      const value = result && result.length > 0 ? result[0][request.column] : ''

      return {
        value: String(value || ''),
        success: true
      }

    } catch (error) {
      console.error('单个字段查询失败:', error)
      return {
        value: '',
        success: false,
        error: error instanceof Error ? error.message : '查询失败'
      }
    }
  }
}

export { EditorDataApis }
