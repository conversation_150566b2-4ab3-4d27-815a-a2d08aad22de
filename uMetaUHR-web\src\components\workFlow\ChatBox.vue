<template>
  <div class="chat-box">
    <div v-if="props?.schema?.header" class="chat-box-header">
      <component
        :is="loadComponent(props.schema?.header?.type)"
        :context="getContext(props)"
        :schema="props.schema?.header"
      />
    </div>
    <div v-if="props?.schema?.body" class="chat-box-body">
      <component
        :is="loadComponent(body?.type)"
        v-for="(body, key) in props?.schema?.body"
        :key="key"
        :context="getContext(props)"
        :schema="body"
      />
    </div>
    <div v-if="props?.schema?.footer" class="chat-box-footer">
      <component
        :is="loadComponent(props.schema?.footer?.type)"
        :context="getContext(props)"
        :schema="props.schema?.footer"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { defineProps } from 'vue'
import { loadComponent } from '../componentRegistry'
import { getContext } from '@/lib/getContext'

const props = defineProps<{
  schema: {
    type: string
    children: Array<any>
  }
  context: { [key: string]: any }
}>()

defineExpose({ compName: props?.schema?.type || '-' })
</script>

<style lang="scss" scoped>
.chat-box {
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
  overflow: auto;

  > div {
    display: flex;
    flex: 1 1 auto;
    margin: 3px;
  }

  .chat-box-header,
  .chat-box-footer {
    flex: 0 0 auto;
  }

  .chat-box-body {
    display: flex;
    flex-direction: column;
    flex: 1 1 auto;
    overflow-y: auto;
    padding: 10px;
  }
}
</style>
