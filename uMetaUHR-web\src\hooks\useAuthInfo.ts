import Apis from "@/service"

function createUseAuthInfo () {
  const authInfo: {
    authServerUrl: string
    nettySocket: string
    uapClientId: string
  } = {
    authServerUrl: '',
    nettySocket: '',
    uapClientId: '',
  }

  return function () {
    async function fetchAuthInfo (appId: string) {
      try {
        const res = await Apis.getAuthInfo(`developerTools-${appId}`)
        const env = JSON.parse(res.env)
        authInfo.authServerUrl = env.authServerUrl
        authInfo.nettySocket = env.nettySocket
        authInfo.uapClientId = env.uapClientId
      } catch (e) {
        console.error(e)
      }
    }

    return {
      authInfo,
      fetchAuthInfo,
    }
  }
}

export const useAuthInfo = createUseAuthInfo()
