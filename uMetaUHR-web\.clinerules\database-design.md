# 数据库设计标准

## 1. 通用原则
- 使用PostgreSQL作为主数据库系统
- 所有表必须包含主键(推荐自增整数类型)
- 数据模型应保持高效和一致
- 所有时间戳字段应使用整数类型(1970-01-01 00:00:00 UTC以来的秒数)，包括DATE/TIME类型.

## 2. 命名规范

### 表名
- 使用小写字母和下划线(snake_case)
- 使用复数名词(如`users`, `order_items`)
- 避免使用数据库关键字作为表名
- 关联表命名: `表1_表2`(如`user_role`)

### 列名
- 使用小写字母和下划线
- 外键: `被引用表_id`(如`user_id`)
- 布尔字段: 使用`is_`, `has_`, `can_`前缀(如`is_active`)

### 索引
- 普通索引: `idx_表名_列名`
- 唯一索引: `uk_表名_列名`
- 主键: `pk_表名`

## 3. 表设计

### 基本规则
- 每张表必须有主键,名称为id
- 主键优先使用自增整数(特殊情况下使用UUID)，推荐使用 id INT GENERATED BY DEFAULT AS IDENTITY
- 避免复合主键
- 所有表必须有注释说明其用途. 并且符合 postgresql 的语法。

### 列设计
- 选择合适的数据类型(如尽可能使用TINYINT而非INT)
- 避免使用ENUM类型 - 改用引用表
- 所有列必须有默认值(NULL也可接受)
- 字符串列必须定义长度
- 货币值使用DECIMAL(避免FLOAT/DOUBLE)

## 4. 关系设计
- 一对多: 在"多"的一方添加外键
- 多对多: 使用关联表
- 避免级联删除
- 所有关系必须通过外键明确定义

## 5. 索引设计

### 最佳实践
- 主键自动创建聚集索引
- 为频繁查询的列创建索引
- 为用于排序/分组的列创建索引
- 复合索引遵循最左前缀原则
- 避免为低选择性列创建索引(如性别)

### 索引限制
- 每表最多5个索引
- 每个索引最多5列
- 避免冗余索引

## 6. SQL标准

### 查询优化
- 避免SELECT * - 只查询需要的列
- 使用EXPLAIN分析查询计划
- 避免在WHERE子句中使用函数操作
- 尽可能用UNION ALL替代OR条件
- 大数据集分页使用延迟连接

### 事务
- 保持事务尽可能短
- 避免在事务中进行远程调用
- 设置合适的事务隔离级别

## 7. 安全标准
- 禁止使用字符串拼接SQL - 使用参数化查询
- 生产环境数据库账户应保持最小权限
- 加密敏感数据(密码、ID等)
- 定期备份并验证

## 8. 文档
- 维护完整的ER图和数据字典
- 记录模式变更历史
- 为复杂业务逻辑添加注释

## 9. 性能指南
- 当表超过500万行时考虑分区
- 将大字段(TEXT/BLOB)移至单独的表
- 合理使用数据库分区
- 定期维护(重建索引、更新统计信息)

## 10. 其他建议
- 所有表添加created_at和updated_at字段
- 优先使用逻辑删除而非物理删除
- 记录重要操作日志
- 定期评审和优化数据库设计
