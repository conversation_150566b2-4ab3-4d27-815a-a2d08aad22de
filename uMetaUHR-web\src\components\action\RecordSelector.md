## 控件名称

RecordSelector

## 控件描述

RecordSelector 是一个带有自动完成功能的输入控件，主要用于从预定义选项中选择记录。

### 组件定位
- 表单元素：用于数据输入和选择
- 搜索选择器：提供搜索和选择功能

### 核心功能
1. 输入时实时搜索建议
2. 键盘导航支持（上下箭头、Tab、Enter等）
3. 表格形式展示建议结果
4. 支持高亮显示匹配内容
5. 可配置的列定义和显示格式

### 使用场景
1. 患者记录选择
2. 药品搜索选择
3. 诊断代码选择

### 交互规范
1. 输入至少2个字符触发搜索
2. 使用键盘导航选择建议项
3. 点击或按Enter确认选择
4. ESC键清除建议列表

## schema

RecordSelector 通过 schema 属性进行配置，主要字段包括：

- `label`: 输入框标签文本
- `model`: 数据绑定字段路径
- `inputType`: 输入框类型（默认为'text'）
- `columnDef`: 表格列定义数组
  - `field`: 数据字段名
  - `displayName`: 列显示名称
  - `style`: 列样式
- `event`: 事件配置
  - `selectSuggestion`: 选择建议项时触发的事件名
  - `queryCondition`: 查询条件注入键名

### 示例

```json
{
  "label": "选择患者",
  "model": "patient.id",
  "inputType": "text",
  "columnDef": [
    {
      "field": "name",
      "displayName": "姓名"
    },
    {
      "field": "id",
      "displayName": "ID"
    }
  ],
  "event": {
    "selectSuggestion": "select-patient",
    "queryCondition": "patient-query-condition"
  }
}
```

注意事项：
1. `model` 字段必须指定正确的数据路径
2. 至少需要配置一个列定义才能显示建议表格
3. 搜索功能依赖于外部 typesense 服务

## context

RecordSelector 使用 Vue 的依赖注入机制与父组件通信。

### 数据流图

```mermaid
graph TD
    Parent[父组件] -->|provide| QueryCondition[查询条件]
    Parent -->|provide| SelectHandler[选择处理器]
    RecordSelector -->|inject| QueryCondition
    RecordSelector -->|inject| SelectHandler
    RecordSelector -->|更新| Context[上下文数据]
```

关键上下文交互：
1. 通过 `queryCondition` 注入点获取搜索条件
2. 通过 `selectSuggestion` 注入点处理选择事件
3. 直接更新上下文中的绑定字段

## 事件

RecordSelector 处理以下类型的事件：

### 用户事件
1. 输入事件 (`@input`)
   - 触发实时搜索
   - 更新绑定字段值
2. 键盘导航事件 (`@keydown`)
   - 上下箭头：选择建议项
   - Enter：确认选择
   - ESC：清除建议
3. 鼠标点击事件 (`@mousedown`)
   - 选择表格中的建议项

### 系统事件
1. `selectSuggestion`
   - 参数：包含选中记录的文档对象
   - 作用：通知父组件选择变化

### provide 内容示例
父组件应提供：
```javascript
provide('select-patient', (doc) => {
  // 处理选择逻辑
})

provide('patient-query-condition', async () => {
  return {
    search: {
      docType: 'patients',
      param: {
        query_by: 'name,id'
      }
    }
  }
})
```

## 其他注意事项

1. 下拉列表使用 Teleport 渲染到 body，避免被父容器样式影响
2. 输入少于2个字符不会触发搜索
3. 表格行高亮使用鼠标悬停和键盘选择两种方式
4. 错误状态会显示红色边框和错误标记
5. 组件依赖外部 typesense 搜索服务
