export function deepCopy<T>(original: T): T {
  try {
    return JSON.parse(JSON.stringify(original))
  } catch (error) {
    console.error('Deep copy failed:', error)
    return original
  }
}

type DeepMergeOptions = {
  mode?: number
  removeExtra?: boolean
}

function getObjectKey(obj: any): string {
  if (obj === null || obj === undefined) return ''
  if (typeof obj === 'string') return obj

  return JSON.stringify(
    Object.keys(obj)
      .sort()
      .reduce((result: any, key: string) => {
        result[key] = obj[key]
        return result
      }, {})
  )
}

function deepMergeRcr(
  depth: number,
  dest: any,
  original: any,
  options: DeepMergeOptions = { mode: 1, removeExtra: false }
): any {
  if (depth <= 0 || typeof original === 'function') return dest
  if (original === null || original === undefined) return dest

  if (Array.isArray(original)) {
    const merged = Array.isArray(dest) ? [...dest] : []
    const seen = new Map<string, any>(merged.map((item) => [getObjectKey(item), item]))
    if (dest === undefined) return original !== null ? original : dest

    original.forEach((item) => {
      if (
        item === null ||
        item === undefined ||
        (typeof item === 'object' && Object.keys(item).length === 0)
      )
        return
      const key = getObjectKey(item)
      if (seen.has(key)) {
        const origItem = seen.get(key)
        deepMergeRcr(depth - 1, origItem, item, options)
        return
      }
      seen.set(key, item)
      merged.push(item)
    })
    return merged
  }

  if (typeof original === 'object' && typeof dest === 'object' && !Array.isArray(dest)) {
    const copy = dest || {}
    if (options.removeExtra) {
      Object.keys(copy).forEach((key) => {
        if (!Object.prototype.hasOwnProperty.call(original, key)) delete copy[key]
      })
    }
    Object.entries(original).forEach(([key, value]) => {
      if (
        value === undefined ||
        value === null ||
        (typeof value === 'object' && Object.keys(value).length === 0)
      )
        return
      if (options.mode !== 2 || !copy[key]) {
        const rst = deepMergeRcr(depth - 1, copy[key], value, options)
        if (rst === null || rst === undefined) return
        copy[key] = rst
      }
    })
    return copy
  }

  return original
}

export function deepAssign(dest: any, ...sources: any[]) {
  if (!dest || !sources.length) return dest
  sources.forEach((source) => deepMergeRcr(50, dest, source, { mode: 1, removeExtra: true }))
  return dest
}

export function deepMerge(dest: any, ...sources: any[]): any {
  if (!dest || !sources.length) return dest
  sources.forEach((source) => {
    dest = deepMergeRcr(50, dest, source)
  })
  return dest
}
