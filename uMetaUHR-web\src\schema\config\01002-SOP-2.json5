{
  type: 'sop',
  ui: {
    type: 'layout2',
    myFavoriteId: '01000-SOP-2.side2.006',
    children: [
      null,
      {
        type: 'stack',
        myFavoriteId: '01002-SOP-2-stack-32',
        tabPosition: 'left',
        menu: [
          {
            id: 'qc-point',
            displayName: 'SOP质控规则'
          },
          {
            id: 'prompts',
            displayName: '提示词'
          },
          {
            id: 'note-command',
            displayName: '病历命令'
          },
          {
            id: 'article',
            displayName: '稿件'
          },
          {
            id: 'blockly',
            displayName: 'Blockly'
          },
          {
            id: 'typesense',
            displayName: 'typesense'
          }
        ],
        children: {
          blockly: {
            type: 'external',
            externalSchema: '01400-Blockly'
          },
          typesense: {
            type: 'external',
            externalSchema: '00341-typesense'
          },
          'qc-point': {
            type: 'dbFile',
            file: {
              type: 'SOP-guideline'
            },
            subContext: {
              path: 'SOPGuideLine',
              default: {}
            }
          },
          prompts: {
            type: 'dbFile',
            file: {
              type: 'SOP-prompts'
            },
            subContext: {
              path: 'prompts',
              default: {}
            }
          },
          'note-command': {
            type: 'dbFile',
            file: {
              type: 'SOP-note-command'
            },
            subContext: {
              path: 'noteCommand',
              default: {}
            }
          },
          article: {
            type: 'dbFile',
            file: {
              type: 'article'
            },
            subContext: {
              path: 'article',
              default: {}
            }
          }
        },
        subContext: {
          path: 'dbFile',
          default: {}
        }
      },
      {
        menu: [
          {
            id: 'text-generation',
            displayName: '文本生成'
          },
          {
            id: 'data',
            displayName: '数据'
          }
        ],
        type: 'stack',
        myFavoriteId: '01002-SOP-2.stack.179',
        children: {
          data: {
            type: 'section',
            title: '数据',
            children: [
              {
                type: 'dataViewer',
                model: ''
              }
            ]
          },
          'text-generation': {
            type: 'window',
            title: '文本生成',
            style: {
              title: {
                background: 'lightblue',
                'font-size': '10pt'
              }
            },
            head: [
              {
                type: 'toolbar',
                style: {
                  'justify-content': 'right'
                },
                children: [
                  {
                    type: 'button',
                    event: {
                      name: 'generate-text'
                    },
                    label: '生成文本'
                  }
                ]
              }
            ],
            body: [
              {
                type: 'section',
                title: '原始文本',
                collapsible: true,
                myFavoriteId: '01002-SOP-2.137',
                children: [
                  {
                    type: 'emrEditor',
                    model: 'inputText'
                  }
                ]
              },
              {
                type: 'section',
                title: '模版',
                collapsible: true,
                myFavoriteId: '01002-SOP-2.section.150',
                children: [
                  {
                    type: 'emrEditor',
                    model: 'template'
                  }
                ]
              },
              {
                type: 'section',
                title: '数据',
                collapsible: true,
                myFavoriteId: '01002-SOP-2.161',
                children: [
                  {
                    type: 'dataViewer',
                    model: ''
                  }
                ]
              }
            ],
            subContext: {
              path: 'detail',
              default: {}
            }
          }
        },
        tabPosition: 'right'
      }
    ]
  },
  subContext: {
    path: '/temp.sop',
    default: {}
  }
}
