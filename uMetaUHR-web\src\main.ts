import './assets/main.css'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import { createApp, provide } from 'vue'
import { createPinia } from 'pinia'
import store from './store'

import App from './App.vue'
import axios from 'axios'
import { library } from '@fortawesome/fontawesome-svg-core' //18k for every icon, 1M in total. so, there should be some compromise.
import { fas } from '@fortawesome/free-solid-svg-icons'

import * as Icons from '@ant-design/icons-vue';

library.add(fas)

const app = createApp(App)

app
  .use(createPinia()) //
  .component('FontAwesomeIcon', FontAwesomeIcon)
  .provide('store', store)
  .mount('#app')

for (const key in Icons) {
  if (Object.prototype.hasOwnProperty.call(Icons, key)) {
    app.component(key, Icons[key as keyof typeof Icons]);
  }
}

app.use(createPinia())
provide('app', app)
console.log('start')
axios.defaults.withCredentials = true
