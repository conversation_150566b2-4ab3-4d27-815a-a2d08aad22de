!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.EmrEditorSDK=t():e.EmrEditorSDK=t()}(window,(function(){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=0)}([function(e,t,n){"use strict";function r(){var e=window.__EMR_EDITOR_VER__;return!(!e||!0!==window[e])}n.r(t),n.d(t,"Editor",(function(){return I}));var o,i=[],c=[];function u(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];i.push(e),clearTimeout(o),o=setTimeout((function(){c.forEach((function(e){console.dir(e)})),i.forEach((function(e){console.log.apply(void 0,e)})),i.length=0,c.length=0}),1e3)}var a,s=function(){function e(e){this.open(e)}return e.prototype.createTable=function(e,t){if(this.db&&t){var n=this.keys=Object.keys(t);this.db.transaction((function(t){t.executeSql("CREATE TABLE IF NOT EXISTS "+e+" (id INTEGER PRIMARY KEY AUTOINCREMENT,"+n.join(",")+")",[],(function(e,t){}),(function(e,t){}))}))}},e.prototype.removeTable=function(e){this.db&&this.db.transaction((function(t){t.executeSql("DROP TABLE "+e,[],(function(e,t){}),(function(e,t){}))}))},e.prototype.add=function(e,t){if(this.db&&t){var n=this.keys,r=[],o=[];n.forEach((function(e){o.push("?"),r.push(t[e])}));var i="INSERT INTO "+e+" ("+n.join(",")+") VALUES ("+o.join(",")+")";this.db.transaction((function(e){e.executeSql(i,r,(function(e,t){}),(function(e,t){}))}))}},e.prototype.query=function(e,t){var n=this;return new Promise((function(r,o){n.db.transaction((function(n){n.executeSql("SELECT * FROM "+e+(t||""),[],(function(e,t){r(t.rows)}),(function(e,t){console.log(t)}))}))}))},e.prototype.delete=function(e,t){var n=this;return new Promise((function(r,o){n.db.transaction((function(n){n.executeSql("delete FROM "+e+(t||""),[],(function(e,t){r(t.rows.length)}),(function(e,t){console.log(t)}))}))}))},e.prototype.sum=function(e){var t=this;return new Promise((function(n,r){t.db.transaction((function(t){t.executeSql("SELECT count(id) FROM "+e,[],(function(e,t){var r=0;t.rows.length&&(r=t.rows[0]["count(id)"]),n(r)}),(function(e,t){console.log(t)}))}))}))},e.prototype.open=function(e){try{this.db=window.openDatabase(e.name,e.version,e.description,e.size)}catch(e){console.log(e)}},e}(),f=function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function c(e){try{a(r.next(e))}catch(e){i(e)}}function u(e){try{a(r.throw(e))}catch(e){i(e)}}function a(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(c,u)}a((r=r.apply(e,t||[])).next())}))},l=function(e,t){var n,r,o,i,c={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function u(i){return function(u){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;c;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return c.label++,{value:i[1],done:!1};case 5:c.label++,r=i[1],i=[0];continue;case 7:i=c.ops.pop(),c.trys.pop();continue;default:if(!((o=(o=c.trys).length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){c=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){c.label=i[1];break}if(6===i[0]&&c.label<o[1]){c.label=o[1],o=i;break}if(o&&c.label<o[2]){c.label=o[2],c.ops.push(i);break}o[2]&&c.ops.pop(),c.trys.pop();continue}i=t.call(e,c)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,u])}}};!function(e){e.Info="info",e.Debug="debug",e.Interface="interface",e.Error="error",e.DevLog="devlog"}(a||(a={}));var d,v=function(){function e(){var e=this;this.id=0,this._db=new s({name:"logs",version:10001,description:"the editor logs",size:52428800}),this.insertTable(),setTimeout((function(){e.updateTable()}))}return e.prototype.insert=function(e){this._db.add(e.level,e)},e.prototype.removeTables=function(){var e=this._db;e.removeTable(a.Info),e.removeTable(a.Interface),e.removeTable(a.Debug),e.removeTable(a.Error),e.removeTable(a.DevLog)},e.prototype.getInterface=function(e,t){var n=this;return void 0===e&&(e=1),new Promise((function(r,o){return f(n,void 0,void 0,(function(){var n,o,i;return l(this,(function(c){switch(c.label){case 0:return t||(t=" order by id desc LIMIT "+e),[4,this.query(a.Interface,t)];case 1:return n=c.sent(),o=/name: (\w+),arguments:([\s\S]*)/,i="",Array.from(n).forEach((function(e){var t=e.description,n=o.exec(t);if(n){var r=[];if(n[2])for(var c=/\d: (([\s\S](?!\d: ))+)?/g,u=n[2],a=void 0;a=c.exec(u);)if(isNaN(a[1]))if(["true","false"].includes(a[1]))r.push(a[1]);else if(/^[{\[][\s\S]+[}\]]$/.test(a[1]))try{var s=JSON.stringify(new Function("json","return json")(a[1]));r.push(s)}catch(e){r.push(a[1])}else r.push("'"+a[1]+"'");else r.push(a[1]);i="editor."+n[1]+"("+r.join(",")+")"+(i?";"+i:"")}})),console.log(n,i),r(i),[2]}}))}))}))},e.prototype.getLastDatas=function(e,t){var n=this;return void 0===e&&(e=1),new Promise((function(r,o){return f(n,void 0,void 0,(function(){var n;return l(this,(function(o){switch(o.label){case 0:return t||(t=" order by id desc LIMIT "+e),[4,this.query(a.Interface,t)];case 1:return n=o.sent(),r(n),[2]}}))}))}))},e.prototype.query=function(e,t){return void 0===e&&(e="interface"),this._db.query(e,t)},e.prototype.removeBeforeDatas=function(e,t){if(void 0===t&&(t=a.Interface),this._db)return this._db.delete(t," where id < "+e)},e.prototype.removeDatas=function(e){var t=this;return void 0===e&&(e=a.Interface),new Promise((function(n,r){t._db&&(t.deleteTable(e),t._db.createTable(e,{description:void 0,wasted:void 0,time:void 0,editorID:void 0,result:void 0}))}))},e.prototype.deleteTable=function(e){this._db.removeTable(e)},e.prototype.exportLogs=function(e,t){return void 0===e&&(e="interface"),f(this,void 0,void 0,(function(){var n,r;return l(this,(function(o){switch(o.label){case 0:return[4,this.query(e,t)];case 1:return(n=o.sent())&&n.length>0&&((r=document.createElement("a")).href=URL.createObjectURL(new Blob([JSON.stringify(n)],{type:"text/plain"})),r.download="actions.json",r.click()),[2]}}))}))},e.prototype.getSumDatas=function(e){return this._db.sum(e)},e.prototype.insertTable=function(){var e=this._db,t={description:void 0,wasted:void 0,time:void 0,editorID:void 0,result:void 0};e.createTable(a.Info,t),e.createTable(a.Interface,t),e.createTable(a.Debug,t),e.createTable(a.Error,t),e.createTable(a.DevLog,t)},e.prototype.updateTable=function(){return f(this,void 0,void 0,(function(){var e,t;return l(this,(function(n){switch(n.label){case 0:return[4,this.query(a.Interface," LIMIT 1")];case 1:return e=n.sent(),t=!1,e&&e.length>0&&e[0].level&&(this.removeTables(),this.insertTable(),t=!0),t?[3,3]:[4,this.getSumDatas(a.Interface)];case 2:n.sent()>1e4&&this._db.delete(a.Interface," where id < "+(e[0].id+8e3)),n.label=3;case 3:return[2]}}))}))},e}(),h=new Map,p=new Map,b=new Map;window.addEventListener("message",(function(e){var t=e.data;r()&&u(t,p);var n=t.__emrType;if(n)switch(n){case"external":!function(e){if(e){d||(d=new v,window.__editorDb=d),r()&&u(e);try{JSON.parse(e).forEach((function(e){d.insert(e)}))}catch(e){return void console.error(e)}}}(i=t.result);break;case"callback":var o=t.error,i=t.result,c=t.callId;c&&h.has(c)&&(h.get(c)(o,i),h.delete(c));break;case"event":var a=t.params,s=t.editorId,f=t.eventType;p.has(s)&&(l=p.get(s)[f])instanceof Function&&l.apply(null,a);break;case"custom":var l;a=t.params,s=t.editorId,f=t.eventType,b.has(s)&&(l=b.get(s)[f])instanceof Function&&l.apply(null,a)}}),!1);var y,m=1,w=function(e,t,n,r){void 0===r&&(r=[]);var o="callId-"+m++;return e.contentWindow.postMessage({callId:o,method:n,params:r,editorId:t,__emrType:"invork"},"*"),new Promise((function(e,t){h.set(o,(function(n,r){n?t(n):e(r)}))}))},g=function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function c(e){try{a(r.next(e))}catch(e){i(e)}}function u(e){try{a(r.throw(e))}catch(e){i(e)}}function a(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(c,u)}a((r=r.apply(e,t||[])).next())}))},E=function(e,t){var n,r,o,i,c={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function u(i){return function(u){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;c;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return c.label++,{value:i[1],done:!1};case 5:c.label++,r=i[1],i=[0];continue;case 7:i=c.ops.pop(),c.trys.pop();continue;default:if(!((o=(o=c.trys).length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){c=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){c.label=i[1];break}if(6===i[0]&&c.label<o[1]){c.label=o[1],o=i;break}if(o&&c.label<o[2]){c.label=o[2],c.ops.push(i);break}o[2]&&c.ops.pop(),c.trys.pop();continue}i=t.call(e,c)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,u])}}},T=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),c=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)c.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return c},_=function(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(T(arguments[t]));return e},I=function(){function e(){}return e.prototype.init=function(e){return g(this,void 0,void 0,(function(){var t,n,r,o,i,c=this;return E(this,(function(u){switch(u.label){case 0:return t=e.dom,n=e.src,(r=document.createElement("iframe")).width="100%",r.height="100%",r.frameBorder="0",r.src=n,r.scrolling="no",t.appendChild(r),[4,new Promise((function(e,t){r.onload=function(){return e(null)}}))];case 1:return u.sent(),this.iframe=r,[4,this.invork(null,"init")];case 2:return u.sent(),(o=e.option||{}).__src=n,[4,this.createEditor(o)];case 3:return u.sent(),function(e,t){var n=b.get(e);n?Object.keys(t).forEach((function(e){n[e]=t[e]})):b.set(e,t)}(i=this.instance.id,{getItem:function(e){var t=e.key,n=e.index,r=localStorage.getItem(t);c.invork(i,"___custom",[n,r])},setItem:function(e){var t=e.key,n=void 0===t?"":t,r=e.value,o=void 0===r?"":r,u=e.index;n&&(localStorage.setItem(n,o),c.invork(i,"___custom",[u]))},removeItem:function(e){var t=e.key,n=e.index;localStorage.removeItem(t),c.invork(i,"___custom",[n])}}),[2,this]}}))}))},e.prototype.getEditor=function(){return g(this,void 0,void 0,(function(){return E(this,(function(e){return[2,this.getAsyncEditor()]}))}))},e.prototype.getAsyncEditor=function(){return g(this,void 0,void 0,(function(){return E(this,(function(e){return[2,this.instance.editor]}))}))},e.prototype.setEvent=function(e){if(e)return function(e,t){var n=p.get(e);n?Object.keys(t).forEach((function(e){n[e]=t[e]})):(n=t,p.set(e,n))}(this.instance.id,e)},e.prototype.removeEvent=function(e){return function(e,t){if(!p.has(e))return!1;if(t){var n=p.get(e);t.forEach((function(e){n[e]=void 0}))}else p.delete(e);return!0}(this.instance.id,e)},e.prototype.createEditor=function(e){return g(this,void 0,void 0,(function(){var t,n,r=this;return E(this,(function(o){switch(o.label){case 0:return this.instance?[3,2]:[4,this.invork(null,"createEditor",[e])];case 1:t=o.sent(),(n=function e(t,n){var r=this;if("object"==typeof t)for(var o in t)"object"==typeof t[o]&&(t[o]=e(t[o],n));return new Proxy(t,{set:function(t,r,o,i){"object"==typeof o&&(o=e(o,n));var c=null==t[r]?"create":"modify";return Array.isArray(t)&&"length"===r||n(c,{target:t,key:r,value:o}),Reflect.set(t,r,o,i)},get:function(e,t,o){if(e.hasOwnProperty(t))return y=t,Reflect.get(e,t,o);y&&(t=y+"."+t,y=void 0);var i=n(e,t);return"function"==typeof i?function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return g(r,void 0,void 0,(function(){return E(this,(function(t){switch(t.label){case 0:return[4,i.apply(void 0,_(e))];case 1:return[2,t.sent()]}}))}))}:Reflect.get(e,y||t,o)}})}({},(function(e,n){return"then"===n?void 0:function(){for(var e=[],o=0;o<arguments.length;o++)e[o]=arguments[o];return r.invork(t,n,e)}}))).NIS={},this.instance={editor:n,id:t},o.label=2;case 2:return[2]}}))}))},e.prototype.invork=function(e,t,n){return void 0===n&&(n=[]),g(this,void 0,void 0,(function(){return E(this,(function(r){switch(r.label){case 0:return this.iframe?"close"===t?(this.removeEvent(),this.iframe&&this.iframe.parentNode&&(this.iframe.outerHTML="",this.iframe=null),[2,Promise.resolve()]):[4,w(this.iframe,e,t,n)]:(console.log("编辑器已经关闭"),[2]);case 1:return[2,r.sent()]}}))}))},e}()}])})),window.__EMR_EDITOR_SDK_VER__="1.1.240301";