<template>
  <div class="record-entry">
    <div v-if="schema?.head" class="record-entry-head">
      <component
        :is="loadComponent(tb.type)"
        v-for="(tb, index) in schema?.head || []"
        :key="`head-${index}`"
        :context="getContext(props)"
        :schema="tb"
      />
    </div>
    <table>
      <thead>
        <tr>
          <th
            v-for="(col, colIndex) in props.schema?.columnDef"
            :key="'k1' + colIndex"
            :style="col?.style || {}"
          >
            {{ col.displayName || col.field }}
          </th>
        </tr>
      </thead>
      <tbody ref="tbodyRef">
        <template v-for="(record, index) in records" :key="`k2-${index}`">
          <tr
            class="record-row"
          >
            <td
              v-for="(column, index2) in props.schema.columnDef"
              :key="`k3-${index2}`"
              :style="column?.style || {}"
            >
              <div v-if="index === 0 && column.firstRowIcon" class="icon-number-container">
                <img :src="`${BASE}${column.firstRowIcon}`" class="first-row-icon" />
                <span>{{ handleRecordShow(record, column) }}</span>
              </div>
              <template v-else>
                {{ handleRecordShow(record, column) }}
              </template>
            </td>
            <td v-if="props?.schema?.actions" class="action-anchor">
              <div class="action-toolbar">
                <component
                  :is="loadComponent(action.type)"
                  v-for="(action, index4) in props.schema?.actions || []"
                  :key="`action-${index4}`"
                  :context="getActionContext(action, record, index)"
                  :schema="action"
                />
              </div>
            </td>
          </tr>
          <tr v-if="rowSchema?.[index]">
            <td colspan="100" class="editor-box">
              <div class="row-edit">
                <component
                  :is="loadComponent(rowSchema[index].type)"
                  :context="{ ...getContext(props), record, index }"
                  :schema="rowSchema[index]"
                />
              </div>
            </td>
          </tr>
        </template>

        <tr :class="['record-selector-row', { 'fixed-bottom': records.length > 4 }]">
          <td colspan="100" style="overflow: visible">
            <RecordSelector
            :schema="props.schema.recordSelector"
            :context="getContext(props)"
            @focus="scrollToBottom"
          />
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script lang="ts" setup>
import { computed, defineProps, inject, onMounted, ref, onUnmounted, getCurrentInstance } from 'vue'
import RecordSelector from './RecordSelector.vue'
import { getContext, getNestedValue, setNestedValue } from '@/lib/getContext'
import { loadComponent } from '@/components/componentRegistry'
import { useEventBus } from '@/components/mixins/eventBus'

interface Action {
  type: string
  [key: string]: unknown
}

interface RowSchema {
  [index: number]: { type: string }
}

interface ColumnDef {
  field: string
  displayName?: string
  style?: Record<string, string>
}

interface Schema {
  columnDef: ColumnDef[]
  recordSelector?: Record<string, unknown>
  model: string
  label?: string
  style?: Record<string, string>
  actions?: Action[]
  event?: {
    listRecords?: string
    messageEndPoint?: string
  }
}

interface Props {
  schema: Schema & {
    head?: Array<{ type: string }>
  }
  context?: Record<string, unknown>
}
const BASE = import.meta.env.BASE_URL || ''
const props = defineProps<Props>()

const records = ref<Array<Record<string, unknown>>>([])
const tbodyRef = ref<HTMLElement | null>(null)

const comp = getCurrentInstance()
const eventBus = useEventBus()
const callBackWithContext = (data: any) => {
  let { cb } = data || {}
  if (!cb) return
  cb({ comp: comp?.exposed || {} })
}

function scrollToTop() {
  if (tbodyRef.value) {
    tbodyRef.value.scrollTop = 0
  }
}

function scrollToBottom() {
  if (tbodyRef.value) {
    tbodyRef.value.scrollTop = tbodyRef.value.scrollHeight
  }
}

function getActionContext(action: any, record: any, index: number) {
  const context = getContext(props)
  return {
    ...context,
    [action?.model]: record,
    index
  }
}

function handleRecordShow(record: any, column: any) {
  if (column?.format) {
    // 特殊的结构，需要格式化，按照orderList顺序展示
    const orderList = column.orderList || [];
    return orderList
      .map((order: string) => getNestedValue(record, order) || '') // 获取每个字段的值，不存在则返回空字符串
      .join('');
  } else {
    return getNestedValue(record, column.field)
  }
}

const listRecords = inject(
  props.schema?.event?.listRecords || 'list-records',
  async () => 1
) as Function

async function fetchRecords() {
  if (!props.context) return

  const fetchedRecords = await listRecords()
  if (!Array.isArray(fetchedRecords)) return

  records.value = [...fetchedRecords]
  setNestedValue(props.context, props.schema?.model, records.value)
}

onMounted(async () => {
  await fetchRecords()
  if (props.schema?.event?.messageEndPoint) {
      eventBus.on(props.schema?.event?.messageEndPoint, callBackWithContext)
  }
})

onUnmounted(() => {
  if (props.schema?.event?.messageEndPoint) {
      eventBus.on(props.schema?.event?.messageEndPoint, callBackWithContext)
  }
})

let rowSchema = ref([] as any)

function setRowSchema(schema: RowSchema, index: number) {
  rowSchema.value[index] = schema
}

defineExpose({ compName: 'recordEntry', setRowSchema, fetchRecords, scrollToTop, scrollToBottom })
</script>

<style lang="scss" scoped>
.record-entry {
  width: 100%;

  table {
    width: 100%;
    display: block;
    height: calc(40px + 5 * 40px);
    border-collapse: collapse;
    overflow: hidden;
    border: 1px solid #D5DCE8;
    margin-top: 10px;

    thead {
      display: block;
      background: rgba(54,95,217,0.08);
    }

    tbody {
      display: block;
      overflow-y: auto;
      height: calc(100% - 40px);
    }

    tr {
      height: 40px;
      display: table;
      width: 100%;
      table-layout: fixed;
    }

    .icon-number-container {
      display: flex;
      align-items: center;
      width: 100%;

      .first-row-icon {
        position: absolute;
        left: 16px;
        top: 50%;
        transform: translateY(-50%);
        width: 24px;
        height: 24px;
        margin-right: 16px;
      }
      span {
        flex: 1;
      }
    }

    th,
    td {
      font-size: 14px;
      width: auto;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      box-sizing: border-box;
      border: 1px solid #ddd;
      padding: 2px;
      text-align: left;
    }

    th {
      color: grey;
      font-weight: bold;
      padding: 0 8px;
    }

    .action-anchor {
      width: 1px;
      padding: 0 !important;
      position: relative;
      overflow: visible;
    }

    .action-toolbar {
      position: absolute;
      right: 0;
      top: 0;
      bottom: 0;
      display: none;
      align-items: center;
      background: white;
      box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1);
      padding: 0 8px;
      gap: 4px;
      z-index: 2;
    }

    .record-row:hover .action-toolbar {
      display: flex;
    }

    .action-toolbar button {
      background: #007bff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      white-space: nowrap;

      &:hover {
        background: #0069d9;
      }
    }

    .record-selector-row {
      background-color: #f9f9f9;

      &.fixed-bottom {
        position: sticky;
        bottom: 0;
      }

      td {
        padding: 0;

        :deep(.input-text) {
          margin: 0;

          label {
            span {
              display: none;
            }

            input {
              width: 100%;
              padding: 8px;
              box-sizing: border-box;
            }
          }
        }
      }
    }
  }

  .editor-box {
    background-color: #fbfbfb;
    box-shadow: inset 1px 1px 3px grey;
  }
}
</style>
