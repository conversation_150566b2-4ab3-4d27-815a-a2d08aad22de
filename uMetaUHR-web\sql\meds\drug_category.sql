CREATE TABLE drug_category
(
    id            INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    category_name VARCHAR(50) NOT NULL,
    parent_id     INT,
    level         INT    DEFAULT 1,
    description   TEXT,
    created_at    BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
    updated_at    BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
    FOREIGN KEY (parent_id) REFERENCES drug_category (id)
);

COMMENT ON TABLE drug_category IS 'Drug classification hierarchy';
COMMENT ON COLUMN drug_category.category_name IS '分类名称';
COMMENT ON COLUMN drug_category.parent_id IS '父分类ID';
COMMENT ON COLUMN drug_category.level IS '层级(1=顶级分类)';
COMMENT ON COLUMN drug_category.description IS '分类描述';
COMMENT ON COLUMN drug_category.created_at IS '记录创建时间';
COMMENT ON COLUMN drug_category.updated_at IS '记录最后更新时间';
