CREATE TABLE drug_category
(
    id            INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    category_name VARCHAR(50) NOT NULL,
    parent_id     INT,
    level         INT    DEFAULT 1,
    description   TEXT,
    created_at    BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
    updated_at    BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
    FOREIGN KEY (parent_id) REFERENCES drug_category (id)
);

COMMENT ON TABLE drug_category IS 'Drug classification hierarchy';
COMMENT ON COLUMN drug_category.category_name IS 'Category name';
COMMENT ON COLUMN drug_category.parent_id IS 'Parent category reference';
COMMENT ON COLUMN drug_category.level IS 'Hierarchy level (1=top level)';
COMMENT ON COLUMN drug_category.description IS 'Category description';
COMMENT ON COLUMN drug_category.created_at IS 'Timestamp when record was created';
COMMENT ON COLUMN drug_category.updated_at IS 'Timestamp when record was last updated';
