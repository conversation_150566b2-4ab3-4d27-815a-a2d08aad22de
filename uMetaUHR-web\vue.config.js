const MiniCssExtractPlugin = require('mini-css-extract-plugin')

module.exports = {
  // 添加代码分割配置
  configureWebpack: {
    optimization: {
      splitChunks: {
        chunks: 'all', // 对同步和异步模块都进行代码分割
        minSize: 30000,  // 模块的最小尺寸
        maxSize: 0,      // 最大尺寸不限制
        minChunks: 1,    // 模块最少被引用次数，才会分割
        automaticNameDelimiter: '-', // chunk 文件名称的分隔符
        cacheGroups: {
          vendors: {
            test: /[\\/]node_modules[\\/]/, // 将 node_modules 中的模块打包为 vendors.js
            name: 'vendors',
            chunks: 'all',
            priority: -10
          },
          default: {
            minChunks: 2,  // 被至少两个模块引用
            priority: -20,
            reuseExistingChunk: true  // 重用已有的 chunk
          }
        }
      }
    },
    output: {
      // 为生成的 JavaScript 文件名添加内容指纹
      // filename: '[name].[chunkhash].js',  // 根据每个 chunk 的内容生成 hash
      // chunkFilename: '[name].[chunkhash].js' // 为分割出来的 chunk 文件添加 hash
      filename: '[name].js',  // 根据每个 chunk 的内容生成 hash
      chunkFilename: '[name].js' // 为分割出来的 chunk 文件添加 hash
    },
    plugins: [
      // 提取 CSS 到单独文件，并为 CSS 文件添加内容指纹
      new MiniCssExtractPlugin({
        // filename: '[name].[contenthash].css',  // 为每个 CSS 文件生成基于内容的 hash
        // chunkFilename: '[name].[contenthash].css' // 为分割出来的 CSS 文件添加 hash
        filename: '[name].css',  // 为每个 CSS 文件生成基于内容的 hash
        chunkFilename: '[name].css' // 为分割出来的 CSS 文件添加 hash
      })
    ],
    resolve: {
      alias: {
        vue$: 'vue/dist/vue.esm-bundler.js'
      }
    }
  },
  chainWebpack: (config) => {
    // Ensure Vue runtime compiler is used
    config.resolve.alias.set('vue$', 'vue/dist/vue.esm-bundler.js')
  }
}