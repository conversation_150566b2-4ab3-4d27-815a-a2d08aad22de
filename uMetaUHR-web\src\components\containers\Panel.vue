<template>
  <div v-if="isVisible" :style="schema?.style?.main" class="panel">
    <component
      :is="loadComponent(child.type)"
      v-for="(child, index) in schema.children"
      :key="index"
      :context="getContext(props)"
      :schema="child"
    />
  </div>
</template>

<script lang="ts" setup>
import { computed, defineProps } from 'vue'
import useSubContext from '../mixins/subContextMixin'
import { loadComponent } from '../componentRegistry'
import { evaluateShowWhen, getContext } from '@/lib/getContext'

const props = defineProps<{
  schema: {
    type: string
    children: Array<any>
  }
  context: { [key: string]: any }
}>()

const { context: subContext, processSchema } = useSubContext()

subContext.value = { ...props.context }
processSchema(props.schema, subContext.value)

const isVisible = computed(() => evaluateShowWhen(props))
defineExpose({ compName: props?.schema?.type || '-' })
</script>

<style lang="scss" scoped>
.panel {
  background: #e6f2ff;
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;

  .title {
    background: aliceblue;
    border-radius: 5px 5px 0 0;
    font-weight: bold;
    padding: 0.1rem 0.4rem;
  }

  .body {
    padding: 5px;
  }
}

h3 {
}
</style>
