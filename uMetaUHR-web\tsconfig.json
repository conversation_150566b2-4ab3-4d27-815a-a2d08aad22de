{
  "files": [],
  "references": [
    {
      "path": "./tsconfig.node.json"
    },
    {
      "path": "./tsconfig.app.json"
    },
  ],
  "compilerOptions": {
    "module": "NodeNext",
    "target": "es6",
    //    "module": "commonjs",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "baseUrl": "./",
    "typeRoots": [
      "./node_modules/@types",
      "./src/types"
    ],
    "types": [
    ],
  },
  "include": [
    "global.d.ts",
    "src/**/*.d.ts",
    "env.d.ts"
  ]
}
