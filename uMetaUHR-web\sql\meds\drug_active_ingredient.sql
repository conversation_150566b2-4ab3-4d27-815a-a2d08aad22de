CREATE TABLE active_ingredient
(
    id                INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    name              VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    english_name      VARCHAR(100),
    cas_number        VARCHAR(50),
    molecular_formula VARCHAR(50),
    molecular_weight  DECIMAL(10, 4),
    is_active         BOOLEAN DEFAULT TRUE,
    created_at        BIGINT  DEFAULT EXTRACT(EPOCH FROM NOW()),
    updated_at        BIGINT  DEFAULT EXTRACT(EPOCH FROM NOW())
);

COMMENT ON TABLE active_ingredient IS 'Active pharmaceutical ingredients information';
COMMENT ON COLUMN active_ingredient.name IS 'Ingredient name in Chinese';
COMMENT ON COLUMN active_ingredient.english_name IS 'Ingredient name in English';
COMMENT ON COLUMN active_ingredient.cas_number IS 'Chemical Abstracts Service number';
COMMENT ON COLUMN active_ingredient.molecular_formula IS 'Molecular formula';
COMMENT ON COLUMN active_ingredient.molecular_weight IS 'Molecular weight';
COMMENT ON COLUMN active_ingredient.is_active IS 'Whether this is an active pharmaceutical ingredient';
COMMENT ON COLUMN active_ingredient.created_at IS 'Timestamp when record was created';
COMMENT ON COLUMN active_ingredient.updated_at IS 'Timestamp when record was last updated';
