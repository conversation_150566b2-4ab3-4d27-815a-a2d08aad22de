CREATE TABLE drug_active_ingredient
(
    id                INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    name              VARCHAR(100) NOT NULL,
    english_name      VARCHAR(100),
    cas_number        VARCHAR(50),
    molecular_formula VARCHAR(50),
    molecular_weight  DECIMAL(10, 4),
    is_active         BOOLEAN DEFAULT TRUE,
    created_at        BIGINT  DEFAULT EXTRACT(EPOCH FROM NOW()),
    updated_at        BIGINT  DEFAULT EXTRACT(EPOCH FROM NOW())
);

COMMENT ON TABLE drug_active_ingredient IS 'Active pharmaceutical ingredients information';
COMMENT ON COLUMN drug_active_ingredient.name IS '中文名称';
COMMENT ON COLUMN drug_active_ingredient.english_name IS '英文名称';
COMMENT ON COLUMN drug_active_ingredient.cas_number IS '化学文摘社登记号(CAS号)';
COMMENT ON COLUMN drug_active_ingredient.molecular_formula IS '分子式';
COMMENT ON COLUMN drug_active_ingredient.molecular_weight IS '分子量';
COMMENT ON COLUMN drug_active_ingredient.is_active IS '是否为有效药物成分';
COMMENT ON COLUMN drug_active_ingredient.created_at IS '记录创建时间';
COMMENT ON COLUMN drug_active_ingredient.updated_at IS '记录最后更新时间';
