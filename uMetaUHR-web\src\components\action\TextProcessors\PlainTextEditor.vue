<template>
  <div>
    <label v-if="props.schema?.label">
      <span class="label">{{ props.schema.label }}</span>
    </label>
    <textarea
      ref="inputRef"
      v-model="modelValue"
      class="structured-input"
      placeholder="Enter command on the first line, followed by your input on subsequent lines"
      rows="10"
      @blur="handleCache"
    ></textarea>
    <pre><code v-if="errorMessage" class="error-message">{{ errorMessage }}</code></pre>
  </div>
</template>

<script lang="ts" setup>
import { computed, defineProps, nextTick, onMounted, ref } from 'vue'
import { CacheManager } from '@/lib/CacheManager'
import { getContext, getNestedValue, setNestedValue } from '@/lib/getContext'

const props = defineProps<{
  schema: {
    label?: string
    model: string
    options: Array<{ value: string | number; label: string }>
  }
  context: { [key: string]: any }
}>()

const modelValue = computed({
  get: () => getNestedValue(getContext(props), props.schema.model),
  set: (value) => setNestedValue(getContext(props), props.schema.model, value)
})

// Props and emits
let CACHE_KEY = 'editor-aux-input'

const errorMessage = ref('') // Error message

const inputRef = ref<HTMLTextAreaElement | null>(null)

onMounted(async () => {
  await nextTick()
  let favData = (await cacheManager.get(CACHE_KEY)) || {}

  modelValue.value = favData.userInput
  inputRef.value?.focus()
})

const cacheManager = CacheManager.getInstance<any>()

async function handleCache() {
  let favData = (await cacheManager.get(CACHE_KEY)) || {}
  favData.userInput = modelValue.value || ''

  await cacheManager.set(CACHE_KEY, favData)
}

defineExpose({ compName: props?.schema?.type || '-' })
</script>

<style scoped>
.structured-input {
  width: 100%;
  padding: 10px;
  font-family: monospace;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.error-message {
  color: red;
  font-size: 0.9rem;
  margin: 5px 0;
}
</style>
