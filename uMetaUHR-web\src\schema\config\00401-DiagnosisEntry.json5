{
  type: 'diagnosisEntry',
  ui: {
    type: 'window',
    body: [
      {
        type: 'recordEntry',
        event: {
          listRecords: 'list-records',
          messageEndPoint: 'record-entry'
        },
        head: [
          {
            type: 'tag',
            label: '常用：',
            mode: 'single',
            style: {
              tagsInput: {
                gap: '10px'
              },
              label: {
                fontSize: '14px',
                color: '#939393',
                fontWeight: 400
              }
            },
            model: 'selectedValue',
            event: {
              // todo tag的绑定事件
              name: 'save-records'
            },
            options: [
              {
                value: 'acute_bronchitis',
                label: '急性支气管炎'
              },
              {
                value: 'pneumonia',
                label: '肺炎'
              },
              {
                value: 'influenza_a',
                label: '甲型流感'
              },
              {
                value: 'II_diabetes',
                label: 'II 型糖尿病'
              },
              {
                value: 'acute_gastroenteritis',
                label: '急性肠胃炎'
              },
              {
                value: 'fracture',
                label: '骨折'
              }
            ]
          }
        ],
        columnDef: [
          {
            displayName: '编码',
            field: 'code',
            style: {
              width: '250px',
              paddingLeft: '56px',
              position: 'relative'
            },
            comp: {
              type: 'textDisplay',
              selfIcon: '/main-diagnosis.svg',
              style: {
                main: {
                  justifyContent: 'flex-start'
                },
                selfIcon: {
                  width: '24px',
                  height: '24px',
                  marginRight: '16px',
                  marginLeft: '-40px'
                }
              },
              showIcon: 'context?.selectedLine === 0',
              model: 'record',
              template: '\
                <span>{{code}}</span>'
            }
          },
          {
            displayName: '诊断',
            field: 'diagnosis_name',
            style: {
              paddingLeft: '16px'
            },
            comp: {
              type: 'textDisplay',
              style: {
                main: {
                  justifyContent: 'flex-start'
                }
              },
              model: 'record',
              template: '\
                <span>{{diagnosis_modifier.prefix}}{{diagnosis_name}}{{diagnosis_modifier.suffix}}</span>'
            }
          }
        ],
        actions: [
          {
            type: 'button',
            event: {
              name: 'edit-record'
            },
            style: {
              main: {
                backgroundColor: 'white',
                width: '25px',
                height: '25px'
              }
            },
            model: 'diagnosisRecord',
            customIcon: '/edit.svg'
          },
          {
            type: 'button',
            event: {
              name: 'delete-record'
            },
            style: {
              main: {
                backgroundColor: 'white',
                width: '25px',
                height: '25px'
              }
            },
            model: 'diagnosisRecord',
            customIcon: '/delete.svg'
          }
        ],
        model: 'diagnosisRecordList',
        recordSelector: {
          type: 'recordSelector',
          model: 'diagnosisRecord', // 要与编辑按钮的model保持一致
          selfIcon: '/add.svg',
          placeholder: '搜索关键词',
          noDataText: '未找到相关数据',
          autoFocus: true,
          event: {
            queryCondition: 'query-condition',
            selectSuggestion: 'save-record',
            messageEndPoint: 'record-selector'
          },
          style: {
            selfIcon: {
              marginRight: '10px',
              marginLeft: '10px',
              width: '15px',
              height: '15px'
            }
          },
          columnDef: [
            {
              field: 'code',
              displayName: '编码'
            },
            {
              field: 'name',
              displayName: '诊断'
            }
          ]
        }
      }
    ]
  },
  schema: {
    'edit-record-modal': {
      title: '前后缀输入',
      modal: true,
      showMinimize: false,
      showClose: true,
      style: {
        main: {
          minWidth: '451px',
          width: 'auto',
          height: '200px'
        },
        header: {
          marginLeft: '10px',
          fontFamily: 'SourceHanSansCN-Bold',
          fontSize: '18px',
          color: '#202020',
          fontWeight: 700
        },
        content: {
          display: 'flex',
          gap: '8px',
          flexDirection: 'row',
          alignItems: 'center'
        }
      },
      body: [
        {
          type: 'inputText',
          inputType: 'text',
          placeholder: '请输入前缀',
          model: 'diagnosisRecord.diagnosis_modifier.prefix',
          autoFocus: true,
          style: {
            main: {
              width: '156px',
              height: '32px',
              marginLeft: '20px'
            }
          }
        },
        {
          type: 'textDisplay',
          style: {
            main: {
              fontSize: '14px',
              fontWeight: 400,
              color: '#202020',
              minWidth: '61px'
            }
          },
          template: '\
            <span>{{diagnosisRecord.diagnosis_name}}</span>'
        },
        {
          type: 'inputText',
          inputType: 'text',
          placeholder: '请输入后缀',
          model: 'diagnosisRecord.diagnosis_modifier.suffix',
          style: {
            main: {
              width: '156px',
              height: '32px',
              marginRight: '20px'
            }
          }
        }
      ],
      foot: [
        {
          type: 'button',
          label: '取消',
          style: {
            main: {
              borderRadius: '4px'
            }
          },
          event: {
            name: 'cancel-dialog'
          }
        },
        {
          type: 'button',
          label: '保存',
          style: {
            main: {
              backgroundColor: '#365FD9',
              borderRadius: '4px',
              color: '#FFFFFF'
            }
          },
          event: {
            name: 'confirm-dialog'
          }
        }
      ]
    },
    'delete-record-modal': {
      modal: true,
      showMinimize: false,
      showClose: false,
      showHeader: false,
      style: {
        main: {
          height: '110px'
        },
        content: {
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center'
        }
      },
      body: [
        {
          type: 'textDisplay',
          style: {
            main: {
              fontSize: '16px',
              fontWeight: '700',
              marginLeft: '16px'
            },
            selfIcon: {
              marginRight: '16px',
              width: '21px',
              height: '21px'
            }
          },
          selfIcon: '/alert.svg',
          template: '\
            <span style="">你确定要删除这条诊断吗</span>'
        }
      ],
      foot: [
        {
          type: 'button',
          label: '取消',
          style: {
            main: {
              borderRadius: '4px'
            }
          },
          event: {
            name: 'cancel-dialog'
          }
        },
        {
          type: 'button',
          label: '删除',
          style: {
            main: {
              backgroundColor: '#365FD9',
              borderRadius: '4px',
              color: '#FFFFFF'
            }
          },
          event: {
            name: 'confirm-dialog'
          }
        }
      ]
    }
  }
}
