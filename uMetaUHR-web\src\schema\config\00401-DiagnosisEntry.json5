{
  type: 'diagnosisEntry',
  ui: {
    type: 'window',
    body: [
      {
        type: 'recordEntry',
        event: {
          listRecords: 'list-records',
          messageEndPoint: 'record-entry'
        },
        head: [
          {
            type: 'tag',
            label: '常用：',
            mode: 'single',
            style: {
              tagsInput: {
                gap: '10px'
              },
              label: {
                fontSize: '14px',
                color: '#939393',
                fontWeight: 400
              }
            },
            model: 'selectedValue',
            event: {
              // todo tag的绑定事件
              name: 'save-records'
            },
            options: [
              {
                value: 'acute_bronchitis',
                label: '急性支气管炎'
              },
              {
                value: 'pneumonia',
                label: '肺炎'
              },
              {
                value: 'influenza_a',
                label: '甲型流感'
              },
              {
                value: 'II_diabetes',
                label: 'II 型糖尿病'
              },
              {
                value: 'acute_gastroenteritis',
                label: '急性肠胃炎'
              },
              {
                value: 'fracture',
                label: '骨折'
              }
            ]
          }
        ],
        columnDef: [
          {
            displayName: '编码',
            field: 'code',
            firstRowIcon: '/main-diagnosis.svg',
            style: {
              width: '250px',
              paddingLeft: '56px',
              position: 'relative'
            }
          },
          {
            displayName: '诊断',
            field: 'diagnosis_name',
            style: {
              paddingLeft: '16px'
            },
            format: true,
            orderList: ['diagnosis_modifier.prefix', 'diagnosis_name', 'diagnosis_modifier.suffix']
          }
        ],
        actions: [
          {
            type: 'button',
            event: {
              name: 'edit-record'
            },
            style: {
              main: {
                backgroundColor: 'white'
              }
            },
            model: 'diagnosisRecord',
            customIcon: '/edit.svg'
          }
        ],
        model: 'diagnosisRecordList',
        recordSelector: {
          type: 'recordSelector',
          model: 'diagnosisRecord', // 要与编辑按钮的model保持一致
          selfIcon: '/add.svg',
          placeholder: '搜索关键词',
          noDataText: '未找到相关数据',
          autoFocus: true,
          event: {
            queryCondition: 'query-condition',
            selectSuggestion: 'save-record',
            messageEndPoint: 'record-selector'
          },
          style: {
            selfIcon: {
              marginRight: '10px',
              marginLeft: '10px',
              width: '15px',
              height: '15px'
            }
          },
          columnDef: [
            {
              field: 'code',
              displayName: '编码'
            },
            {
              field: 'name',
              displayName: '诊断'
            }
          ]
        }
      }
    ]
  },
  schema: {
    'record-modal': {
      title: '前后缀输入',
      modal: true,
      style: {
        main: {
          minWidth: '451px',
          width: 'auto',
          height: '200px'
        },
        header: {
          marginLeft: '10px',
          fontFamily: 'SourceHanSansCN-Bold',
          fontSize: '18px',
          color: '#202020',
          fontWeight: 700
        },
        content: {
          display: 'flex',
          gap: '8px',
          flexDirection: 'row',
          alignItems: 'center'
        }
      },
      body: [
        {
          type: 'inputText',
          inputType: 'text',
          placeholder: '请输入前缀',
          model: 'diagnosisRecord.diagnosis_modifier.prefix',
          tempModel: 'diagnosisRecord.tempPrefix',
          autoFocus: true,
          style: {
            main: {
              width: '156px',
              height: '32px',
              marginLeft: '20px'
            }
          }
        },
        {
          type: 'textDisplay',
          style: {
            main: {
              fontSize: '14px',
              fontWeight: 400,
              color: '#202020',
              minWidth: '61px'
            }
          },
          template: '\
            <div style="display: flex;">\
              <span>{{diagnosisRecord.diagnosis_name}}</span>\
            </div>'
        },
        {
          type: 'inputText',
          inputType: 'text',
          placeholder: '请输入后缀',
          model: 'diagnosisRecord.diagnosis_modifier.suffix',
          tempModel: 'diagnosisRecord.tempSuffix',
          style: {
            main: {
              width: '156px',
              height: '32px',
              marginRight: '20px'
            }
          }
        }
      ],
      foot: [
        {
          type: 'button',
          label: '取消',
          style: {
            main: {
              borderRadius: '4px'
            }
          },
          event: {
            name: 'cancel-dialog'
          }
        },
        {
          type: 'button',
          label: '保存',
          style: {
            main: {
              backgroundColor: '#365FD9',
              borderRadius: '4px',
              color: '#FFFFFF'
            }
          },
          event: {
            name: 'confirm-dialog'
          }
        }
      ]
    }
  }
}
