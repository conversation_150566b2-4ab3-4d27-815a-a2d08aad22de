<template>
  <div v-if="isVisible" class="input-text" :style="props?.schema?.style?.main">
    <label>
      <span class="label" v-if="props?.schema?.label">
        {{ props?.schema?.label }}
        <span v-if="props?.schema?.valid?.required" class="required">*</span>
      </span>
      <input
        ref="inputRef"
        :class="{ 'input-error': error }"
        :type="props?.schema?.inputType ?? 'text'"
        :value="getNestedValue(context, props?.schema?.model)"
        :placeholder="props?.schema?.placeholder || ''"
        :style="props?.schema?.style?.input"
        @blur="handleInput($event)"
        autocomplete="off"
      />
    </label>
    <span v-if="error" :title="error" class="error"> * </span>
  </div>
</template>

<script lang="ts" setup>
import { computed, defineProps, nextTick, onMounted, ref, watchEffect } from 'vue'
import useValidation from '../mixins/validationMixin'
import { evaluateShowWhen, getNestedValue, setNestedValue } from '@/lib/getContext'

const props = defineProps<{
  label?: string
  schema: {
    style?: {
      main?: any
      input?: any
    }
    autoFocus?: boolean
    placeholder?: string
    type: string
    label?: string
    model: string
    inputType?: string
    dataType?: string
    showWhen?: string
    valid?: {
      required?: boolean
    }
  }
  context: { [key: string]: any }
}>()
const inputRef = ref<HTMLInputElement>()
const { error, validate } = useValidation()

onMounted(async () => {
  if (props.schema?.autoFocus) {
    // 通过 schema 控制是否聚焦
    await nextTick()
    inputRef.value?.focus()
  }
})

function handleInput(event: any) {
  let value = event?.target?.value
  if (value === undefined || value === null) return
  if (props?.schema?.dataType === 'n') value = +value
  setNestedValue(props.context, props.schema?.model, value)
}

const isVisible = computed(() => evaluateShowWhen(props))

watchEffect(() => {
  validate(getNestedValue(props.context, props.schema?.model), {
    required: props.schema?.valid?.required || false
  })
})
defineExpose({ compName: props?.schema?.type || '-' })
</script>

<style lang="scss" scoped>
.input-text {
  display: flex;
  flex-wrap: nowrap;
  margin: 2px;

  label {
    display: flex;
    flex-wrap: nowrap;
    flex: 1 1 auto;

    span {
      min-width: 5rem;
      white-space: nowrap;
      text-align: right;
      padding-right: 0.5rem;

      &.required {
        color: red;
      }
    }

    input {
      width: 100%;
      min-width: 5rem;
      border-radius: 0.2rem;
      border: 1px solid #ccc;
      padding-left: 5px;
      min-height: 1.5rem;
    }
  }
}
</style>
