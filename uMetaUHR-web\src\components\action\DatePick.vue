<template>
  <a-range-picker 
    :presets="rangePresets" 
    v-bind="props?.schema?.attrs"
    :value="getNestedValue(context, props?.schema?.model)" 
    @change="onRangeChange" />
</template>

<script lang="ts" setup>
import { defineProps, ref, inject } from 'vue'
import dayjs, { Dayjs } from 'dayjs';
import { getNestedValue, setNestedValue } from '@/lib/getContext'

type RangeValue = [Dayjs, Dayjs];

type RangePreset = {
  label: string,
  diff: number,
  unit: 'd' | 'm' | 'y'
}

const props = defineProps<{
  label?: string
  schema: {
    model: string,
    attrs?: Record<string, any>,
    event?: {
      change?: string
    },
    rangePresets?: RangePreset[]
  }
  context: { [key: string]: any }
}>()

const rangePresets = ref((props.schema.rangePresets || []).map(item=> ({
  label: item.label,
  value: [dayjs().add(item.diff, item.unit), dayjs()]
})));

const {
  schema: {
    event: {
      change: changeEventInject = "change-event",
    } = {}
  } = {}
} = props

const changeEvent = inject(changeEventInject, () => 1) as Function

const onRangeChange = (dates: RangeValue, dateStrings: string[]) => {
  setNestedValue(props.context, props.schema?.model, dates)
  changeEvent(dates, dateStrings)
};

</script>

<style lang="scss" scoped></style>
