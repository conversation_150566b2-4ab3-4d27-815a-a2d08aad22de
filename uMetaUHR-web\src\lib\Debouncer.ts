type AnyFunction = (...args: any[]) => Promise<any> | any

interface DebounceOptions {
  leading?: boolean
  trailing?: boolean
}

export class Debouncer {
  private timeouts: Map<AnyFunction, ReturnType<typeof setTimeout>> = new Map()
  private lastCalls: Map<AnyFunction, Promise<any>> = new Map()
  private readonly wait: number
  private options: DebounceOptions

  constructor(wait: number, options: DebounceOptions = { leading: false, trailing: true }) {
    if (wait <= 0) {
      throw new Error('The wait time must be greater than 0')
    }
    if (!options.leading && !options.trailing) {
      throw new Error('Either leading or trailing must be true')
    }
    this.wait = wait
    this.options = options
  }

  public debounce<T extends AnyFunction>(
    func: T
  ): (...args: Parameters<T>) => Promise<ReturnType<T> | undefined> {
    return async (...args: Parameters<T>): Promise<ReturnType<T> | undefined> => {
      // Clear any existing timeout
      if (this.timeouts.has(func)) {
        clearTimeout(this.timeouts.get(func)!)
      }

      const shouldCallNow = this.options.leading && !this.timeouts.has(func)

      return new Promise<ReturnType<T> | undefined>((resolve, reject) => {
        const timeout = setTimeout(async () => {
          this.timeouts.delete(func)

          if (this.options.trailing && !shouldCallNow) {
            try {
              const result = await func(...args)
              this.lastCalls.set(func, Promise.resolve(result))
              resolve(result)
            } catch (error) {
              reject(error)
            }
          } else {
            resolve(undefined)
          }
        }, this.wait)

        this.timeouts.set(func, timeout)

        if (shouldCallNow) {
          try {
            const result = Promise.resolve(func(...args))
            this.lastCalls.set(func, result)
            result.then(resolve).catch(reject)
          } catch (error) {
            reject(error)
          }
        }
      })
    }
  }

  public cancel(func?: AnyFunction): void {
    if (func && this.timeouts.has(func)) {
      clearTimeout(this.timeouts.get(func)!)
      this.timeouts.delete(func)
      this.lastCalls.delete(func)
    } else if (!func) {
      for (const timeout of this.timeouts.values()) {
        clearTimeout(timeout)
      }
      this.timeouts.clear()
      this.lastCalls.clear()
    }
  }

  public async flush<T extends AnyFunction>(
    func: T,
    ...args: Parameters<T>
  ): Promise<ReturnType<T> | undefined> {
    if (this.timeouts.has(func)) {
      clearTimeout(this.timeouts.get(func)!)
      this.timeouts.delete(func)
      try {
        const result = await func(...args)
        this.lastCalls.set(func, Promise.resolve(result))
        return result
      } finally {
        this.lastCalls.delete(func)
      }
    }
    return undefined
  }

  public reset(func: AnyFunction): void {
    if (this.timeouts.has(func)) {
      clearTimeout(this.timeouts.get(func)!)
    }
    this.timeouts.delete(func)
    this.lastCalls.delete(func)
  }
}
