<template>
  <component
    :is="loadComponent(props.schema?.ui?.type)"
    v-if="props.schema"
    :context="getContext(props)"
    :schema="props.schema.ui"
  />
</template>

<script lang="ts" setup>
import { defineProps, onMounted, provide } from 'vue'
import { loadComponent } from '../componentRegistry'
import { getContext } from '@/lib/getContext'
import { dbList, dbSave } from '@/lib/dataMan'
import { deepMerge } from '@/lib/copymerge'

const props = defineProps<{
  schema: {
    type: string
    children: Array<any>
  }
  context: { [key: string]: any }
}>()

const saveConfig = async (event: any) => {
  let {
    config: { main: config = {} }
  } = getContext(props)
  Object.assign(config, {
    category: 'fidelity',
    key: 'main'
  })
  return dbSave({
    table: 'config',
    data: [config],
    conflict_column: 'key',
    update_columns: ['category', 'key', 'config']
  })
}
provide('save-config', saveConfig)

const loadConfig = async (event: any) => {
  let items =
    (await dbList({
      sql: `SELECT *
          FROM "maxEMR".config
          WHERE category = ?::text`,
      param: ['fidelity']
    })) ?? ([] as any)

  let context = getContext(props)
  for (let item of items) {
    let { key, config } = item
    deepMerge(context, { config: { [key]: { config } } })
  }
}

onMounted(async () => {
  await loadConfig(props.schema)
})

defineExpose({ compName: props?.schema?.type || '-' })
</script>
