{
  // Helper functions can be placed here if needed.
}

start
  = textBefore:nonAiText aiBlock:aiBlock textAfter:.* {
      return {
        textBefore: textBefore ?? "",
        aiContent: aiBlock?.content ?? "",
        args: aiBlock?.args,
        textAfter: textAfter?.join?.('') ?? "",
        hasAICommands: true,
      };
  }
  / textOnly:.* {
      return {
        hasAICommands: false,
        textBefore: textOnly?.join?.("") || ""
      }
  }

nonAiText
  = chars:(!openAiTag @.)* {
      // Capture all text until we encounter an {{#ai}} block
      return chars.join('');
  }

aiBlock
  = open:openAiTag content:aiContent closeAiTag {
      return {
        type: "aiBlock",
        args: open.args,
        content
      };
  }

aiContent
  = content:(@aiBlock / @textChunk)* {
      // aiContent can contain nested aiBlocks or plain text
      // Return an array of strings and objects
      return content?.join("") || ""
  }

textChunk
  = chars:(!openAiTag !closeAiTag @.)+ {
      return chars.join('');
  }

openAiTag
  = "{{#ai" args:arguments? "}}" {
      return { args: args || [] };
  }

closeAiTag
  = "{{/ai}}"

arguments
  = wsp arg:argument rest:(wsp argument)* {
      return [arg].concat(rest.map(r => r[1]));
  }

argument
  = stringArgument / numberArgument / identifierArgument

stringArgument
  = '"' chars:([^"]*) '"' {
      return chars.join('');
  }

numberArgument
  = digits:[0-9]+ {
      return parseInt(digits.join(''), 10);
  }

identifierArgument
  = chars:[a-zA-Z0-9_]+ {
      return chars.join('');
  }

wsp
  = [ \t\n\r]+