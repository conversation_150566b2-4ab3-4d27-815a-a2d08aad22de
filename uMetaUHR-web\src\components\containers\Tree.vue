<template>
  <div :class="`${theme} field-w-toc`" :style="schema?.style?.main">
    <!-- Dynamic Toolbar -->
    <component
      :is="loadComponent(schema?.toolbar)"
      v-if="schema?.toolbar"
      :context="context"
      :schema="schema?.toolbar"
      class="field-w-toc-toolbar"
    />

    <!-- Tree Body -->
    <div class="field-w-toc-body">
      <el-tree
        :key="`tree-${renderTreeSeq}`"
        ref="tree"
        :data="state.data"
        :filter-node-method="filterNode"
        :highlight-current="true"
        :indent="20"
        node-key="id"
        @node-click="nodeClick"
      >
        <template #default="{ node }">
          <span class="custom-tree-node">
            <span>{{ node.label }}</span>
          </span>
        </template>
      </el-tree>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { inject, onMounted, reactive, ref, watch } from 'vue'
import { loadComponent } from '@/components/componentRegistry'
import { getContext } from '@/lib/getContext'
import { isEmpty } from '@/lib/util'

// Injections
const context = inject('context', {})
const eventSelectItem = inject('toc-select-line', () => {})
const eventListItem = inject('toc-list-data', () => {})
const eventRefreshItems = inject('toc-should-refresh', () => {})

// Props
const props = defineProps({
  schema: {
    type: Object,
    default: () => ({})
  },
  theme: {
    type: String,
    default: 'table-detail'
  }
})

// State
const state = reactive({
  schema: props.schema || {},
  data: [],
  selectedLine: -1
})

const tree = ref(null)
const renderTreeSeq = ref(0)
const tocKey = ref(1)

// Fetch data on mount
onMounted(async () => {
  state.data = await eventListItem({ queryParam: state.schema || {} })
})

// Node click handler
const nodeClick = async (nodeData: any, node: any) => {
  const { id } = nodeData || {}
  state.selectedLine = state.data.findIndex((item) => item.id === id)
  eventSelectItem({ id, data: nodeData, node })
  scrollToSelectedLine()
}

// Filter nodes
const filterNode = (value: any, nodeData: any) => {
  if (!value) return true
  return nodeData.label.indexOf(value) !== -1
}

// Scroll to the selected line
const scrollToSelectedLine = () => {
  nextTick(() => {
    try {
      const selectedNode = tree.value?.getNode(state.selectedLine)
      if (selectedNode) {
        selectedNode.scrollIntoView({ behavior: 'smooth', block: 'center' })
      }
    } catch (e) {
      console.error('Error scrolling to selected line:', e)
    }
  })
}

// Watch for data changes
watch(
  () => state.data,
  async (newValue) => {
    if (isEmpty(newValue)) return
    await selectLine(0)
    scrollToSelectedLine()
    tocKey.value++
  }
)

// Select a line
const selectLine = async (rowIndex: number) => {
  if (rowIndex < 0) rowIndex = 0
  if (rowIndex >= state.data.length) rowIndex = state.data.length - 1
  state.selectedLine = rowIndex
  const selectedData = state.data[rowIndex]
  eventSelectItem({ id: selectedData.id, data: selectedData })
}

// Handle refresh
watch(
  async () => await eventRefreshItems(getContext(props), props.schema?.model),
  async () => {
    state.data = await eventListItem({ queryParam: state.schema || {} })
    await selectLine(0)
  }
)
defineExpose({ compName: props?.schema?.type || '-' })
</script>

<style lang="scss" scoped>
.field-w-toc {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  overflow: auto;

  .field-w-toc-body {
    overflow: auto;

    .el-tree {
      width: 100%;
      overflow: auto;

      .el-tree-node {
        &.is-current > .el-tree-node__content {
          background-color: rgb(151, 195, 239);
        }

        .el-tree-node__content {
          height: 1em;
          outline: none;
          cursor: pointer;

          &:hover {
            background-color: rgba(151, 195, 239, 0.2);
          }
        }
      }
    }
  }
}

.field-w-toc-toolbar,
.toolbar {
  flex: 0 !important;
}

.table-detail.field-w-toc {
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
  overflow: hidden;
  padding: 1px;

  .field-w-toc-body {
    display: block;
    overflow: auto;
    position: relative;
  }
}
</style>
