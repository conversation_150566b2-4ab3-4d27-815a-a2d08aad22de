{
  type: 'sop',
  ui: {
    type: 'layout2',
    myFavoriteId: '01000-SOP.side2.006',
    query: {
      search: {
        'sql-pat': 'select id, patname, data from "maxEMR".pt1',
        'sql-encounter': 'select id as note_id, enc_id as pat_encounter, date, note_type, note_type_code  from "maxEMR".cn1 WHERE "patname" = ?::text',
        'sql-note': 'select * from "maxEMR".cn2 WHERE note_id = ?::int order by "WJJG_INDEX"'
      }
    },
    children: [
      null,
      {
        type: 'tocDetail',
        myFavoriteId: '01000-SOP.tocDetail.017',
        toc: {
          type: 'toc',
          columnDef: [
            {
              field: 'id',
              displayName: 'ID'
            },
            {
              field: 'data.marker1',
              displayName: '标1'
            },
            {
              field: 'data.sopMarker',
              displayName: '标2'
            }
          ],
          event: {
            listItems: 'list-patients',
            selectItem: 'select-patient'
          },
          selectedLine: 0,
          myFavoriteId: '01000-SOP.tocDetail.39'
        },
        style: {
          toc: {
            width: '10rem'
          },
          main: {
            background: 'white',
            border: 'none',
            margin: '0 0 0 3px'
          }
        },
        detail: {
          type: 'stack',
          style: {
            vars: {
              themeBorderColor: 'orange'
            }
          },
          menu: [
            {
              id: 'pat_summary_menu',
              displayName: '患者总结'
            },
            {
              id: 'note_list_menu',
              displayName: '既往病历'
            },
            {
              id: 'note_writer',
              displayName: '病历书写'
            }
          ],
          tabPosition: 'top',
          myFavoriteId: '01000-SOP.stack.62',
          event: {
            beforeSwitchTab: {
              name: 'before-switching-pat-detail'
            }
          },
          children: {
            note_list_menu: {
              type: 'tocDetail',
              myFavoriteId: '01000-SOP.tocDetail.076',
              toc: {
                type: 'toc',
                event: {
                  listItems: 'list-enc-notes',
                  selectItem: 'select-enc-note',
                  tocCommandSelectTopic: 'command-select-note'
                },
                columnDef: [
                  {
                    field: 'date',
                    displayName: '就诊日期'
                  },
                  {
                    field: 'note_type',
                    displayName: '病历类型'
                  }
                ],
                selectedLine: 0,
                myFavoriteId: '01000-SOP.toc.84'
              },
              style: {
                toc: {
                  width: '15rem'
                },
                main: {
                  border: 'none',
                  margin: '0 0 0 3px'
                }
              },
              detail: {
                type: 'layout',
                theme: 'side_by_side_2',
                myFavoriteId: '01000-SOP.side2.115',
                children: [
                  {
                    type: 'layout',
                    theme: 'side_by_side_2',
                    myFavoriteId: '01000-SOP.side2.130',
                    children: [
                      {
                        type: 'window',
                        title: '文书',
                        style: {
                          main: {
                            margin: '0 5px'
                          }
                        },
                        body: [
                          {
                            type: 'textDisplay',
                            template: '{{#each d}} <p><div style="font-weight: bold;">{{WJJG_INDEX}} {{WJJGMC}}:</div>{{WBNR}}</p> {{/each}}',
                            contentEditable: true
                          }
                        ],
                        head: [
                          {
                            type: 'toolbar',
                            children: [
                              {
                                type: 'button',
                                label: '提取检查结果',
                                showWhen: "context?.cn1?.note_type?.includes?.('出院')",
                                event: {
                                  name: 'llm-extract-note-info',
                                  tag: '提取检查结果',
                                  item: 'labResults'
                                }
                              },
                              {
                                type: 'button',
                                showWhen: "context?.cn1?.note_type?.includes?.('综合治疗')",
                                event: {
                                  name: 'llm-check-note',
                                  sop: 'SOP-综合治疗讨论',
                                  item: 'rationale'
                                },
                                label: '综合治疗讨论合理性'
                              },
                              {
                                type: 'button',
                                showWhen: "context?.cn1?.note_type?.includes?.('出院')",
                                label: '解析病历',
                                event: {
                                  name: 'parse-note-for-data',
                                  data: {
                                    prompt: '出-数据提取'
                                  }
                                },
                                hkeys: [
                                  {
                                    key: 'ctrl+h'
                                  }
                                ]
                              },
                              {
                                type: 'button',
                                event: {
                                  name: 'data-summarize-from-note'
                                },
                                label: '总结'
                              }
                            ]
                          }
                        ]
                      }
                    ]
                  },
                  {
                    type: 'stack',
                    tabPosition: 'right',
                    menu: [
                      {
                        id: 'memo',
                        displayName: '笔记'
                      },
                      {
                        id: 'noteInfo',
                        displayName: '工具与分析'
                      },
                      {
                        id: 'pat-lab-and-exam',
                        displayName: '检查与检验'
                      }
                    ],
                    children: {
                      noteInfo: {
                        type: 'window',
                        title: '文书信息',
                        body: [
                          {
                            type: 'section',
                            title: '诊断',
                            collapsible: true,
                            myFavoriteId: '01000-SOP.section-184',
                            children: [
                              {
                                type: 'textDisplay',
                                template: '{{#each d}} {{#if (contains WJJGMC "诊断")}} <p><span style="font-size: small; font-weight: bold; color: darkblue;">{{WJJGMC}}: </span> {{WBNR}}</p> {{/if}} {{/each}}'
                              }
                            ]
                          },
                          {
                            type: 'section',
                            title: '治疗前评估-检查',
                            collapsible: true,
                            showWhen: "context?.cn1?.note_type?.includes?.('出院')",
                            myFavoriteId: '01000-SOP.section-197',
                            children: [
                              {
                                type: 'toolbar',
                                style: {
                                  'justify-content': 'right'
                                },
                                children: [
                                  {
                                    type: 'button',
                                    event: {
                                      name: 'llm-check-note',
                                      tag: 'SOP-治疗前评估-检查',
                                      sop: 'SOP-治疗前评估-检查',
                                      item: 'assessmentExam'
                                    },
                                    label: '建议'
                                  }
                                ]
                              },
                              {
                                type: 'textDisplay',
                                template: '<pre style="white-space: pre-wrap">{{cn1.data.assessmentExam}}</pre>'
                              }
                            ]
                          },
                          {
                            type: 'section',
                            title: '治疗前评估-检验',
                            showWhen: "context?.cn1?.note_type?.includes?.('出院')",
                            collapsible: true,
                            myFavoriteId: '01000-SOP.section-228',
                            children: [
                              {
                                type: 'toolbar',
                                style: {
                                  'justify-content': 'right'
                                },
                                children: [
                                  {
                                    type: 'button',
                                    event: {
                                      name: 'llm-check-note',
                                      tag: 'SOP-治疗前评估-lab',
                                      sop: 'SOP-治疗前评估-lab',
                                      item: 'assessmentLab'
                                    },
                                    label: '建议'
                                  }
                                ]
                              },
                              {
                                type: 'textDisplay',
                                template: '<pre style="white-space: pre-wrap">{{cn1.data.assessmentLab}}</pre>'
                              }
                            ]
                          },
                          {
                            type: 'section',
                            title: '治疗前评估-病理',
                            showWhen: "context?.cn1?.note_type?.includes?.('出院')",
                            collapsible: true,
                            myFavoriteId: '01000-SOP.section-259',
                            children: [
                              {
                                type: 'toolbar',
                                style: {
                                  'justify-content': 'right'
                                },
                                children: [
                                  {
                                    type: 'button',
                                    event: {
                                      name: 'llm-check-note',
                                      tag: 'SOP-治疗前评估-病理学',
                                      sop: 'SOP-治疗前评估-病理学',
                                      item: 'assessmentPathology'
                                    },
                                    label: '建议'
                                  }
                                ]
                              },
                              {
                                type: 'textDisplay',
                                template: '<pre style="white-space: pre-wrap">{{cn1.data.assessmentPathology}}</pre>'
                              }
                            ]
                          },
                          {
                            type: 'section',
                            title: '合理性判断依据',
                            collapsible: true,
                            myFavoriteId: '01000-SOP.section-289',
                            children: [
                              {
                                type: 'textDisplay',
                                template: '{{#each (obj2list cn1.data.extNoteData)}} <div><span style="font-size: small; font-weight: bold; color: darkblue;">{{key}}: </span> {{showObj value}}</div> {{/each}}'
                              }
                            ]
                          },
                          {
                            type: 'section',
                            title: 'TNM分期',
                            collapsible: true,
                            myFavoriteId: '01000-SOP.section-301',
                            children: [
                              {
                                type: 'toolbar',
                                style: {
                                  'justify-content': 'right'
                                },
                                children: [
                                  {
                                    type: 'button',
                                    event: {
                                      name: 'llm-check-note',
                                      tag: 'TNM分期',
                                      sop: 'SOP-TNM分期建议',
                                      item: 'TNMStaging'
                                    },
                                    label: '建议'
                                  }
                                ]
                              },
                              {
                                type: 'textDisplay',
                                template: '<pre style="white-space: pre-wrap">{{cn1.data.TNMStaging}}</pre>'
                              }
                            ]
                          },
                          {
                            type: 'section',
                            title: '手术合理性',
                            showWhen: "context?.cn1?.note_type?.includes?.('手术')",
                            collapsible: true,
                            myFavoriteId: '01000-SOP.section-332',
                            children: [
                              {
                                type: 'toolbar',
                                style: {
                                  'justify-content': 'right'
                                },
                                children: [
                                  {
                                    type: 'button',
                                    event: {
                                      name: 'llm-check-note',
                                      tag: '手术合理性',
                                      sop: 'SOP-手术合理性（简单）',
                                      item: 'surgicalRationale'
                                    },
                                    label: '建议'
                                  }
                                ]
                              },
                              {
                                type: 'textDisplay',
                                template: '<pre style="white-space: pre-wrap">{{cn1.data.surgicalRationale}}</pre>'
                              }
                            ]
                          },
                          {
                            type: 'section',
                            title: '内科治疗信息',
                            showWhen: "context?.cn1?.note_type?.includes?.('治疗')",
                            collapsible: true,
                            myFavoriteId: '01000-SOP.section-363',
                            children: [
                              {
                                type: 'toolbar',
                                style: {
                                  'justify-content': 'right'
                                },
                                children: [
                                  {
                                    type: 'button',
                                    event: {
                                      name: 'llm-check-note',
                                      tag: '内科治疗信息',
                                      sop: 'SOP-内科治疗信息',
                                      item: 'intnTreatmentInfo'
                                    },
                                    label: '建议'
                                  }
                                ]
                              },
                              {
                                type: 'textDisplay',
                                template: '<pre style="white-space: pre-wrap">{{cn1.data.intnTreatmentInfo}}</pre>'
                              }
                            ]
                          },
                          {
                            type: 'section',
                            title: '合理性',
                            collapsible: true,
                            myFavoriteId: '01000-SOP.section-419',
                            children: [
                              {
                                type: 'textDisplay',
                                template: '<pre style="white-space: pre-wrap">{{cn1.data.rationale}}</pre>'
                              }
                            ]
                          },
                          {
                            type: 'section',
                            title: '信息提取',
                            showWhen: "context?.cn1?.note_type?.includes?.('出院')",
                            collapsible: true,
                            myFavoriteId: '01000-SOP.section-394',
                            children: [
                              {
                                type: 'toolbar',
                                style: {
                                  'justify-content': 'right'
                                },
                                children: [
                                  {
                                    type: 'button',
                                    label: '提取检查结果',
                                    event: {
                                      name: 'llm-extract-note-info',
                                      tag: '提取检查结果',
                                      item: 'labResults'
                                    }
                                  }
                                ]
                              },
                              {
                                type: 'textDisplay',
                                template: '<pre style="white-space: pre-wrap">{{cn1.data.labResults}}</pre>'
                              }
                            ]
                          },
                          {
                            type: 'section',
                            myFavoriteId: '01001-SOP-PATSUM.section-97',
                            collapsible: true,
                            title: '主要结果',
                            children: [
                              {
                                type: 'labChart',
                                model: 'lab_exam_list',
                                showItems: [
                                  'ALT',
                                  'AST',
                                  'TP',
                                  'WBC',
                                  'K+',
                                  'Na+',
                                  'Cl-'
                                ]
                              }
                            ]
                          },
                          {
                            type: 'section',
                            myFavoriteId: '01001-SOP-PATSUM.section-97',
                            collapsible: true,
                            title: '检查与检验',
                            children: [
                              {
                                type: 'textDisplay',
                                template: "{{#if lab_exam_list.length}}<table>  <tbody>  {{#each lab_exam_list}}  <tr style=\"font-weight: bold; background: #dee;\">    <td style=\"font-weight:bold; font-size: 10pt; color: #08e\">{{date_time}}</td>    <td style=\"font-weight:bold; font-size: 10pt; color: #08e\">{{name}}<div style=\"float:right\">{{comp 'button' id 'data-summarize-from-lab' '总结'}}{{comp 'button' id 'lab-analysis' '分析与解析'}}</div></td>  </tr>  <tr>    <td colSpan=5>{{data.content}}{{showLab this 'list' ../displayMode}}</td>  </tr>  {{/each}}  </tbody> </table> {{else}}<div class=\"slow\">无数据</div>{{/if}}"
                              }
                            ]
                          }
                        ],
                        head: [
                          {
                            type: 'toolbar',
                            style: {
                              'justify-content': 'right'
                            },
                            children: [
                              {
                                type: 'button',
                                event: {
                                  name: 'llm-save-note'
                                },
                                label: '保存'
                              }
                            ]
                          }
                        ]
                      },
                      memo: {
                        type: 'window',
                        title: '数据',
                        head: [
                          {
                            type: 'toolbar',
                            children: [
                              {
                                type: 'button',
                                event: {
                                  name: 'save-to-note'
                                },
                                label: '保存内容'
                              }
                            ]
                          }
                        ],
                        style: {
                          header: {
                            background: 'lightblue'
                          },
                          body: {
                            flex: '1'
                          }
                        },
                        body: [
                          {
                            type: 'richTextEditor',
                            model: 'cn1.data.sopAnalysis'
                          }
                        ]
                      },
                      'pat-lab-and-exam': {
                        type: 'window',
                        title: '检查与检验',
                        event: {
                          loadData: 'load-labs-and-exam'
                        },
                        head: [
                          {
                            type: 'toolbar',
                            style: {
                              'justify-content': 'right'
                            },
                            children: [
                              {
                                type: 'button',
                                event: {
                                  name: 'load-labs-and-exam'
                                },
                                label: '刷新'
                              },
                              {
                                type: 'tag',
                                label: '显示',
                                model: 'displayMode',
                                myFavoriteId: '01000-SOP.tag.527',
                                event: {
                                  name: 'change-display-mode-of-labs-and-exam'
                                },
                                options: [
                                  {
                                    value: 'full',
                                    label: '全部'
                                  },
                                  {
                                    value: 'abnormal',
                                    label: '仅异常'
                                  }
                                ],
                                mode: 'single'
                              }
                            ]
                          }
                        ],
                        body: [
                          {
                            type: 'textDisplay',
                            template: "{{#if lab_exam_list.length}}<table>  <tbody>  {{#each lab_exam_list}}  <tr style=\"font-weight: bold; background: #dee;\">    <td style=\"font-weight:bold; font-size: 10pt; color: #08e\">{{date_time}}</td>    <td style=\"font-weight:bold; font-size: 10pt; color: #08e\">{{name}}<div style=\"float:right\">{{comp 'button' id 'data-summarize-from-lab' '总结'}}{{comp 'button' id 'lab-analysis' '分析与解析'}}</div></td>  </tr>  <tr>    <td colSpan=5>{{data.content}}{{showLab this 'list' ../displayMode}}</td>  </tr>  {{/each}}  </tbody> </table> {{else}}<div class=\"slow\">无数据</div>{{/if}}"
                          }
                        ]
                      }
                    },
                    myFavoriteId: '01000-SOP.stack.477'
                  }
                ]
              },
              subContext: {
                path: 'detail',
                default: {}
              }
            },
            pat_summary_menu: {
              type: 'external',
              externalSchema: '01001-SOP-PATSUM'
            },
            note_writer: {
              type: 'external',
              externalSchema: '01003-Note-Writer'
            }
          }
        }
      },
      {
        menu: [
          {
            id: 'pat',
            displayName: '笔记'
          },
          {
            id: 'sopDataAnalysis',
            displayName: 'SOP分析'
          },
          {
            id: '检查与检验',
            displayName: '检查与检验'
          },
          {
            id: 'dataCollect',
            displayName: '数据解析'
          },
          {
            id: 'data',
            displayName: '数据'
          }
        ],
        type: 'stack',
        style: {
          main: {
            background: 'lightgrey',
            'border-radius': '5px'
          }
        },
        myFavoriteId: '01000-SOP.stack.520',
        children: {
          dataCollect: {
            type: 'section',
            title: '质控',
            children: [
              {
                type: 'toolbar',
                children: [
                  {
                    type: 'button',
                    event: {
                      name: 'act-collect-patient-demographic-info'
                    },
                    label: '解析数据'
                  }
                ]
              },
              {
                type: 'tocDetail',
                myFavoriteId: '01000-SOP.tocDetail.607',
                toc: {
                  type: 'toc',
                  event: {
                    listItems: 'list-prompts',
                    selectItem: 'select-prompt'
                  },
                  toolbar: [
                    {
                      type: 'button',
                      event: {
                        name: 'new-prompt'
                      },
                      label: '+'
                    },
                    {
                      type: 'button',
                      event: {
                        name: 'save-prompt'
                      },
                      label: 's'
                    }
                  ],
                  columnDef: [
                    {
                      field: 'key',
                      displayName: '提示词'
                    }
                  ],
                  selectedLine: 0
                },
                style: {
                  toc: {
                    width: '8rem'
                  },
                  main: {
                    border: 'none'
                  }
                },
                detail: {
                  type: 'layout',
                  children: [
                    {
                      body: [
                        {
                          type: 'section',
                          children: [
                            {
                              type: 'inputText',
                              model: 'key',
                              label: '提示词'
                            },
                            {
                              type: 'richTextEditor',
                              event: {
                                editorOnChange: 'change-prompt'
                              },
                              model: 'prompt'
                            }
                          ]
                        }
                      ],
                      head: [],
                      type: 'window'
                    }
                  ]
                },
                subContext: {
                  path: 'detail.prompt',
                  default: {}
                }
              },
              {
                type: 'dataViewer',
                model: 'aiResult'
              },
              {
                type: 'toolbar',
                children: [
                  {
                    type: 'button',
                    event: {
                      name: 'merge-to-patient'
                    },
                    label: '合并到患者'
                  },
                  {
                    type: 'button',
                    event: {
                      name: 'save-to-patient'
                    },
                    label: '保存到患者'
                  }
                ]
              },
              {
                type: 'dataViewer',
                model: 'patData'
              }
            ]
          },
          data: {
            type: 'section',
            title: '数据',
            children: [
              {
                label: '上下文数据',
                type: 'dataViewer',
                model: ''
              },
              {
                label: '患者数据',
                type: 'dataViewer',
                model: 'patData.data'
              }
            ]
          },
          pat: {
            type: 'section',
            title: '数据',
            children: [
              {
                type: 'toolbar',
                children: [
                  {
                    type: 'button',
                    event: {
                      name: 'save-pat-data'
                    },
                    label: '保存患者数据'
                  }
                ]
              },
              {
                type: 'choice',
                label: '标记',
                model: 'patData.data.sopMarker',
                options: [
                  {
                    value: null,
                    label: '-'
                  },
                  {
                    label: '挑选患者-m2',
                    value: 'm2'
                  },
                  {
                    label: '详细分析-m3',
                    value: 'm3'
                  },
                  {
                    label: '汇报-m4',
                    value: 'm4'
                  }
                ]
              },
              {
                label: '备注',
                type: 'richTextEditor',
                model: 'patData.data.comment'
              }
            ]
          },
          sopDataAnalysis: {
            type: 'window',
            title: 'SOP数据分析',
            head: [
              {
                type: 'toolbar',
                children: [
                  {
                    type: 'button',
                    event: {
                      name: 'save-to-patient'
                    },
                    label: '保存'
                  },
                  {
                    type: 'button',
                    event: {
                      name: 'data-summarize-from-clipboard'
                    },
                    label: '粘贴'
                  }
                ]
              }
            ],
            body: [
              {
                type: 'richTextEditor',
                model: 'patData.data.sopAnalysis',
                eventEndPoint: 'tp-summary-in-note'
              }
            ]
          },
          检查与检验: {
            type: 'window',
            title: '检查与检验',
            event: {
              loadData: 'load-labs-and-exam'
            },
            head: [
              {
                type: 'toolbar',
                style: {
                  'justify-content': 'right'
                },
                children: [
                  {
                    type: 'button',
                    event: {
                      name: 'load-labs-and-exam'
                    },
                    label: '刷新'
                  }
                ]
              }
            ],
            body: [
              {
                type: 'textDisplay',
                template: "{{#if lab_exam_list.length}}<table>  <tbody>  {{#each lab_exam_list}}  <tr style=\"font-weight: bold; background: #dee;\">    <td style=\"font-weight:bold; font-size: 10pt; color: #08e\">{{date_time}}</td>    <td style=\"font-weight:bold; font-size: 10pt; color: #08e\">{{name}}<div style=\"float:right\">{{comp 'button' id 'data-summarize-from-lab' '总结'}}{{comp 'button' id 'lab-analysis' '分析与解析'}}</div></td>  </tr>  <tr>    <td colSpan=5>{{data.content}}{{showLab this 'list' ../displayMode}}</td>  </tr>  {{/each}}  </tbody> </table> {{else}}<div class=\"slow\">无数据</div>{{/if}}"
              }
            ],
            subContext: {
              path: 'detail',
              default: {}
            }
          }
        },
        tabPosition: 'right'
      }
    ]
  },
  dialog: {
    'lab-analysis': {
      type: 'dialog',
      title: '检查检验分析',
      style: {
        body: {
          content: {
            display: 'flex'
          }
        }
      },
      body: [
        {
          type: 'stack',
          menu: [
            {
              id: 'data-parse',
              displayName: '数据解析'
            },
            {
              id: 'tool-maintenance',
              displayName: '解析工具维护'
            }
          ],
          myFavoriteId: '01000-SOP-814',
          tabPosition: 'top',
          children: {
            'data-parse': {
              type: 'window',
              head: [
                {
                  type: 'toolbar',
                  children: [
                    {
                      type: 'button',
                      event: {
                        name: 'extract-lab-info'
                      },
                      label: '解析'
                    },
                    {
                      type: 'button',
                      event: {
                        name: 'save-lab-info'
                      },
                      style: {
                        main: {
                          float: 'right'
                        }
                      },
                      label: '保存'
                    }
                  ]
                }
              ],
              body: [
                {
                  type: 'section',
                  title: '内容',
                  collapsible: true,
                  myFavoriteId: '01000-SOP.section-807',
                  children: [
                    {
                      type: 'tag',
                      label: 'Tags',
                      model: 'lab.data.tags',
                      options: [
                        {
                          value: '1',
                          label: 'Tag 1'
                        },
                        {
                          value: '2',
                          label: 'Tag 2'
                        },
                        {
                          value: '3',
                          label: 'Tag 3'
                        }
                      ],
                      mode: 'single'
                    },
                    {
                      type: 'textDisplay',
                      template: '<p style="font-size: 10pt">{{showObj lab.data.content}}</p>'
                    }
                  ]
                },
                {
                  type: 'section',
                  title: '表格',
                  collapsible: true,
                  myFavoriteId: '01000-SOP.section-817',
                  children: [
                    {
                      type: 'textDisplay',
                      template: '<table class="data"><thead><tr><th>名称</th><th>值</th><th>单位</th><th>标记</th></tr></thead><tbody>{{#each lab.data.result }} <tr><td>{{name.name}}({{name.symbol}})</td><td>{{value}}</td><td>{{unit}}</td><td>{{flag}}</td></tr>{{/each}}</tbody></table>'
                    }
                  ]
                },
                {
                  type: 'section',
                  title: '数据',
                  collapsible: true,
                  myFavoriteId: '01000-SOP.section-827',
                  children: [
                    {
                      type: 'dataViewer',
                      model: ''
                    }
                  ]
                }
              ]
            },
            'tool-maintenance': {
              type: 'window',
              head: [
                {
                  type: 'toolbar',
                  children: [
                    {
                      type: 'button',
                      event: {
                        name: 'parse-lab-result-to-meta-data'
                      },
                      label: '解析为数据元'
                    },
                    {
                      type: 'button',
                      event: {
                        name: 'save-lab-meta-data'
                      },
                      label: '保存数据元'
                    }
                  ]
                }
              ],
              body: [
                {
                  type: 'section',
                  title: '待解析文本',
                  children: [
                    {
                      type: 'textDisplay',
                      style: {
                        main: {
                          border: '1px solid lightgrey',
                          'border-radius': '3px',
                          margin: '5px 0'
                        }
                      },
                      template: '{{showObj lab.data.content}}'
                    },
                    {
                      type: 'richTextEditor',
                      model: 'tool.textToParse'
                    }
                  ]
                },
                {
                  type: 'section',
                  title: '解析结果',
                  children: [
                    {
                      type: 'richTextEditor',
                      model: 'tool.parsedResult'
                    }
                  ]
                }
              ]
            }
          }
        }
      ],
      foot: [
        {
          type: 'toolbar',
          style: {
            'justify-content': 'right'
          },
          children: [
            {
              type: 'button',
              event: {
                name: 'cancel-dialog'
              },
              label: '取消'
            },
            {
              type: 'button',
              event: {
                name: 'confirm-dialog'
              },
              label: '确定'
            }
          ]
        }
      ]
    },
    'llm-extract-note-info': {
      type: 'dialog',
      title: 'test',
      style: {
        main: {
          flex: '1 1 auto'
        },
        body: {
          flex: '1 1 auto'
        }
      },
      head: [
        {
          type: 'toolbar',
          children: [
            {
              type: 'button',
              event: {
                name: 'suggest-reply'
              },
              label: '建议'
            }
          ]
        }
      ],
      body: [
        {
          type: 'dataViewer',
          model: ''
        }
      ],
      foot: [
        {
          type: 'toolbar',
          style: {
            'justify-content': 'right'
          },
          children: [
            {
              type: 'button',
              event: {
                name: 'cancel-dialog'
              },
              label: '取消'
            },
            {
              type: 'button',
              event: {
                name: 'confirm-dialog'
              },
              label: '确定'
            }
          ]
        }
      ]
    }
  },
  subContext: {
    path: '/temp.sop',
    default: {}
  }
}
