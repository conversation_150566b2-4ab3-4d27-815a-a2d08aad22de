<template>
  <div class="week-view">
    <div class="controls">
      <button @click="nonWorkHoursFolded = !nonWorkHoursFolded">
        {{ nonWorkHoursFolded ? '显示所有时间' : '折叠非工作时间' }}
      </button>
      <button @click="showWorkdaysOnly = !showWorkdaysOnly">
        {{ showWorkdaysOnly ? '显示全部日期' : '只显示工作日' }}
      </button>
    </div>

    <div :style="{ gridTemplateColumns: `repeat(${weekDaysLength}, 1fr)` }" class="weekdays-header">
      <div v-for="day in weekDays" :key="day.toISODate()" class="weekday">
        {{ day.toFormat('EEE dd') }}
      </div>
    </div>

    <div class="weekdays-body">
      <div class="week-grid">
        <div :style="{ height: dayHeight }" class="time-grid">
          <div v-for="hour in hours" :key="hour" class="time-slot">
            <div class="time-label">{{ hour }}:00</div>
            <div class="time-line"></div>
          </div>
        </div>

        <div :style="{ gridTemplateColumns: `repeat(${weekDaysLength}, 1fr)` }" class="days">
          <div
            v-for="day in weekDays"
            :key="day.toISODate()"
            :style="{ height: dayHeight }"
            class="day"
            @dblclick="handleDoubleClick(day, $event)"
            @drop="handleDrop($event, day)"
            @dragover.prevent="handleDragOver(day, $event)"
          >
            <div
              v-if="showCurrentTimeLine(day)"
              :class="{
                'current-time-line': currentTimeLineStyle.offhour === 0,
                'current-time-line-before': currentTimeLineStyle.offhour === 1,
                'current-time-line-after': currentTimeLineStyle.offhour === 2
              }"
              :style="currentTimeLineStyle"
            >
              <span>{{ ServerTime.now2().toFormat('M/d h:m') }}</span>
            </div>

            <div v-if="nonWorkHoursFolded" class="hidden-indicators">
              <div v-if="hasHiddenEventsBeforeWorkStart(day)" class="triangle top">▲</div>
              <div v-if="hasHiddenEventsAfterWorkEnd(day)" class="triangle bottom">▼</div>
            </div>

            <div
              v-if="previewEvent?.day?.weekday === day.weekday"
              :style="previewEvent?.style"
              class="event preview-event"
            >
              <div class="event-title">{{ previewEvent?.title }}</div>
              <div class="event-time">{{ previewEvent.time }}</div>
            </div>

            <div
              v-for="event in dayEvents(day) || []"
              :key="event.id"
              :class="['event', { active: event.id === props?.activeEventId }]"
              :style="eventStyle(event, day)"
              draggable="true"
              @dragstart="handleDragStart($event, event)"
              @click.stop="handleEdit(event)"
            >
              <div class="event-tag">
                <span v-if="!isEmpty(event?.data?.importance)" class="i"
                  ><span>{{ event?.data?.importance[0] }}</span></span
                >
                <span v-if="!isEmpty(event?.data?.urgency)" class="u"
                  ><span>{{ event?.data?.urgency[0] }}</span></span
                >
                <span v-if="!isEmpty(event?.data?.progress)" class="p"
                  ><span>{{ event?.data?.progress[0] }}</span></span
                >
              </div>
              <div class="event-title">{{ event.title }}</div>
              <div class="event-time">{{ formatTime(event) }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, defineProps, onMounted, onUnmounted, ref } from 'vue'
import { DateTime } from 'luxon'
import type { CalendarEvent } from '@/components/action/views/CalendarType'
import ServerTime from '@/lib/ServerTime'
import { getContext } from '@/lib/getContext'
import { isEmpty } from '@/lib/util'

const props = defineProps<{
  schema?: {
    workHours?: { start: number; end: number }
    displayDays?: number[] // 新增配置项，例如 [1,2,3,4,5] 表示周一到周五
  }
  context?: { [key: string]: any }
  currentDate: DateTime
  activeEventId?: string
}>()

const emit = defineEmits(['event-drop', 'edit-event', 'add-event', 'reload-events'])

const MINUTES_PER_DAY = 24 * 60
const nonWorkHoursFolded = ref(true)
const showWorkdaysOnly = ref(true)
const currentTime = ref(DateTime.now())

let timeUpdateInterval: number
onMounted(() => {
  timeUpdateInterval = window.setInterval(() => {
    currentTime.value = DateTime.now()
  }, 10 * 1000)
})

onUnmounted(() => {
  clearInterval(timeUpdateInterval)
})

const WORK_HOUR = { start: 9 * 60, end: 18 * 60 } // 9 AM and 5 PM in minutes

const workStart = computed(() => props.schema?.workHours?.start ?? WORK_HOUR.start)
const workEnd = computed(() => props.schema?.workHours?.end ?? WORK_HOUR.end)

const showCurrentTimeLine = (day: DateTime) => {
  const now = currentTime.value
  return day.toISODate() === now.toISODate()
}

const currentTimeLineStyle = computed(() => {
  const now = currentTime.value
  const currentMinutes = now.hour * 60 + now.minute

  let topMinutes = currentMinutes
  const totalVisibleMinutes = nonWorkHoursFolded.value
    ? workEnd.value - workStart.value
    : MINUTES_PER_DAY
  const pixelsPerMinute = parseInt(dayHeight.value) / totalVisibleMinutes

  if (nonWorkHoursFolded.value) {
    topMinutes -= workStart.value
    const visibleTop = Math.min(Math.max(0, topMinutes), totalVisibleMinutes) * pixelsPerMinute
    const offhour = currentMinutes < workStart.value ? 1 : currentMinutes > workEnd.value ? 2 : 0
    return { offhour, top: `${visibleTop}px` }
  }

  return {
    offhour: 0,
    top: `${currentMinutes * pixelsPerMinute}px`
  }
})

const hours = computed(() => {
  if (nonWorkHoursFolded.value) {
    const startHour = Math.floor(workStart.value / 60)
    const endHour = Math.floor(workEnd.value / 60)
    return Array.from({ length: endHour - startHour }, (_, i) => startHour + i)
  }
  return Array.from({ length: 24 }, (_, i) => i)
})

const dayHeight = computed(() => {
  const totalVisibleMinutes = nonWorkHoursFolded.value
    ? workEnd.value - workStart.value
    : MINUTES_PER_DAY
  return `${totalVisibleMinutes}px`
})

const weekDays = computed(() => {
  const start = props.currentDate.startOf('week')
  const allDays = Array.from({ length: 7 }, (_, i) => start.plus({ days: i }))

  // 优先使用配置的 displayDays
  if (props.schema?.displayDays?.length) {
    return allDays.filter((day) => props.schema!.displayDays?.includes(day.weekday))
  }

  // 其次根据按钮状态过滤工作日
  if (showWorkdaysOnly.value) {
    return allDays.filter((day) => day.weekday <= 5) // 周一到周五
  }

  return allDays
})

const weekDaysLength = computed(() => weekDays.value.length)

const dayEvents = (day: DateTime) => {
  let { events = [] } = getContext(props)
  if (isEmpty(events)) return []
  return events?.filter?.((event: any) => DateTime.fromSeconds(event.startTime).hasSame(day, 'day'))
}

const formatTime = (event: CalendarEvent) => {
  const start = ServerTime.fromSeconds(event.startTime).toFormat('HH:mm')
  const end = ServerTime.fromSeconds(event.startTime + event.duration).toFormat('HH:mm')
  return `${start} - ${end}`
}

const eventStyle = (event: CalendarEvent, day: DateTime) => {
  const start = DateTime.fromSeconds(event.startTime, { zone: day.zoneName })
  const startMinutes = start.hour * 60 + start.minute
  const endMinutes = startMinutes + event.duration / 60

  if (nonWorkHoursFolded.value) {
    const visibleStart = Math.max(startMinutes, workStart.value)
    const visibleEnd = Math.min(endMinutes, workEnd.value)

    if (endMinutes <= workStart.value || startMinutes > workEnd.value) {
      return { display: 'none' }
    }

    return {
      top: `${visibleStart - workStart.value}px`,
      height: `${visibleEnd - visibleStart}px`,
      display: 'block'
    }
  }

  return {
    top: `${startMinutes}px`,
    height: `${event.duration / 60}px`,
    display: 'block'
  }
}

const hasHiddenEventsBeforeWorkStart = (day: DateTime) => {
  if (!nonWorkHoursFolded.value) return false
  const events = dayEvents(day)
  return events.some((event: CalendarEvent) => {
    const eventEnd = DateTime.fromSeconds(event.startTime + event.duration)
    return eventEnd.hour * 60 + eventEnd.minute <= workStart.value
  })
}

const hasHiddenEventsAfterWorkEnd = (day: DateTime) => {
  if (!nonWorkHoursFolded.value) return false
  const events = dayEvents(day)
  return events.some((event: CalendarEvent) => {
    const eventStart = DateTime.fromSeconds(event.startTime)
    return eventStart.hour * 60 + eventStart.minute >= workEnd.value + 60
  })
}

const draggedEvent = ref<CalendarEvent | null>(null)
const previewEvent = ref<{
  day: DateTime
  style: { top: string; height: string }
  title: string
  time: string
} | null>(null)

const calculateEventPosition = (e: DragEvent | MouseEvent, day: DateTime) => {
  const dayElement = e.currentTarget as HTMLElement
  const rect = dayElement.getBoundingClientRect()
  const offsetY = Math.max(0, Math.min(e.clientY - rect.top, rect.height))

  const totalVisibleMinutes = nonWorkHoursFolded.value
    ? workEnd.value - workStart.value
    : MINUTES_PER_DAY
  const pixelsPerMinute = dayElement.offsetHeight / totalVisibleMinutes
  let snappedMinutes = Math.round(offsetY / pixelsPerMinute / 15) * 15

  if (nonWorkHoursFolded.value) {
    snappedMinutes += workStart.value
  }

  return { snappedMinutes, dayElement }
}

const img = new Image()
img.src = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'

const handleDragStart = (e: DragEvent, event: CalendarEvent) => {
  if (!e.dataTransfer) return

  e.dataTransfer.setDragImage(img, 0, 0)
  e.dataTransfer.setData('text/plain', JSON.stringify(event))
  draggedEvent.value = event

  const target = e.target as HTMLElement
  target.classList.add('dragging')

  e.target?.addEventListener(
    'dragend',
    () => {
      previewEvent.value = null
      target.classList.remove('dragging')
    },
    { once: true }
  )
}

const handleDragOver = (day: DateTime, e: DragEvent) => {
  e.preventDefault()
  if (!draggedEvent.value) return

  let { snappedMinutes, dayElement } = calculateEventPosition(e, day)
  if (nonWorkHoursFolded.value) {
    snappedMinutes -= workStart.value
  }

  const previewStart = day.startOf('day').plus({ minutes: snappedMinutes })
  const durationMinutes = draggedEvent.value.duration / 60

  previewEvent.value = {
    day,
    style: {
      top: `${snappedMinutes}px`,
      height: `${durationMinutes}px`
    },
    title: draggedEvent.value.title,
    time: `${previewStart.toFormat('HH:mm')} - ${previewStart.plus({ minutes: durationMinutes }).toFormat('HH:mm')}`
  }
}

const handleDrop = (e: DragEvent, day: DateTime) => {
  const { snappedMinutes } = calculateEventPosition(e, day)
  const newStart = day.startOf('day').plus({ minutes: snappedMinutes })

  const eventData = JSON.parse(e.dataTransfer?.getData('text/plain') || '{}')
  emit('event-drop', eventData, newStart)
  previewEvent.value = null
}

const handleEdit = (event: CalendarEvent) => emit('edit-event', event)

const handleDoubleClick = (day: DateTime, e: MouseEvent) => {
  const { snappedMinutes } = calculateEventPosition(e, day)
  const eventStart = day.startOf('day').plus({ minutes: snappedMinutes })
  emit('add-event', eventStart)
}
</script>

<style lang="scss" scoped>
.week-view {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: auto;
}

.controls {
  padding: 0.5rem;
  background: #eee;

  button {
    padding: 0.25rem 0.5rem;
    background: #fff;
    border: 1px solid #ccc;
    border-radius: 4px;
    cursor: pointer;

    &:hover {
      background: #f0f0f0;
    }
  }
}

.weekdays-header {
  display: grid;
  padding: 0.5rem;
  background: #eee;
  font-weight: bold;
  text-align: center;
}

.weekdays-body {
  display: grid;
  overflow: auto;
  padding: 1rem 0.5rem;
}

.week-grid {
  display: flex;
  flex: 1;
  position: relative;
  overflow: visible;

  .current-time-line,
  .current-time-line-before,
  .current-time-line-after {
    position: absolute;
    left: 0;
    right: 0;
    height: 2px;
    background: #ff0000;
    pointer-events: none;
    display: flex;
    justify-content: center;

    &::before {
      content: '';
      position: absolute;
      left: -5px;
      top: -4px;
      width: 10px;
      height: 10px;
      background: #ff0000;
      border-radius: 50%;
    }

    span {
      display: block;
      margin-top: -10px;
      font-size: 8px;
      font-weight: bold;
    }
  }

  .current-time-line-before,
  .current-time-line-after {
    &::before {
      content: '▲';
      color: brown;
      background: none;
      left: 50%;
      transform: translateX(-50%);
    }

    background: transparent;
  }

  .current-time-line-after {
    &::before {
      content: '▼';
    }
  }
}

.time-grid {
  border-right: 1px solid #ddd;
}

.time-slot {
  height: 60px;
  position: relative;
}

.time-label {
  position: absolute;
  top: -10px;
  left: 5px;
  font-size: 0.8rem;
  color: #666;
}

.time-line {
  border-top: 1px solid #eee;
}

.days {
  display: grid;
  flex: 1;
}

.day {
  border-right: 1px solid #ddd;
  position: relative;
}

.hidden-indicators {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;

  .triangle {
    position: absolute;
    font-size: 10px;
    color: #666;
    z-index: 1000;

    &.top {
      top: 2px;
      left: 50%;
      transform: translateX(-50%);
    }

    &.bottom {
      bottom: 2px;
      left: 50%;
      transform: translateX(-50%);
    }
  }
}

.event {
  position: absolute;
  left: 5px;
  right: 5px;
  background: #e3f2fd;
  border: 2px solid white;
  border-left: 3px solid #2196f3;
  padding: 5px;
  border-radius: 3px;
  cursor: pointer;
  min-width: 0;
  box-shadow: 1px 1px 3px lightslategray;

  &-title {
    font-weight: bold;
    font-size: 10pt;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &-time {
    font-size: 8pt;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &.preview-event {
    opacity: 0.7;
    background: #b3e5fc;
    border-left: 3px solid #03a9f4;
  }

  &-tag {
    display: flex;

    span {
      font-size: 6pt;
      font-weight: bolder;
    }
  }

  &.active {
    background: #bbdefb !important;
    border-left: 3px solid brown;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 1);
  }

  .i {
    color: brown;
  }

  .u {
    color: darkblue;
  }

  .p {
    color: #1c6ca1;
  }

  &.dragging {
    opacity: 0;
  }
}
</style>
