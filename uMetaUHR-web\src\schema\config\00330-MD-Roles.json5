{
  type: 'tocDetail',
  toc: {
    type: 'toc',
    query: {
      search: {
        table: 'role',
        tocItems: ['id', 'name'],
        detailItems: ['id', 'name', 'descriptor', 'data']
      }
    },
    columnDef: [
      {
        field: 'id',
        displayName: 'id'
      },
      {
        field: 'name',
        displayName: '名称'
      }
    ],
    selectedLine: 0
  },
  style: {
    toc: {
      width: '10rem'
    }
  },
  detail: {
    body: [
      {
        type: 'window',
        body: [
          {
            type: 'section',
            myFavoriteId: '00330-MD-Roles.section-1',
            title: '基本信息',
            children: [
              {
                type: 'inputText',
                label: 'ID',
                model: 'id'
              }
            ]
          },
          {
            type: 'section',
            title: '数据',
            myFavoriteId: '00330-MD-Roles.section-3',
            children: [
              {
                type: 'dataViewer',
                model: ''
              }
            ]
          }
        ],
        subContext: {
          path: 'detail',
          default: {}
        }
      }
    ],
    head: [
      {
        type: 'toolbar',
        children: [
          {
            type: 'button',
            event: {
              name: 'toc-new-data'
            },
            label: '新建'
          },
          {
            type: 'button',
            event: {
              name: 'toc-save-data'
            },
            label: '保存'
          },
          {
            type: 'button',
            event: {
              name: 'toc-delete-data'
            },
            label: '删除'
          }
        ]
      }
    ],
    type: 'window'
  }
}
