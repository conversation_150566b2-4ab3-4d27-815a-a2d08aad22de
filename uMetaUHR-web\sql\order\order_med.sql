CREATE TABLE medication_order
(
    id                    INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    patient_id            INT  NOT NULL,
    encounter_id          INT  NOT NULL,
    order_type            VARCHAR(20)  NOT NULL DEFAULT 'STANDARD',

    -- Medication information
    drug_id               INT  NOT NULL,
    drug_name             VARCHAR(100) NOT NULL,
    dosage_form           VARCHAR(50)  NOT NULL,
    specification         VARCHAR(100) NOT NULL,

    -- Administration details
    dosage                JSONB        NOT NULL,
    route                 VARCHAR(50)  NOT NULL,
    frequency             VARCHAR(50)  NOT NULL,
    duration              JSONB,

    -- Timing information
    start_time            BIGINT                DEFAULT EXTRACT(EPOCH FROM NOW()),
    end_time              BIGINT,
    prn_condition         TEXT,

    -- Order status
    status                VARCHAR(20)  NOT NULL,
    status_reason         TEXT,

    -- Prescription info
    prescriber_id         INT  NOT NULL,
    prescriber_name       VARCHA<PERSON>(100) NOT NULL,
    order_datetime        BIGINT                DEFAULT EXTRACT(EPOCH FROM NOW()),

    -- Verification info
    verifier_id           INT,
    verification_datetime BIGINT,
    verification_notes    TEXT,

    -- Related information
    diagnosis_codes       VARCHAR(100)[],
    related_orders        BIGINT[],

    -- Clinical context
    clinical_context      JSONB,

    created_at            BIGINT                DEFAULT EXTRACT(EPOCH FROM NOW()),
    updated_at            BIGINT                DEFAULT EXTRACT(EPOCH FROM NOW()),

    FOREIGN KEY (drug_id) REFERENCES drug (id)
);

COMMENT ON TABLE medication_order IS '药品医嘱信息表';
COMMENT ON COLUMN medication_order.patient_id IS '患者唯一标识';
COMMENT ON COLUMN medication_order.encounter_id IS '就诊/住院编号';
COMMENT ON COLUMN medication_order.order_type IS '医嘱类型(STANDARD-标准/PRN-按需/STAT-即刻/TEMPORARY-临时)';
COMMENT ON COLUMN medication_order.drug_id IS '关联药品表';
COMMENT ON COLUMN medication_order.drug_name IS '药品名称(冗余存储)';
COMMENT ON COLUMN medication_order.dosage_form IS '剂型';
COMMENT ON COLUMN medication_order.specification IS '规格';
COMMENT ON COLUMN medication_order.dosage IS '剂量信息(JSON格式)';
COMMENT ON COLUMN medication_order.route IS '给药途径';
COMMENT ON COLUMN medication_order.frequency IS '给药频次';
COMMENT ON COLUMN medication_order.duration IS '疗程(JSON格式)';
COMMENT ON COLUMN medication_order.start_time IS '医嘱开始时间';
COMMENT ON COLUMN medication_order.end_time IS '医嘱结束时间';
COMMENT ON COLUMN medication_order.prn_condition IS 'PRN医嘱使用条件';
COMMENT ON COLUMN medication_order.status IS '医嘱状态(DRAFT-草稿/ACTIVE-生效/ON_HOLD-暂停/COMPLETED-完成/CANCELLED-取消)';
COMMENT ON COLUMN medication_order.status_reason IS '状态变更原因';
COMMENT ON COLUMN medication_order.prescriber_id IS '开嘱人ID';
COMMENT ON COLUMN medication_order.prescriber_name IS '开嘱人姓名';
COMMENT ON COLUMN medication_order.order_datetime IS '医嘱创建时间';
COMMENT ON COLUMN medication_order.verifier_id IS '核对人ID';
COMMENT ON COLUMN medication_order.verification_datetime IS '核对时间';
COMMENT ON COLUMN medication_order.verification_notes IS '核对意见';
COMMENT ON COLUMN medication_order.diagnosis_codes IS '相关诊断ICD编码';
COMMENT ON COLUMN medication_order.related_orders IS '关联医嘱ID';
COMMENT ON COLUMN medication_order.clinical_context IS '临床上下文(JSON格式)';
COMMENT ON COLUMN medication_order.created_at IS '记录创建时间';
COMMENT ON COLUMN medication_order.updated_at IS '记录更新时间';

-- Indexes
CREATE INDEX idx_order_patient ON medication_order (patient_id);
CREATE INDEX idx_order_status ON medication_order (status);
CREATE INDEX idx_order_drug ON medication_order (drug_id);
CREATE INDEX idx_order_prescriber ON medication_order (prescriber_id);
CREATE INDEX idx_order_datetime ON medication_order (order_datetime);
