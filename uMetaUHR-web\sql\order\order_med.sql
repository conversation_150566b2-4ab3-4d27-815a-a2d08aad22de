CREATE TABLE medication_order
(
    id                    INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    patient_id            INT  NOT NULL,
    encounter_id          INT  NOT NULL,
    order_type            VARCHAR(20)  NOT NULL DEFAULT 'STANDARD',

    -- Medication information
    drug_id               INT  NOT NULL,
    drug_name             VARCHAR(100) NOT NULL,
    dosage_form           VARCHAR(50)  NOT NULL,
    specification         VARCHAR(100) NOT NULL,

    -- Administration details
    dosage                JSONB        NOT NULL,
    route                 VARCHAR(50)  NOT NULL,
    frequency             VARCHAR(50)  NOT NULL,
    duration              JSONB,

    -- Timing information
    start_time            BIGINT                DEFAULT EXTRACT(EPOCH FROM NOW()),
    end_time              BIGINT,
    prn_condition         TEXT,

    -- Order status
    status                VARCHAR(20)  NOT NULL,
    status_reason         TEXT,

    -- Prescription info
    prescriber_id         INT  NOT NULL,
    prescriber_name       VA<PERSON>HA<PERSON>(100) NOT NULL,
    order_datetime        BIGINT                DEFAULT EXTRACT(EPOCH FROM NOW()),

    -- Verification info
    verifier_id           INT,
    verification_datetime BIGINT,
    verification_notes    TEXT,

    -- Related information
    diagnosis_codes       VARCHAR(100)[],
    related_orders        BIGINT[],

    -- Clinical context
    clinical_context      JSONB,

    created_at            BIGINT                DEFAULT EXTRACT(EPOCH FROM NOW()),
    updated_at            BIGINT                DEFAULT EXTRACT(EPOCH FROM NOW()),

    FOREIGN KEY (drug_id) REFERENCES drug (id)
);

COMMENT ON TABLE medication_order IS 'Medication order information';
COMMENT ON COLUMN medication_order.patient_id IS 'Patient unique identifier';
COMMENT ON COLUMN medication_order.encounter_id IS 'Encounter/hospitalization number';
COMMENT ON COLUMN medication_order.order_type IS 'Order type (STANDARD/PRN/STAT/TEMPORARY)';
COMMENT ON COLUMN medication_order.drug_id IS 'Reference to drug table';
COMMENT ON COLUMN medication_order.drug_name IS 'Drug name (redundant storage)';
COMMENT ON COLUMN medication_order.dosage_form IS 'Dosage form';
COMMENT ON COLUMN medication_order.specification IS 'Specification';
COMMENT ON COLUMN medication_order.dosage IS 'Dosage information in JSON format';
COMMENT ON COLUMN medication_order.route IS 'Administration route';
COMMENT ON COLUMN medication_order.frequency IS 'Administration frequency';
COMMENT ON COLUMN medication_order.duration IS 'Treatment duration in JSON format';
COMMENT ON COLUMN medication_order.start_time IS 'Start time of order';
COMMENT ON COLUMN medication_order.end_time IS 'End time of order';
COMMENT ON COLUMN medication_order.prn_condition IS 'PRN order usage condition';
COMMENT ON COLUMN medication_order.status IS 'Order status (DRAFT/ACTIVE/ON_HOLD/COMPLETED/CANCELLED)';
COMMENT ON COLUMN medication_order.status_reason IS 'Reason for status change';
COMMENT ON COLUMN medication_order.prescriber_id IS 'Prescriber ID';
COMMENT ON COLUMN medication_order.prescriber_name IS 'Prescriber name';
COMMENT ON COLUMN medication_order.order_datetime IS 'Order creation time';
COMMENT ON COLUMN medication_order.verifier_id IS 'Verifier ID';
COMMENT ON COLUMN medication_order.verification_datetime IS 'Verification time';
COMMENT ON COLUMN medication_order.verification_notes IS 'Verification comments';
COMMENT ON COLUMN medication_order.diagnosis_codes IS 'Related diagnosis ICD codes';
COMMENT ON COLUMN medication_order.related_orders IS 'Related order IDs';
COMMENT ON COLUMN medication_order.clinical_context IS 'Clinical context in JSON format';
COMMENT ON COLUMN medication_order.created_at IS 'Timestamp when record was created';
COMMENT ON COLUMN medication_order.updated_at IS 'Timestamp when record was last updated';

-- Indexes
CREATE INDEX idx_order_patient ON medication_order (patient_id);
CREATE INDEX idx_order_status ON medication_order (status);
CREATE INDEX idx_order_drug ON medication_order (drug_id);
CREATE INDEX idx_order_prescriber ON medication_order (prescriber_id);
CREATE INDEX idx_order_datetime ON medication_order (order_datetime);
