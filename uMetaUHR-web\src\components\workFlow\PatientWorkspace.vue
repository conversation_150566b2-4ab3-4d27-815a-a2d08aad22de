<template>
  <div class="patent">
    <component
      :is="loadComponent(schema?.ui?.type || '')"
      v-if="schema"
      :context="computedContext"
      :schema="schema?.ui"
    />
  </div>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted, watch, computed, defineProps } from 'vue'
import { loadComponent } from '../componentRegistry'
import { getContext } from '@/lib/getContext'
import { editorManager } from '@/lib/EditorOperator/EditorInstanceManager'
import { editorDataSyncService } from '@/lib/EditorOperator/editorDataSyncService'

const props = defineProps<{
  schema: {
    type: string
    ui?: { type?: string }
    children: Array<any>
  }
  context: { [key: string]: any }
}>()

// 定义当前组件的viewId
const VIEW_ID = '门诊工作区'

// 增强context，为子组件添加editorViewId
const computedContext = computed(() => {
  const ctx = getContext(props)
  return {
    ...ctx,
    editorViewId: VIEW_ID
  }
})

async function fetchPatientData() {
  // let context = getContext(props)
  // let { workspace = {}, workspace: { patient: { id = 0 } = {} } = {} } = context || {}
  // if (!id) return

  // const sql = `
  //   SELECT *
  //   FROM "maxEMR".patient_basic_info p
  //   WHERE p.id = ?::int
  // `

  // const patData = await dbGet({
  //   sql,
  //   param: [id]
  // })

  // if (!patData) {
  //   console.error('Patient not found with id:', id)
  //   return
  // }

  // workspace.patient = patData
  // return {}
}


//通过编辑器管理器获取编辑器操作器
async function openEditorRecord() {
  try {
    const operator = await editorManager.getEditor(VIEW_ID)

    // 设置编辑器上下文（患者ID、文书ID等）
    const context = getContext(props)
    const patientId = context?.workspace?.patient?.id || 'P001' // 临时使用固定患者ID
    editorManager.setEditorContext(VIEW_ID, patientId, 'DOC001', 'outpatient')

    await operator.openRecord('门诊病历2.0')

    // // 模拟发送数据同步Action（用于测试）
    // setTimeout(() => {
    //   simulateDataSync(patientId)
    // }, 3000) // 3秒后模拟数据变更

  } catch (error) {
    console.error('Error in openEditorRecord:', error)
  }
}

// 模拟数据同步Action（用于测试）
function simulateDataSync(patientId: string) {
  console.log('🧪 开始模拟数据同步...')

  // 模拟医嘱添加
  editorDataSyncService.simulateAction(
    'ORDER_ADDED',
    '医嘱模块',
    VIEW_ID,
    patientId,
    'ORDER_001'
  )
}



onMounted(() => {
  fetchPatientData()

  openEditorRecord()
})

// 组件卸载时清理编辑器实例
onUnmounted(() => {
  if (editorManager.hasEditor(VIEW_ID)) {
    editorManager.removeEditor(VIEW_ID)
    console.log(`Editor instance for ${VIEW_ID} cleaned up`)
  }
})

watch(
  () => {
    const context = getContext(props)
    return context?.workspace?.patient?.id
  },
  (newId, oldId) => {
    if (newId && newId !== oldId) {
      fetchPatientData()

      // 更新编辑器上下文
      if (editorManager.hasEditor(VIEW_ID)) {
        editorManager.setEditorContext(VIEW_ID, newId, 'DOC001', 'outpatient')
      }

      openEditorRecord()
    }
  }
)

defineExpose({ compName: props?.schema?.type || '-' })
</script>

<style lang="scss" scoped>
.patent {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  overflow: auto;
}
</style>
