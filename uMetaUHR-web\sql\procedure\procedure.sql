-- ===================================================================
-- 医疗操作数据模型
-- 基于Epic系统EAP/EDP主文件和医疗行业标准
-- 遵循Cline数据库设计规范
-- ===================================================================

-- ===================================================================
-- 1. 操作主表 (EAP主文件等效)
-- ===================================================================

CREATE TABLE procedures (
    id INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    
    -- 核心标识
    proc_code VARCHAR(50) NOT NULL,
    proc_name VARCHAR(255) NOT NULL,
    proc_display_name VARCHAR(255),
    
    -- 分类信息
    proc_category_id INTEGER NOT NULL,
    proc_type VARCHAR(20) NOT NULL CHECK (proc_type IN ('LAB', 'IMAGING', 'SURGERY', 'CONSULTATION', 'NURSING', 'PHARMACY', 'REHABILITATION')),
    
    -- 标准编码
    cpt_code VARCHAR(10),
    hcpcs_code VARCHAR(10),
    loinc_code VARCHAR(10),
    snomed_ct_code VARCHAR(18),
    icd10pcs_code VARCHAR(7),
    
    -- 状态标志
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    is_billable BOOLEAN NOT NULL DEFAULT TRUE,
    is_orderable BOOLEAN NOT NULL DEFAULT TRUE,
    is_performable BOOLEAN NOT NULL DEFAULT TRUE,
    
    -- 临床属性
    default_specimen_type VARCHAR(50),
    default_body_site VARCHAR(100),
    default_laterality VARCHAR(10) CHECK (default_laterality IN ('LEFT', 'RIGHT', 'BILATERAL', 'UNILATERAL', 'NA')),
    
    -- 文档信息
    description TEXT,
    instructions TEXT,
    contraindications TEXT,
    preparation_notes TEXT,
    
    -- 报告配置
    report_template_id INTEGER,
    default_priority VARCHAR(20) DEFAULT 'ROUTINE',
    
    -- 审计字段
    created_at INTEGER NOT NULL DEFAULT extract(epoch from now()),
    created_by INTEGER NOT NULL,
    updated_at INTEGER,
    updated_by INTEGER,
    
    -- 约束
    CONSTRAINT uk_procedures_code UNIQUE (proc_code)
);

COMMENT ON TABLE procedures IS '医疗操作主表，存储所有临床操作的基础信息';
COMMENT ON COLUMN procedures.proc_code IS '系统内部唯一操作代码';
COMMENT ON COLUMN procedures.proc_name IS '正式医学名称';
COMMENT ON COLUMN procedures.proc_display_name IS '用户界面显示名称';
COMMENT ON COLUMN procedures.proc_type IS '操作类型：检验/影像/手术/会诊/护理/药学/康复';

-- ===================================================================
-- 2. 操作分类表 (EDP主文件等效)
-- ===================================================================

CREATE TABLE procedure_categories (
    id INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    
    -- 基础信息
    category_code VARCHAR(50) NOT NULL,
    category_name VARCHAR(100) NOT NULL,
    
    -- 业务分类
    order_type VARCHAR(50) NOT NULL, -- 'IMAGING', 'LAB', 'SURGERY', 'CONSULTATION', 'NURSING', 'PHARMACY', 'REHABILITATION'
    specialty VARCHAR(50),
    
    -- 默认设置
    default_priority VARCHAR(20) DEFAULT 'ROUTINE',
    default_frequency VARCHAR(50),
    default_order_class VARCHAR(50),
    
    -- 配置信息
    allowed_order_classes JSONB DEFAULT '[]',
    allowed_frequencies JSONB DEFAULT '[]',
    required_questions JSONB DEFAULT '[]',
    
    -- 工作流
    default_workflow_id INTEGER,
    auto_schedule BOOLEAN DEFAULT FALSE,
    
    -- 文档
    description TEXT,
    
    -- 审计
    created_at INTEGER NOT NULL DEFAULT extract(epoch from now()),
    created_by INTEGER NOT NULL,
    updated_at INTEGER,
    updated_by INTEGER,
    
    CONSTRAINT uk_procedure_categories_code UNIQUE (category_code)
);

COMMENT ON TABLE procedure_categories IS '操作分类表，用于分组管理相似操作';
COMMENT ON COLUMN procedure_categories.order_type IS '业务大类，用于工作流路由';

-- ===================================================================
-- 3. 操作用途定义表
-- ===================================================================

CREATE TABLE procedure_purposes (
    id INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    procedure_id INTEGER NOT NULL REFERENCES procedures(id),
    
    purpose_type VARCHAR(20) NOT NULL CHECK (purpose_type IN ('ORDERABLE', 'PERFORMABLE', 'CHARGEABLE', 'HISTORICAL')),
    is_primary BOOLEAN NOT NULL DEFAULT FALSE,
    
    -- 关联关系
    linked_procedure_id INTEGER REFERENCES procedures(id),
    
    -- 有效期
    effective_date DATE NOT NULL DEFAULT CURRENT_DATE,
    end_date DATE,
    
    -- 审计
    created_at INTEGER NOT NULL DEFAULT extract(epoch from now()),
    created_by INTEGER NOT NULL,
    
    CONSTRAINT uk_procedure_purposes UNIQUE (procedure_id, purpose_type)
);

COMMENT ON TABLE procedure_purposes IS '定义每个操作记录的用途类型';
COMMENT ON COLUMN procedure_purposes.purpose_type IS '用途：可开立/可执行/可计费/历史记录';

-- ===================================================================
-- 4. 操作资源需求表
-- ===================================================================

CREATE TABLE procedure_resources (
    id INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    procedure_id INTEGER NOT NULL REFERENCES procedures(id),
    
    resource_type VARCHAR(50) NOT NULL CHECK (resource_type IN ('STAFF', 'EQUIPMENT', 'INSTRUMENT', 'SUPPLY', 'ROOM', 'MEDICATION')),
    resource_id INTEGER NOT NULL,
    resource_name VARCHAR(255),
    
    quantity INTEGER NOT NULL DEFAULT 1,
    is_required BOOLEAN NOT NULL DEFAULT TRUE,
    preparation_time_minutes INTEGER DEFAULT 0,
    cleanup_time_minutes INTEGER DEFAULT 0,
    
    notes TEXT,
    
    -- 审计
    created_at INTEGER NOT NULL DEFAULT extract(epoch from now()),
    created_by INTEGER NOT NULL,
    
    CONSTRAINT uk_procedure_resources UNIQUE (procedure_id, resource_type, resource_id)
);

COMMENT ON TABLE procedure_resources IS '操作执行所需的资源规划';

-- ===================================================================
-- 5. 操作协议表 (专科操作协议)
-- ===================================================================

CREATE TABLE procedure_protocols (
    id INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    procedure_id INTEGER NOT NULL REFERENCES procedures(id),
    
    protocol_name VARCHAR(100) NOT NULL,
    abbreviated_name VARCHAR(50),
    report_name VARCHAR(100),
    
    -- 标本属性
    specimen_type VARCHAR(50),
    specimen_handling TEXT,
    fixation_requirements TEXT,
    transport_requirements TEXT,
    
    -- 处理流程
    processing_steps JSONB DEFAULT '[]',
    staining_requirements JSONB DEFAULT '[]',
    equipment_settings JSONB DEFAULT '{}',
    
    -- 质量控制
    qc_requirements JSONB DEFAULT '[]',
    expected_turnaround_hours INTEGER,
    
    -- 文档
    description TEXT,
    references TEXT,
    
    -- 审计
    created_at INTEGER NOT NULL DEFAULT extract(epoch from now()),
    created_by INTEGER NOT NULL,
    updated_at INTEGER,
    updated_by INTEGER
);

COMMENT ON TABLE procedure_protocols IS '专科操作的详细协议和标准化流程';

-- ===================================================================
-- 6. 操作费用关联表
-- ===================================================================

CREATE TABLE procedure_charge_linkages (
    id INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    procedure_id INTEGER NOT NULL REFERENCES procedures(id),
    charge_procedure_id INTEGER NOT NULL REFERENCES procedures(id),
    
    charge_type VARCHAR(20) NOT NULL CHECK (charge_type IN ('PROFESSIONAL', 'TECHNICAL', 'GLOBAL', 'FACILITY', 'PROFESSIONAL_FEE', 'ANESTHESIA', 'SUPPLY')),
    
    -- 定价
    base_price DECIMAL(10,2),
    billing_multiplier DECIMAL(3,2) DEFAULT 1.0,
    
    -- 有效期
    effective_date DATE NOT NULL DEFAULT CURRENT_DATE,
    end_date DATE,
    
    -- 审计
    created_at INTEGER NOT NULL DEFAULT extract(epoch from now()),
    created_by INTEGER NOT NULL,
    
    CONSTRAINT uk_procedure_charge_linkages UNIQUE (procedure_id, charge_procedure_id, charge_type)
);

COMMENT ON TABLE procedure_charge_linkages IS '临床操作与计费操作的关联关系';

-- ===================================================================
-- 7. 医嘱开立配置表
-- ===================================================================

CREATE TABLE order_composer_configs (
    id INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    config_name VARCHAR(100) NOT NULL,
    
    -- 上下文
    ordering_type VARCHAR(20) NOT NULL CHECK (ordering_type IN ('PROCEDURE', 'MEDICATION')),
    context VARCHAR(50) NOT NULL CHECK (context IN ('AMBULATORY', 'INPATIENT', 'EMERGENCY', 'TELEHEALTH')),
    
    -- 显示配置
    display_items JSONB NOT NULL DEFAULT '[]',
    summary_items JSONB NOT NULL DEFAULT '[]',
    controlled_items JSONB DEFAULT '[]',
    
    -- 关联
    procedure_id INTEGER REFERENCES procedures(id),
    category_id INTEGER REFERENCES procedure_categories(id),
    
    is_system_default BOOLEAN DEFAULT FALSE,
    
    -- 审计
    created_at INTEGER NOT NULL DEFAULT extract(epoch from now()),
    created_by INTEGER NOT NULL,
    updated_at INTEGER,
    updated_by INTEGER
);

COMMENT ON TABLE order_composer_configs IS '医嘱开立界面的配置信息';

-- ===================================================================
-- 8. 操作别名表
-- ===================================================================

CREATE TABLE procedure_aliases (
    id INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    procedure_id INTEGER NOT NULL REFERENCES procedures(id),
    
    alias_name VARCHAR(255) NOT NULL,
    alias_type VARCHAR(20) NOT NULL CHECK (alias_type IN ('COMMON', 'ABBREVIATION', 'ACRONYM', 'ALTERNATE_LANGUAGE')),
    
    language_code VARCHAR(10) DEFAULT 'zh-CN',
    is_preferred BOOLEAN DEFAULT FALSE,
    
    -- 审计
    created_at INTEGER NOT NULL DEFAULT extract(epoch from now()),
    created_by INTEGER NOT NULL,
    
    CONSTRAINT uk_procedure_aliases UNIQUE (procedure_id, alias_name, language_code)
);

COMMENT ON TABLE procedure_aliases IS '操作的别名、同义词和多语言支持';

-- ===================================================================
-- 9. 外部系统映射表
-- ===================================================================

CREATE TABLE procedure_mappings (
    id INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    procedure_id INTEGER NOT NULL REFERENCES procedures(id),
    
    external_system VARCHAR(50) NOT NULL, -- 'HL7', 'DICOM', 'EPIC', 'CERNER', '医保系统'等
    external_code VARCHAR(50) NOT NULL,
    external_name VARCHAR(255),
    
    -- 映射详情
    mapping_type VARCHAR(20) NOT NULL CHECK (mapping_type IN ('EXACT', 'EQUIVALENT', 'BROADER', 'NARROWER', 'RELATED')),
    confidence_score DECIMAL(3,2),
    
    -- 状态
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    
    -- 审计
    created_at INTEGER NOT NULL DEFAULT extract(epoch from now()),
    created_by INTEGER NOT NULL,
    updated_at INTEGER,
    updated_by INTEGER,
    
    CONSTRAINT uk_procedure_mappings UNIQUE (procedure_id, external_system, external_code)
);

COMMENT ON TABLE procedure_mappings IS '与外部系统的编码映射关系';

-- ===================================================================
-- 10. 操作文档模板表
-- ===================================================================

CREATE TABLE procedure_templates (
    id INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    procedure_id INTEGER NOT NULL REFERENCES procedures(id),
    
    template_name VARCHAR(100) NOT NULL,
    template_type VARCHAR(20) NOT NULL CHECK (template_type IN ('ORDER', 'RESULT', 'REPORT', 'CONSENT', 'PROTOCOL')),
    
    -- 模板内容
    template_content JSONB NOT NULL,
    required_fields JSONB DEFAULT '[]',
    optional_fields JSONB DEFAULT '[]',
    
    -- 使用设置
    is_default BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    
    -- 审计
    created_at INTEGER NOT NULL DEFAULT extract(epoch from now()),
    created_by INTEGER NOT NULL,
    updated_at INTEGER,
    updated_by INTEGER
);

COMMENT ON TABLE procedure_templates IS '标准化文档模板';

-- ===================================================================
-- 11. 操作状态流转表
-- ===================================================================

CREATE TABLE procedure_status_history (
    id INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    procedure_id INTEGER NOT NULL REFERENCES procedures(id),
    
    status VARCHAR(20) NOT NULL CHECK (status IN ('PENDING', 'SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'DISCONTINUED')),
    status_reason VARCHAR(255),
    
    -- 时间戳
    status_time INTEGER NOT NULL DEFAULT extract(epoch from now()),
    estimated_completion_time INTEGER,
    actual_completion_time INTEGER,
    
    -- 执行者
    performed_by INTEGER,
    verified_by INTEGER,
    
    -- 位置
    location_id INTEGER,
    
    -- 备注
    notes TEXT,
    
    -- 审计
    created_at INTEGER NOT NULL DEFAULT extract(epoch from now()),
    created_by INTEGER NOT NULL
);

COMMENT ON TABLE procedure_status_history IS '操作状态变更历史记录';

-- ===================================================================
-- 12. 操作医嘱表
-- ===================================================================

CREATE TABLE procedure_orders (
    id INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    
    -- 基本信息
    order_number VARCHAR(50) NOT NULL,
    procedure_id INTEGER NOT NULL REFERENCES procedures(id),
    
    -- 患者信息
    patient_id INTEGER NOT NULL,
    encounter_id INTEGER,
    
    -- 医嘱信息
    order_priority VARCHAR(20) DEFAULT 'ROUTINE',
    order_status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    
    -- 时间信息
    order_date INTEGER NOT NULL DEFAULT extract(epoch from now()),
    scheduled_date INTEGER,
    start_date INTEGER,
    end_date INTEGER,
    
    -- 医生信息
    ordering_physician_id INTEGER NOT NULL,
    attending_physician_id INTEGER,
    performing_physician_id INTEGER,
    
    -- 位置信息
    ordering_department_id INTEGER,
    performing_department_id INTEGER,
    
    -- 特殊要求
    special_instructions TEXT,
    pre_op_diagnosis TEXT,
    post_op_diagnosis TEXT,
    
    -- 审计
    created_at INTEGER NOT NULL DEFAULT extract(epoch from now()),
    created_by INTEGER NOT NULL,
    updated_at INTEGER,
    updated_by INTEGER,
    
    CONSTRAINT uk_procedure_orders_number UNIQUE (order_number)
);

COMMENT ON TABLE procedure_orders IS '医疗操作医嘱主表';

-- ===================================================================
-- 13. 操作结果表
-- ===================================================================

CREATE TABLE procedure_results (
    id INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    order_id INTEGER NOT NULL REFERENCES procedure_orders(id),
    
    -- 结果信息
    result_type VARCHAR(20) NOT NULL CHECK (result_type IN ('QUALITATIVE', 'QUANTITATIVE', 'TEXT', 'IMAGE')),
    result_value TEXT,
    result_unit VARCHAR(50),
    
    -- 参考信息
    reference_range VARCHAR(100),
    normal_range VARCHAR(100),
    
    -- 标志位
    is_abnormal BOOLEAN DEFAULT FALSE,
    is_critical BOOLEAN DEFAULT FALSE,
    
    -- 时间信息
    result_date INTEGER NOT NULL DEFAULT extract(epoch from now()),
    verified_date INTEGER,
    
    -- 执行者
    performed_by INTEGER NOT NULL,
    verified_by INTEGER,
    
    -- 备注
    comments TEXT,
    
    -- 审计
    created_at INTEGER NOT NULL DEFAULT extract(epoch from now()),
    created_by INTEGER NOT NULL
);

COMMENT ON TABLE procedure_results IS '操作结果记录';

-- ===================================================================
-- 性能优化索引
-- ===================================================================

-- 核心操作索引
CREATE INDEX idx_procedures_code ON procedures(proc_code);
CREATE INDEX idx_procedures_name ON procedures(proc_name);
CREATE INDEX idx_procedures_category ON procedures(proc_category_id);
CREATE INDEX idx_procedures_active ON procedures(is_active) WHERE is_active = TRUE;
CREATE INDEX idx_procedures_billable ON procedures(is_billable) WHERE is_billable = TRUE;
CREATE INDEX idx_procedures_type ON procedures(proc_type);

-- 分类索引
CREATE INDEX idx_procedure_categories_order_type ON procedure_categories(order_type);
CREATE INDEX idx_procedure_categories_specialty ON procedure_categories(specialty);

-- 用途索引
CREATE INDEX idx_procedure_purposes_type ON procedure_purposes(purpose_type);
CREATE INDEX idx_procedure_purposes_procedure ON procedure_purposes(procedure_id);

-- 资源索引
CREATE INDEX idx_procedure_resources_type ON procedure_resources(resource_type);
CREATE INDEX idx_procedure_resources_procedure ON procedure_resources(procedure_id);

-- 别名索引
CREATE INDEX idx_procedure_aliases_name ON procedure_aliases(alias_name);
CREATE INDEX idx_procedure_aliases_type ON procedure_aliases(alias_type);

-- 映射索引
CREATE INDEX idx_procedure_mappings_system ON procedure_mappings(external_system);
CREATE INDEX idx_procedure_mappings_code ON procedure_mappings(external_code);

-- 状态历史索引
CREATE INDEX idx_procedure_status_history_procedure ON procedure_status_history(procedure_id);
CREATE INDEX idx_procedure_status_history_status ON procedure_status_history(status);

-- 医嘱索引
CREATE INDEX idx_procedure_orders_patient ON procedure_orders(patient_id);
CREATE INDEX idx_procedure_orders_procedure ON procedure_orders(procedure_id);
CREATE INDEX idx_procedure_orders_status ON procedure_orders(order_status);

-- 结果索引
CREATE INDEX idx_procedure_results_order ON procedure_results(order_id);
CREATE INDEX idx_procedure_results_abnormal ON procedure_results(is_abnormal) WHERE is_abnormal = TRUE;

-- ===================================================================
-- 常用视图
-- ===================================================================

-- 活跃操作视图
CREATE VIEW active_procedures AS
SELECT 
    p.id,
    p.proc_code,
    p.proc_name,
    p.proc_display_name,
    pc.category_name,
    p.proc_type,
    p.cpt_code,
    p.is_billable,
    p.is_orderable,
    p.description
FROM procedures p
JOIN procedure_categories pc ON p.proc_category_id = pc.id
WHERE p.is_active = TRUE;

-- 操作摘要视图
CREATE VIEW procedure_summary AS
SELECT 
    p.id,
    p.proc_code,
    p.proc_name,
    pc.category_name,
    pc.order_type,
    pp.purpose_type,
    COUNT(pr.id) as resource_count
FROM procedures p
JOIN procedure_categories pc ON p.proc_category_id = pc.id
LEFT JOIN procedure_purposes pp ON p.id = pp.procedure_id AND pp.is_primary = TRUE
LEFT JOIN procedure_resources pr ON p.id = pr.procedure_id
WHERE p.is_active = TRUE
GROUP BY p.id, p.proc_code, p.proc_name, pc.category_name, pc.order_type, pp.purpose_type;

-- 待执行医嘱视图
CREATE VIEW pending_orders AS
SELECT 
    po.id,
    po.order_number,
    p.proc_name,
    po.patient_id,
    po.order_priority,
    po.scheduled_date,
    po.ordering_physician_id,
    po.ordering_department_id
FROM procedure_orders po
JOIN procedures p ON po.procedure_id = p.id
WHERE po.order_status = 'PENDING';

-- ===================================================================
-- 初始数据
-- ===================================================================

-- 插入根操作分类
INSERT INTO procedure_categories (category_code, category_name, order_type, specialty, description) VALUES
('IMAGING', '医学影像', 'IMAGING', '放射科', '所有诊断影像检查'),
('LAB', '实验室检查', 'LAB', '检验科', '所有实验室和病理检查'),
('SURGERY', '手术操作', 'SURGERY', '外科', '所有手术和操作程序'),
('CONSULT', '会诊服务', 'CONSULTATION', '多专科', '医疗会诊服务'),
('NURSING', '护理操作', 'NURSING', '护理部', '护理操作程序'),
('PHARMACY', '药学服务', 'PHARMACY', '药学部', '药物治疗相关操作'),
('REHABILITATION', '康复治疗', 'REHABILITATION', '康复科', '康复治疗操作');

-- 插入常用操作类型
INSERT INTO procedures (proc_code, proc_name, proc_display_name, proc_category_id, proc_type, cpt_code, description) VALUES
('CT_HEAD', '头部CT平扫', '头部CT', 1, 'IMAGING', '70450', '头部CT平扫检查'),
('CT_HEAD_CE', '头部CT增强', '头部CT增强', 1, 'IMAGING', '70460', '头部CT增强检查'),
('CBC', '血常规检查', '血常规', 2, 'LAB', '85025', '全血细胞计数及分类'),
('BMP', '基础代谢检查', '生化全套', 2, 'LAB', '80047', '基础代谢检查组合'),
('APPENDECTOMY', '阑尾切除术', '阑尾切除', 3, 'SURGERY', '44970', '腹腔镜阑尾切除术'),
('CARDIO_CONSULT', '心内科会诊', '心内科会诊', 4, 'CONSULTATION', '99243', '门诊心内科会诊'),
('IV_START', '静脉输液', '静脉输液', 5, 'NURSING', '36000', '静脉输液操作'),
('MED_RECONCILE', '药物重整', '药物重整', 6, 'PHARMACY', '99605', '药物重整服务'),
('PT_EVAL', '物理治疗评估', 'PT评估', 7, 'REHABILITATION', '97161', '物理治疗评估');

-- 关联操作用途
INSERT INTO procedure_purposes (procedure_id, purpose_type, is_primary) VALUES
(1, 'ORDERABLE', TRUE), (1, 'PERFORMABLE', TRUE), (1, 'CHARGEABLE', TRUE),
(2, 'ORDERABLE', TRUE), (2, 'PERFORMABLE', TRUE), (2, 'CHARGEABLE', TRUE),
(3, 'ORDERABLE', TRUE), (3, 'PERFORMABLE', TRUE), (3, 'CHARGEABLE', TRUE),
(4, 'ORDERABLE', TRUE), (4, 'PERFORMABLE', TRUE), (4, 'CHARGEABLE', TRUE),
(5, 'ORDERABLE', TRUE), (5, 'PERFORMABLE', TRUE), (5, 'CHARGEABLE', TRUE),
(6, 'ORDERABLE', TRUE), (6, 'PERFORMABLE', TRUE), (6, 'CHARGEABLE', TRUE),
(7, 'ORDERABLE', TRUE), (7, 'PERFORMABLE', TRUE), (7, 'CHARGEABLE', TRUE),
(8, 'ORDERABLE', TRUE), (8, 'PERFORMABLE', TRUE), (8, 'CHARGEABLE', TRUE),
(9, 'ORDERABLE', TRUE), (9, 'PERFORMABLE', TRUE), (9, 'CHARGEABLE', TRUE);

-- 插入操作资源需求
INSERT INTO procedure_resources (procedure_id, resource_type, resource_name, quantity, preparation_time_minutes) VALUES
(1, 'EQUIPMENT', 'CT扫描仪', 1, 5),
(1, 'STAFF', '放射科技师', 1, 0),
(2, 'EQUIPMENT', 'CT扫描仪', 1, 10),
(2, 'SUPPLY', '造影剂', 1, 0),
(3, 'EQUIPMENT', '血液分析仪', 1, 0),
(3, 'STAFF', '检验技师', 1, 0),
(5, 'ROOM', '手术室', 1, 30),
(5, 'STAFF', '外科医师', 1, 15),
(5, 'STAFF', '麻醉医师', 1, 15);

-- 插入操作别名
INSERT INTO procedure_aliases (procedure_id, alias_name, alias_type, language_code, is_preferred) VALUES
(1, '头颅CT', 'COMMON', 'zh-CN', TRUE),
(1, 'Head CT', 'COMMON', 'en', FALSE),
(2, '增强CT', 'COMMON', 'zh-CN', TRUE),
(3, '血常规', 'ABBREVIATION', 'zh-CN', TRUE),
(4, '生化', 'ABBREVIATION', 'zh-CN', TRUE),
(5, '阑尾手术', 'COMMON', 'zh-CN', TRUE);

-- ===================================================================
-- 数据验证触发器
-- ===================================================================

-- 确保每个操作至少有一个主要用途
CREATE OR REPLACE FUNCTION ensure_primary_purpose()
RETURNS TRIGGER AS $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM procedure_purposes 
        WHERE procedure_id = NEW.id AND is_primary = TRUE
    ) THEN
        INSERT INTO procedure_purposes (procedure_id, purpose_type, is_primary)
        VALUES (NEW.id, 'ORDERABLE', TRUE);
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_ensure_primary_purpose
    AFTER INSERT ON procedures
    FOR EACH ROW
    EXECUTE FUNCTION ensure_primary_purpose();

-- 操作状态变更触发器
CREATE OR REPLACE FUNCTION log_procedure_status_change()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.order_status IS DISTINCT FROM OLD.order_status THEN
        INSERT INTO procedure_status_history (
            procedure_id, status, status_reason, created_by
        ) VALUES (
            NEW.id, NEW.order_status, '状态变更', NEW.updated_by
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_log_status_change
    AFTER UPDATE ON procedure_orders
    FOR EACH ROW
    EXECUTE FUNCTION log_procedure_status_change();

-- ===================================================================
-- 使用示例
-- ===================================================================

/*
-- 查询所有激活的影像检查
SELECT * FROM active_procedures WHERE order_type = 'IMAGING';

-- 查找需要特定设备的所有操作
SELECT p.proc_name, pr.resource_name, pr.quantity
FROM procedures p
JOIN procedure_resources pr ON p.id = pr.procedure_id
WHERE pr.resource_type = 'EQUIPMENT' 
  AND pr.resource_name ILIKE '%CT%';

-- 获取操作的所有别名
SELECT p.proc_name, array_agg(pa.alias_name) as aliases
FROM procedures p
LEFT JOIN procedure_aliases pa ON p.id = pa.procedure_id
WHERE p.proc_code = 'CT_HEAD'
GROUP BY p.proc_name;

-- 查询待执行医嘱
SELECT * FROM pending_orders WHERE patient_id = 12345;

-- 查询异常结果
SELECT po.order_number, p.proc_name, pr.result_value, pr.is_abnormal
FROM procedure_results pr
JOIN procedure_orders po ON pr.order_id = po.id
JOIN procedures p ON po.procedure_id = p.id
WHERE pr.is_abnormal = TRUE;
*/
