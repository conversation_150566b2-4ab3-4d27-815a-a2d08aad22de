// 就诊状态
export type VisitStatus = 'pending' | 'inProgress' | 'completed' | 'missed';

// 午别
export type Periods = 'AM' | 'PM';

export interface PatientRecord {
  operation: "结束检查" | "叫号" | "查看报告"
  queue_number: string
  patient_id: string
  visit_status: VisitStatus
  patient_name: string
  gender: "M" | "F" | "U"
  birth_date: string
  visit_type: string
  diagnosis: string
  fee_category: string
  medical_card_no: string
  id_card_no: string
  registration_date: string
  registering_doctor: string
  visit_period: Periods
  triage_time: string
  attending_doctor: string
  department: string,
  visit_status_template: string,
  patient_age: string
}