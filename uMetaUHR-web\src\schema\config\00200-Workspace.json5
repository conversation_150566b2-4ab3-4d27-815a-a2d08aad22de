{
  type: 'workspace',
  ui: {
    type: 'stack',
    tabPosition: 'top-navi',
    style: {
      vars: {
        themeColor: 'white',
        themeBC: '#364EAF',
        themeActiveBC: '#7481C8',
        themeActiveColor: 'white',
        themeContentBC: 'darkgrey'
      },
    },
    menu: [
      {
        id: 'patAppointment',
        icon: 'calendar',
        displayName: '预约'
      },
      {
        id: 'patList',
        displayName: '挂号患者',
        customIcon: {
          src: '/patient-admin.svg'
        }
      },
      {
        id: '就诊',
        displayName: '就诊（姓名）',
        icon: 'user'
      },
      {
        id: 'statistics',
        displayName: '统计报表',
        customIcon: {
          src: '/statistics.svg'
        }
      },
      {
        id: 'information-list',
        displayName: '消息列表',
        icon1: {
          src: '/information-list.svg'
        }
      },
      {
        id: 'SOP',
        displayName: 'SOP'
      },
      {
        type: 'toolbar',
        style: {
          background: 'none',
          margin: 0,
          padding: 0,
          color: 'white',
          'justify-content': 'right'
        },
        children: [
          {
            type: 'button',
            customIcon: '/upload.svg',
            style: {
              main: {
                background: 'none',
                border: 'none',
                color: 'white',
                'box-shadow': 'none',
                height: '80%',
                margin: '3px',
                width: '40px'
              },
              vars: {
                iconBtnSize: '30px'
              },
              event: {
                name: 'upload'
              }
            }
          },
          {
            type: 'button',
            customIcon: '/signal.svg',
            style: {
              main: {
                background: 'none',
                border: 'none',
                color: 'white',
                'box-shadow': 'none',
                height: '80%',
                margin: '3px',
                width: '40px'
              },
              vars: {
                iconBtnSize: '30px'
              },
              event: {
                name: 'upload'
              }
            }
          },
          {
            type: 'button',
            customIcon: '/notification.svg',
            style: {
              main: {
                background: 'none',
                border: 'none',
                color: 'white',
                'box-shadow': 'none',
                height: '80%',
                margin: '3px',
                width: '40px'
              },
              vars: {
                iconBtnSize: '30px'
              },
              event: {
                name: 'upload'
              }
            }
          },
          {
            type: 'button',
            customIcon: '/application.svg',
            style: {
              main: {
                background: 'none',
                border: 'none',
                color: 'white',
                'box-shadow': 'none',
                height: '80%',
                margin: '3px',
                width: '40px'
              },
              vars: {
                iconBtnSize: '30px'
              },
              event: {
                name: 'upload'
              }
            }
          },
          {
            type: 'button',
            customIcon: '/home.svg',
            style: {
              main: {
                background: 'none',
                border: 'none',
                color: 'white',
                'box-shadow': 'none',
                height: '80%',
                margin: '3px',
                width: '40px'
              },
              vars: {
                iconBtnSize: '30px'
              },
              event: {
                name: 'upload'
              }
            }
          }
        ]
      },
      {
        id: 'config',
        icon: 'cog',
        displayName: ''
      },
      {
        type: 'verticalDivider'
      },
      {
        type: 'button',
        customIcon: '/user-icon.svg',
        label: '成吉思汗',
        style: {
          main: {
            background: 'none',
            border: 'none',
            color: 'white'
          },
          vars: {
            iconBtnSize: '20px'
          },
          event: {
            name: 'upload'
          }
        },
        event: {
          name: 'quality-control'
        }
      },
      {
        type: 'button',
        customIcon: '/logo.svg',
        style: {
          main: {
            background: 'none',
            border: 'none'
          }
        }
      }
    ],
    children: {
      patList: {
        type: 'external',
        externalSchema: '01305-PatList'
      },
      patAppointment: {
        type: 'external',
        externalSchema: '01306-PatAppointment'
      },
      statistics: {
        type: 'external',
        externalSchema: '00300-MasterDataFile'
      },
      就诊: {
        type: 'external',
        externalSchema: '01307-PatWorkspace'
      },
      config: {
        type: 'external',
        externalSchema: '01002-SOP-2'
      },
      SOP: {
        type: 'external',
        externalSchema: '01000-SOP'
      },
      'information-list': {
        type: 'external',
        externalSchema: '00600-PatIntake'
      }
    },
    myFavoriteId: '00200-Workspace.stack-60',
    event: {
      messageEndPoint: 'patient-workspace'
    }
  },
  subContext: {
    path: '/patient',
    default: {}
  }
}
