<template>
  <a-radio-group :value="getNestedValue(context, props?.schema?.model)" v-bind="props?.schema?.attrs"
    @change="onChange">
    <template v-if="props?.schema?.radioType === 'button'">
      <a-radio-button :value="option.value" v-for="option in props?.schema?.options">
        {{ `${option.label}${props.schema.labelSuffixModel && props.schema.labelSuffixModel[option.value] ?
          getNestedValue(context, props.schema.labelSuffixModel[option.value]) : ''}` }}
      </a-radio-button>
    </template>
    <template v-else>
      <a-radio :value="option.value" v-for="option in props?.schema?.options">
        {{ `${option.label}${props.schema.labelSuffixModel && props.schema.labelSuffixModel[option.value] ?
          getNestedValue(context, props.schema.labelSuffixModel[option.value]) : ''}` }}
      </a-radio>
    </template>
  </a-radio-group>
</template>

<script lang="ts" setup>
import { defineProps, inject } from 'vue'
import { getNestedValue, setNestedValue } from '@/lib/getContext'

const props = defineProps<{
  schema: {
    attrs?: Record<string, any>,
    model: string,
    radioType?: string,
    options: Array<{
      label: string
      value: any
    }>,
    event?: {
      change?: string
    },
    labelSuffixModel?: Record<string, string>
  }
  context: { [key: string]: any }
}>()

const {
  schema: {
    event: {
      change: changeEventInject = "change-radio-event",
    } = {}
  } = {}
} = props

const changeEvent = inject(changeEventInject, () => 1) as Function

const onChange = (e: any) => {
  setNestedValue(props.context, props.schema?.model, e.target.value)
  changeEvent(e.target.value)
};
</script>

<style lang="scss" scoped></style>
