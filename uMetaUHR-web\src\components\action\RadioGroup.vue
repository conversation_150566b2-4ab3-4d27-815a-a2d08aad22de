<template>
  <a-radio-group
    :value="getNestedValue(context, props?.schema?.model)"
    v-bind="props?.schema?.attrs"
    @change="onChange"
  >
    <template v-if="props?.schema?.radioType === 'button'">
      <a-radio-button
        :value="option"
        v-for="(option, index) in props?.schema?.options"
        :key="`k2-${index}`"
      >
        {{ option.label }}
      </a-radio-button>
    </template>
    <template v-else>
      <a-radio
        :value="option"
        v-for="(option, index) in props?.schema?.options"
        :key="`k-1${index}`"
      >
        {{ option.label }}
      </a-radio>
    </template>
  </a-radio-group>
</template>

<script lang="ts" setup>
import { defineProps, inject } from 'vue'
import { getNestedValue, setNestedValue } from '@/lib/getContext'

const props = defineProps<{
  schema: {
    attrs?: Record<string, any>
    model: string
    radioType?: string
    options: Array<{
      label: string
      value: any
    }>
    event?: {
      change?: string
    }
  }
  context: { [key: string]: any }
}>()

const { schema: { event: { change: changeEventInject = 'change-radio-event' } = {} } = {} } = props

const changeEvent = inject(changeEventInject, () => {
  window.popAlert?.({ message: `function "${changeEventInject}" is not implemented.` })
}) as Function

const onChange = async (e: any) => {
  let { schema: { model = '' } = {} } = props || {}
  let { target: { value, value: { value: v2 } = {} as any } = {} as any } = e
  if (!v2) return
  changeEvent({ context: props.context, data: value })

  if (!model) return
  setNestedValue(props.context, model, v2)
}
</script>

<style lang="scss" scoped></style>
