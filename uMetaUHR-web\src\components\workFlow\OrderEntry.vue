<template>
  <div class="order-entry">
    <component
      :is="loadComponent(props.schema?.ui?.type)"
      v-if="props.schema"
      :context="getContext(props)"
      :schema="props.schema?.ui"
    />
  </div>
  <WnDialog ref="dialog" />
</template>

<script lang="ts" setup>
import { defineProps, provide, ref } from 'vue'
import { loadComponent } from '../componentRegistry'
import { getContext, getNestedValue } from '@/lib/getContext'
import WnDialog from '@/components/containers/WnDialog.vue'
import { dbGet, dbList, dbSave } from '@/lib/dataMan'
import { deepMerge } from '@/lib/copymerge'

const props = defineProps<{
  schema: any
  context: { [key: string]: any }
}>()

const dialog = ref<InstanceType<typeof WnDialog>>()

provide('query-condition', async (param: any = {}) => {
  let cat = 'meds'
  let docType = 'col1'
  const filter = cat ? { filter_by: `category:=${cat}` } : {}
  return {
    search: {
      docType,
      param: { query_by: 'name', typo_tokens_threshold: 0, max_candidates: 30, ...filter }
    }
  }
})

provide('list-records', async () => {
  let context = getContext(props)
  let encounterId = context.detail?.encounter?.id || ''
  if (!encounterId) return []

  let sql = `SELECT o.id,
                    o.order_code as code,
                    o.order_description as description,
                    o.order_date as date
             FROM "maxEMR".orders o
             WHERE o.encounter_id = ?::int
             ORDER BY o.order_date DESC`
  return (
    (await dbList({
      sql,
      param: [encounterId]
    })) || []
  )
})

provide('select-order', async (param = {}) => {
  let context = getContext(props)
  let { data: { id = '' } = {} } = param as any
  if (!id) return

  const order = await dbGet({
    sql: `SELECT *
          FROM "maxEMR".orders o
          WHERE o.id = ?::int`,
    param: [id]
  })

  context.detail.detail.order = order
  return order
})

provide('new-order', async (param = {}) => {
  let context = getContext(props)
  let { detail: { encounter: { id: encounterId = 0 } = {} } = {} } = context as any
  if (!encounterId) return

  let { getParentExposed } = param || ({} as any)
  let { newItem } = getParentExposed('toc')
  newItem({})

  context.detail.detail.order = {}
  return {}
})

provide('save-order', async (param = {}) => {
  let context = getContext(props)
  let order = context.detail?.detail?.order || {}
  let encounter = context.detail?.detail?.encounter || {}
  if (!encounter.id) {
    await window.popAlert({ message: 'encounter id is not set.' })
    return
  }
  order.encounter_id = encounter.id

  const conditionalColumns = order.id ? ['id'] : []
  const result = await dbSave({
    table: `orders`,
    data: [order],
    conflict_column: 'id',
    update_columns: [
      ...conditionalColumns,
      'encounter_id',
      'order_code',
      'order_description',
      'order_date',
      'status_c',
      'priority_c',
      'extensions'
    ]
  })

  if (result?.ids?.[0]?.id) {
    deepMerge(context, { detail: { order: { id: result.ids[0].id } } })
  }
  return result
})

provide('edit-order', async (para: object) => {
  let { getParentExposed, context } = para as any
  if (!getParentExposed || !context) return
  let schema = props.schema.schema['edit-order']
  getParentExposed('recordEntry')?.setRowSchema?.(schema, context.index)
})

provide('edit-order-closed', async (para: object) => {
  let { getParentExposed, context } = para as any
  if (!getParentExposed || !context) return
  let schema = props.schema.schema['edit-order']
  getParentExposed('recordEntry')?.setRowSchema?.(null, context.index)
})

provide('save-records', async (param: Record<string, unknown> = {}) => {
  let context = getContext(props)
  const orderList = getNestedValue(context, 'orderList')
  if (!orderList.length) return
})

defineExpose({ compName: 'orderEntry' })
</script>

<style lang="scss" scoped>
.order-entry {
  display: flex;
  flex: 1 1 auto;
  min-height: 5rem;
}
</style>
