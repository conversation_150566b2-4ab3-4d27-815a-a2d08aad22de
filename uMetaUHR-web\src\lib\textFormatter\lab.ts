import Handlebars from 'handlebars'
import { isEmpty } from '@/lib/util'

interface Name {
  name: string
  symbol: string
}

interface LabDataItem {
  指标代码: string
  指标名称: string
  检验结果: number
  结果单位: string
  参考值?: string
}

interface ResultItem {
  flag?: string
  name: Name
  unit: string
  value: number
}

interface Lab {
  data?: LabDataItem[]
  申请时间?: string
}

interface InputObject {
  name?: string
  date_time?: string
  data?: {
    lab?: Lab
    exam?: any
    result?: any
  }
}

function isNumber(value: any) {
  return !isNaN(parseFloat(value)) && isFinite(value)
}

function checkRef(value: any, ref: string) {
  const [, op, val] = ref.match(/([><=≦]+)[^\d]*([\d.]+)/) || ([] as any)
  if (+ref > 45000) return 0

  if (op) {
    if (['<', '≦', '<='].includes(op)) return +value < +val ? 0 : 1
    if (['>', '>='].includes(op)) return +value > +val ? 0 : 1
  }

  const [, low, high] = ref.match(/([\d.]+)[^\d]+([\d.]+)/) || ([] as any)
  if ([low, high, value].every(isNumber)) {
    if (+value < +low) return -1
    if (+value > +high) return 1
    return 0
  }
}

function labObjInHTML(obj: InputObject, format: string, displayMode: string): string {
  // eslint-disable-next-line prefer-const
  let { data: { exam, lab, result } = {} } = obj

  if (exam) {
    // return showObj(exam)
    const { 标本名称, 细胞学诊断, 检查所见, 检查结论 } = exam
    if ([标本名称, 细胞学诊断, 检查所见, 检查结论].every((x) => !x)) {
      return showObj(exam)
    }
    return ['检查所见', '检查结论']
      .filter((x) => !!exam[x])
      .map(
        (key: string) =>
          `<span style="color:darkblue; font-weight: bold">${key}</span>: ${exam[key]}<br>`
      )
      .join('')
  }

  if (lab?.data) {
    const { data: labData, 申请时间 } = lab
    if (!isEmpty(labData)) {
      result = labData
        .map(({ 指标代码, 指标名称, 检验结果, 结果单位, 参考值 = '' }) => {
          const flag = checkRef(检验结果, 参考值)
          if (displayMode != 'full' && flag === 0) return
          return {
            flag,
            ref: 参考值,
            name: { name: 指标名称, symbol: 指标代码 },
            unit: 结果单位,
            value: 检验结果
          }
        })
        .filter((x) => x)
    }
  }

  if (isEmpty(result)) return ''

  const flagSym = (n: number) => {
    if (n > 0) return '<sup style="font-weight: bolder;color: red">⬆</sup>'
    if (n < 0) return '<sup style="font-weight: bolder;color: blue">⬇</sup>'
    return ''
  }

  if (format === 'table') {
    return `
      <table style="width: 100%; text-align: center;">
        <thead>
          <tr>
            <th>指标代码</th>
            <th>指标名称</th>
            <th>检验结果</th>
            <th>结果单位</th>
            <th>参考值</th>
          </tr>
        </thead>
        <tbody>
          ${result!
            .map(
              ({ name, value, unit, flag, ref }: any) => `
            <tr>
              <td>${name.symbol}</td>
              <td>${name.name}</td>
              <td>${value} ${flagSym(flag)}</td>
              <td>${unit}</td>
              <td>${ref || ''}</td>
            </tr>
          `
            )
            .join('')}
        </tbody>
      </table>`
  }

  return `* ${result!
    .map(({ name, value, unit, flag, ref }: any) => `${name.name} ${value}${unit}${flagSym(flag)}`)
    .join('，')}`
}

const MAX_DEPTH = 5 // 最大递归深度限制

// 内部递归函数
function showObj(data: any, depth = 0): string {
  if (depth > MAX_DEPTH) {
    return `<span style="color: #999;">...（内容过深，已隐藏）</span>` // 超出深度限制
  }

  if (!data || typeof data !== 'object') {
    return `<span>${Handlebars.escapeExpression(data)}</span>` // 非对象直接返回
  }

  const entries = Object.entries(data)
  if (entries.length === 0) {
    return `<ul style="padding-left: ${depth * 20}px;"><li><em>空内容</em></li></ul>` // 空对象处理
  }

  // 遍历对象生成 HTML
  return `
      <ul style="padding-left: ${depth * 20}px; list-style: none; margin: 0;">
        ${entries
          .map(
            ([key, value]) => `
            <li style="margin: 5px 0;">
              <strong>${Handlebars.escapeExpression(key)}</strong>:
              ${typeof value === 'object' && value !== null ? showObj(value, depth + 1) : `<span>${Handlebars.escapeExpression(value as string)}</span>`}
            </li>`
          )
          .join('')}
      </ul>
    `
}

export { labObjInHTML, showObj }
