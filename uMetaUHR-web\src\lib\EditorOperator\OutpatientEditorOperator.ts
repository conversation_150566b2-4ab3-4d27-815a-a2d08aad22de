import { EMR_EDITOR_DEFAULT_OPTION } from '@/service/base/editorConfig';
import { BaseEditorOperator } from './BaseEditorOperator';

export class OutpatientEditorOperator extends BaseEditorOperator {
  async openRecord(recordId: string) {
    // 门诊专属打开逻辑
    if (this.editor) {
      await super.openRecord(recordId);
      await this.syncData('P001');
      await this.hideToolbarItems(EMR_EDITOR_DEFAULT_OPTION.outpatient.toolbarDisabled);
      await this.editor.setViewProportion(1);
    }
  }

  async saveRecord(data: any) {
    // 门诊专属保存逻辑
    if (this.editor && this.editor.saveOutpatientRecord) {
      return await this.editor.saveOutpatientRecord(data);
    }
    return super.saveRecord(data);
  }

  // 门诊：从外部同步数据到文档
  async syncData(patientId: string): Promise<{[name: string]: string}> {
    try {
      // 1. 获取编辑器的JSON配置
      const jsonString = await this.getEditorJsonConfig()
      if (!jsonString) {
        console.warn('[SyncData] 无法获取编辑器JSON配置')
        return {}
      }

      // 2. 解析JSON，提取name和对应的sourceKey
      const editorConfig = JSON.parse(jsonString)
      const syncItems: Array<{
        name: string
        sourceKey: string
        index: number
      }> = []

      let index = 0
      for (const [name, config] of Object.entries(editorConfig)) {
        const elementConfig = config as any
        // 检查是否有dynamicConfig.sourceKey
        if (elementConfig?.property?.dynamicConfig?.sourceKey) {
          syncItems.push({
            name,
            sourceKey: elementConfig.property.dynamicConfig.sourceKey,
            index: index++
          })
        }
      }

      if (syncItems.length === 0) {
        console.log('[SyncData] 没有找到需要同步的数据元素')
        return {}
      }

      // 3. 加载数据元映射表
      const mappingTable = await this.loadMappingTable()

      // 4. 构建批量查询请求
      const queryItems = syncItems.map(item => {
        const mapping = mappingTable[item.sourceKey]
        if (!mapping) {
          console.warn(`[SyncData] 未找到sourceKey的映射: ${item.sourceKey}`)
          return null
        }

        return {
          index: item.index,
          name: item.name,
          sourceKey: item.sourceKey,
          table: mapping.table,
          column: mapping.column,
          condition: `pat_id = '${patientId}'` // 根据实际的患者ID字段名调整
        }
      }).filter(item => item !== null)

      if (queryItems.length === 0) {
        console.warn('[SyncData] 没有有效的查询项')
        return {}
      }

      // 5. 调用批量查询接口
      const { EditorDataApis } = await import('@/service/editor/api')
      const response = await EditorDataApis.batchSyncData({
        items: queryItems,
        patientId
      })

      // 6. 组装valueMap
      const valueMap: {[name: string]: string} = {}

      if (response.success && response.items) {
        response.items.forEach(result => {
          if (result.success) {
            valueMap[result.name] = result.text
          } else {
            console.warn(`[SyncData] 查询失败 ${result.name}: ${result.error}`)
            valueMap[result.name] = '' // 失败时设置为空字符串
          }
        })
      }

      console.log(`[SyncData] 同步完成，共处理 ${Object.keys(valueMap).length} 个字段`)
      return valueMap

    } catch (error) {
      console.error('[SyncData] 数据同步失败:', error)
      return {}
    }
  }

  /**
   * 获取编辑器JSON配置
   */
  private async getEditorJsonConfig(): Promise<string | null> {
    try {
      // 使用编辑器的getStructsXmlInfoByParament2方法获取结构化信息
      if (this.editor && typeof this.editor.getStructsXmlInfoByParament2 === 'function') {
        return await this.editor.getStructsXmlInfoByParament2()
      }
      return null
    } catch (error) {
      console.error('[SyncData] 获取编辑器配置失败:', error)
      return null
    }
  }

  /**
   * 加载数据元映射表
   */
  private async loadMappingTable(): Promise<{[sourceKey: string]: {table: string, column: string, description?: string}}> {
    try {
      const mappingModule = await import('./数据元映射表.json')
      const mappingTable = mappingModule.default || mappingModule

      // 过滤掉元数据字段
      const filteredMapping: any = {}
      for (const [key, value] of Object.entries(mappingTable)) {
        if (!key.startsWith('_')) {
          filteredMapping[key] = value
        }
      }

      return filteredMapping
    } catch (error) {
      console.error('[SyncData] 加载映射表失败:', error)
      return {}
    }
  }
}