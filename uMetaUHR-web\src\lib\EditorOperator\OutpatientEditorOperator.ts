import { EMR_EDITOR_DEFAULT_OPTION } from '@/service/base/editorConfig';
import { BaseEditorOperator } from './BaseEditorOperator';

export class OutpatientEditorOperator extends BaseEditorOperator {
  async openRecord(recordId: string) {
    // 门诊专属打开逻辑
    if (this.editor) {
      await super.openRecord(recordId);
      await this.syncData('P001');
      await this.hideToolbarItems(EMR_EDITOR_DEFAULT_OPTION.outpatient.toolbarDisabled);
      await this.editor.setViewProportion(1);
    }
  }

  async saveRecord(data: any) {
    // 门诊专属保存逻辑
    if (this.editor && this.editor.saveOutpatientRecord) {
      return await this.editor.saveOutpatientRecord(data);
    }
    return super.saveRecord(data);
  }

  // 门诊：从外部同步数据到文档
  async syncData(patientId: string) {
    if (this.editor) {
      //需要patientId： P001
      // 获取结构化数据
      const jsonString = await this.editor.getStructsXmlInfoByParament2();
      const jsonObject = JSON.parse(jsonString);
      // jsonObject结构为 { elementID: { ... } }

      // 遍历所有结构，筛选出有dynamicConfig.sourceKey的元素，并收集elementID
      const data: any[] = [];
      let dataMapping: any = {};
      try {
          dataMapping = dataMapping.default || dataMapping;
      } catch (e) {
          console.error('无法加载数据映射配置:', e);
          return;
      }
      for (const elementID in jsonObject) {
          const struct = jsonObject[elementID];
          const customProp = struct.property && struct.property.CustomProperty;
          if (customProp && customProp.dynamicConfig) {
              let dynamicConfig = customProp.dynamicConfig;
              // dynamicConfig 可能是字符串，需要解析
              if (typeof dynamicConfig === 'string') {
                  try {
                      dynamicConfig = JSON.parse(dynamicConfig);
                  } catch { /* empty */ }
              }
              if (dynamicConfig && dynamicConfig.sourceKey) {
                  const sourceKey = dynamicConfig.sourceKey;
                  const mapping = dataMapping[sourceKey] || {};
                  // 合并params，始终加上patientId
                  const params = { ...(mapping.params || {}), patientId: patientId };
                  data.push({
                      elementID,
                      sourceKey,
                      params
                  });
              }
          }
      }
      if (data.length === 0) {
          console.warn('没有需要批量请求的数据项');
          return;
      }

      // 4. 发送批量请求
      try {
          const serverUrl = EMR_EDITOR_DEFAULT_OPTION.EMR_EDITOR_DEFAULT_DATA_SERVER_URL;
          const response = await fetch(`${serverUrl}/api/emr/batchValue`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ data })
          });
          const result = await response.json();
          // 5. 解析返回的 results 数组，组装成 接口要求的 格式
          if (result.success && Array.isArray(result.results)) {
              const valueMap: Record<string, any> = {};
              result.results.forEach((item: any) => {
                  if (item.value !== null && item.value !== undefined) {
                      valueMap[item.elementId] = { content_text: String(item.value) };
                  }
              });
              // 一次性批量回填
              await this.editor.setStructsTextByArray(JSON.stringify(valueMap));
          } else {
              console.error('批量请求返回格式异常:', result);
          }
      } catch (e) {
          console.error('批量请求失败:', e);
      }
  }
  }
}