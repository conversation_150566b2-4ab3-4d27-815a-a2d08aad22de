import { EMR_EDITOR_DEFAULT_OPTION } from '@/service/base/editorConfig';
import { BaseEditorOperator } from './BaseEditorOperator';

export class OutpatientEditorOperator extends BaseEditorOperator {
  async openRecord(recordId: string) {
    // 门诊专属打开逻辑
    if (this.editor) {
      await super.openRecord(recordId);
      await this.syncData('P001');
      await this.hideToolbarItems(EMR_EDITOR_DEFAULT_OPTION.outpatient.toolbarDisabled);
      await this.editor.setViewProportion(1);
    }
  }

  async saveRecord(data: any) {
    // 门诊专属保存逻辑
    if (this.editor && this.editor.saveOutpatientRecord) {
      return await this.editor.saveOutpatientRecord(data);
    }
    return super.saveRecord(data);
  }

  // 门诊：从外部同步数据到文档
  async syncData(patientId: string): Promise<{[name: string]: {content_text: string}}> {
    try {
      // 获取编辑器的结构化JSON信息,后期可以从数据库中直接获取
      const jsonString = await this.getEditorJsonConfig()
      if (!jsonString) {
        return {}
      }

      // 解析JSON，提取name和对应的sourceKey
      const editorConfig = JSON.parse(jsonString)
      const syncItems: Array<{
        name: string
        sourceKey: string
        index: number
      }> = []

      let index = 0
      for (const [name, config] of Object.entries(editorConfig)) {
        const elementConfig = config as any
        // 检查是否有dynamicConfig.sourceKey
        if (elementConfig?.property?.dynamicConfig?.sourceKey) {
          syncItems.push({
            name,
            sourceKey: elementConfig.property.dynamicConfig.sourceKey,
            index: index++
          })
        }
      }

      if (syncItems.length === 0) {
        return {}
      }

      // 加载数据元映射表
      const mappingTable = await this.loadMappingTable()

      // 构建批量查询请求
      const queryItems = syncItems.map(item => {
        const mapping = mappingTable[item.sourceKey]
        if (!mapping) {
          return null
        }

        return {
          index: item.index,
          name: item.name,
          sourceKey: item.sourceKey,
          table: mapping.table,
          column: mapping.column,
          condition: `pat_id = '${patientId}'` // 根据实际的患者ID字段名调整
        }
      }).filter(item => item !== null)

      if (queryItems.length === 0) {
        return {}
      }

      // 调用批量查询接口
      const { EditorDataApis } = await import('@/service/editor/api')
      const response = await EditorDataApis.batchSyncData({
        items: queryItems,
        patientId
      })

      // 组装valueMap setStructsTextByArray 接口要求
      const valueMap: {[name: string]: {content_text: string}} = {}

      if (response.success && response.items) {
        response.items.forEach(result => {
          if (result.success) {
            valueMap[result.name] = { content_text: result.text }
          } else {
            valueMap[result.name] = { content_text: '' } // 失败时设置为空字符串
          }
        })
      }

      // 调用编辑器API批量回填数据
      if (Object.keys(valueMap).length > 0 && this.editor && typeof this.editor.setStructsTextByArray === 'function') {
        await this.editor.setStructsTextByArray(JSON.stringify(valueMap))
      }

      return valueMap

    } catch (error) {
      return {}
    }
  }

  /**
   * 获取编辑器JSON配置
   */
  private async getEditorJsonConfig(): Promise<string | null> {
    try {
      if (this.editor && typeof this.editor.getStructsXmlInfoByParament2 === 'function') {
        return await this.editor.getStructsXmlInfoByParament2()
      }
      return null
    } catch (error) {
      return null
    }
  }

  /**
   * 加载数据元映射表
   */
  private async loadMappingTable(): Promise<{[sourceKey: string]: {table: string, column: string, description?: string}}> {
    try {
      const mappingModule = await import('./数据元映射表.json')
      const mappingTable = mappingModule.default || mappingModule

      // 过滤掉元数据字段
      const filteredMapping: any = {}
      for (const [key, value] of Object.entries(mappingTable)) {
        if (!key.startsWith('_')) {
          filteredMapping[key] = value
        }
      }

      return filteredMapping
    } catch (error) {
      return {}
    }
  }
}