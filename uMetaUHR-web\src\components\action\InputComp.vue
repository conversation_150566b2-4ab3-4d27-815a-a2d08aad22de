<template>
  <div v-if="isVisible" class="input-text">
      <!-- <span class="label">
        {{ props?.schema?.label }}
        <span v-if="props?.schema?.valid?.required" class="required">*</span>
      </span> -->
    <a-input
      :class="{ 'input-error': error }"
      :type="props?.schema?.inputType ?? 'text'"
      :model-value="getNestedValue(context, props?.schema?.model)"
      v-bind="props?.schema?.attrs"
      @blur="handleInput($event)"
      @keyup.enter.native="handleEnter"
    >
      <template 
        v-for="slot in props?.schema?.slots" 
        :key="slot.name"
        #[slot.name]
      >
        <template v-if="typeof slot.content === 'string'">
          {{ slot.content }}
        </template>
        <template v-else-if="slot.content?.component">
          <component
            :is="slot?.content?.component"
            v-bind="slot.attrs"
            :context="getContext(props)"
            :schema="slot?.content"
          />
        </template>
        <template v-else>
          <component
            :is="loadComponent(slot?.content?.type)"
            :context="getContext(props)"
            :schema="slot?.content"
          />
        </template>
      </template>
    </a-input>
    <span v-if="error" :title="error" class="error"> * </span>
  </div>
</template>

<script lang="ts" setup>
import { inject, computed, defineProps, watchEffect } from 'vue'
import useValidation from '../mixins/validationMixin'
import { loadComponent } from '../componentRegistry'
import { getContext } from '@/lib/getContext'
import { evaluateShowWhen, getNestedValue, setNestedValue } from '@/lib/getContext'

const props = defineProps<{
  label?: string
  schema: {
    type: string
    label?: string
    model: string
    inputType?: string
    dataType?: string
    showWhen?: string
    valid?: {
      required?: boolean
    },
    event?: {
      enterEvent?: string
    }
    slots?: Array<{
      name: 'prepend' | 'append' | 'prefix' | 'suffix'
      content: string
    }>
    attrs?: Record<string, any>
  }
  context: { [key: string]: any }
}>()

const {
  schema: {
    event: {
      enterEvent: enterEventInject = "enter-event-func",
    } = {}
  } = {}
} = props

const enterEvent = inject(enterEventInject, () => 1) as Function

const { error, validate } = useValidation()

function handleInput(event: any) {
  let value = event?.target?.value
  if (value === undefined || value === null) return
  if (props?.schema?.dataType === 'n') value = +value
  setNestedValue(props.context, props.schema?.model, value)
}

function handleEnter(event: any) {
  handleInput(event)
  enterEvent(props)
}

const isVisible = computed(() => evaluateShowWhen(props))

watchEffect(() => {
  validate(getNestedValue(props.context, props.schema?.model), {
    required: props.schema?.valid?.required || false
  })
})
defineExpose({ compName: props?.schema?.type || '-' })
</script>

<style lang="scss" scoped>
.input-text {
  display: flex;
  flex-wrap: nowrap;
  margin: 2px;

  label {
    display: flex;
    flex-wrap: nowrap;
    flex: 1 1 auto;

    span {
      min-width: 5rem;
      white-space: nowrap;
      text-align: right;
      padding-right: 0.5rem;

      &.required {
        color: red;
      }
    }

    input {
      width: 100%;
      min-width: 5rem;
      border-radius: 0.2rem;
      border: 1px solid #ccc;
    }
  }
}
</style>
