<template>
  <div :class="`${theme} field-w-toc`" :style="schema?.style?.main">
    <div v-if="schema?.toolbar" :style="schema?.style?.toolbar" class="toolbar">
      <component
        :is="loadComponent(child?.type)"
        v-for="(child, index) in schema?.toolbar"
        :key="index"
        :context="getContext(props)"
        :schema="child"
      />
    </div>
    <div v-if="schema!.head" class="field-w-toc-head">
      <component
        :is="loadComponent(child?.type)"
        v-for="(child, index) in schema!.head || []"
        :key="`panel-child-${index}`"
        :context="getContext(props)"
        :schema="child"
      />
    </div>
    <div class="field-w-toc-body">
      <table key="tocTableKey" ref="tocTable">
        <thead>
          <tr class="command-row">
            <template
              v-for="(col, colIndex) in state?.schema?.columnDef"
              :key="`hd-col-${colIndex}-${sortEventKey}`"
            >
              <th
                :class="{ 'command-anchor2': col?.isCmd }"
                @click="handleSort($event as PointerEvent, col, colIndex)"
              >
                <div :class="{ 'command-toolbar': col?.isCmd }" :style="col?.style">
                  <div>
                    {{ col.displayName || col.field }}
                    <sup>{{ getSortMarker(colIndex) || '' }}</sup>
                  </div>
                </div>
                <div class="head-vertical-bar"></div>
              </th>
            </template>
          </tr>
        </thead>
        <tbody :key="`${tocKey}`" ref="sortableTable">
          <template v-for="(row, rowIndex) in state?.data" :key="`tr-${rowIndex}`">
            <tr
              :class="`fw-${state.selectedLine === rowIndex ? 'selectedLine' : 'normal-items'}`"
              :data-id="rowIndex"
              tabindex="-1"
              @click="selectLine($event, rowIndex, true)"
              @dblclick="openItem($event, rowIndex)"
              class="command-row"
            >
              <td
                v-for="(col, colIndex) in state?.schema?.columnDef"
                :key="`col-${colIndex}-`"
                :style="col?.style"
                :class="{ 'command-anchor': col?.isCmd }"
              >
                <template v-if="col?.comp">
                  <div class="command-toolbar">
                    <div>
                      <component
                        :is="loadComponent(col.comp)"
                        :context="{
                          ...getContext(props),
                          selectedLine: rowIndex,
                          record: state.data?.[rowIndex]
                        }"
                        :schema="col?.comp"
                      />
                    </div>
                  </div>
                </template>
                <template v-else>
                  {{ lineName(row, col, colIndex) || '&nbsp;' }}
                </template>
              </td>
            </tr>
          </template>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  getCurrentInstance,
  inject,
  nextTick,
  onMounted,
  onUnmounted,
  reactive,
  ref,
  watch
} from 'vue'
import { JSONPath } from 'jsonpath-plus'
import Sortable from 'sortablejs'
import { Debouncer } from '@/lib/Debouncer'
import { loadComponent } from '@/components/componentRegistry'
import { getContext, getNestedValue } from '@/lib/getContext'
import { cacheManager } from '@/lib/CacheManager'
import { isEmpty } from '@/lib/util'
import type { CommandFlow } from '@/components/action/commandFlow/CommandFlow'
import type { CommandManager } from '@/components/action/commandFlow/CommandFlowManager'
import ServerTime from '@/lib/ServerTime'
import { useEventBus } from '@/components/mixins/eventBus'

interface ColumnDef {
  field: string
  displayName?: string
  format?: string
  style?: Record<string, string>
  concept?: string
  dataType?: string
  model?: string
  type?: string
  comp?: any // For component rendering in cells
}

interface Schema {
  style?: {
    main?: Record<string, string>
    toolbar?: Record<string, string>
  }
  toolbar?: any
  columnDef?: ColumnDef[]
  myFavoriteId?: string
  selectedLineModel?: string
  naviWithKey?: string
  model?: string
  head?: any[]
  event?: {
    selectItem?: string
    listItems?: string
    shouldRefreshItems?: string
    openItem?: string
    messageEndPoint?: string
    tocCommandSelectTopic?: string
  }
}

interface Props {
  schema?: Schema
  context?: any
  theme?: string
  toolbarSeq?: number
  toolbar?: any
  columnDef?: ColumnDef[]
  data?: any[]
  selectedLine?: number
  filterFun?: Function
}

const props = defineProps<Props>()

const state = reactive({
  schema: props.schema || {},
  data: props.data || [],
  columnDef: props.columnDef || [],
  selectedLine: props.selectedLine || -1,
  toolbar: props.toolbar || {},
  theme: props.theme || 'table-detail',
  toolbarSeq: props.toolbarSeq || 0,
  filterFun: props.filterFun || null
})

const tocTable = ref<HTMLElement | null>(null)
const sortableTable = ref<HTMLElement>({} as HTMLElement)

const {
  schema: {
    event: {
      selectItem: eventSelectItem = 'toc-select-line',
      listItems: eventListItem = 'toc-list-data',
      shouldRefreshItems: eventRefreshItems = 'toc-should-refresh',
      openItem: eventOpenItem = 'toc-open-line',
      tocCommandSelectTopic = 'command-select-topic'
    } = {}
  } = {}
} = props

const tocListItem = inject(eventListItem, () => 1) as Function
const tocTableKey = ref(1)
const sortEventKey = ref(1)
const tocKey = ref(1)
let sortOrder: number[] = []
let sortInfo: Record<number, string> = {} // Store only direction

const getSortMarker = (colIndex: number): string => {
  if (!sortInfo || !sortOrder) return ''
  const direction = sortInfo[colIndex]
  const order = sortOrder.indexOf(colIndex) + 1
  const directionSymbol = { u: '▲', d: '▼' }[direction] || ''
  return directionSymbol && order ? `${directionSymbol}${order}` : ''
}

watch(
  () => getNestedValue(props.context, props.schema?.model || ''),
  async (newValue, oldValue) => {
    if (newValue === oldValue) return
    state.data = newValue
  }
)

watch(
  () => state.data,
  async (newValue, oldValue) => {
    if (!newValue || isEmpty(newValue)) return
    const favID = props.schema?.myFavoriteId
    let selectedLine = 0
    if (favID) {
      const favData = (await cacheManager.get(favID)) || {}
      selectedLine = favData.selectedLine
      if (selectedLine >= state.data.length) {
        selectedLine = state.data.length - 1
      }
      sortOrder = favData.sortOrder || []
      sortInfo = favData.sortInfo || {}
    }
    await sortTable()
    await selectLine(null, selectedLine || 0)
    scrollToSelectedLine()
    tocTableKey.value++
  }
)

async function saveFav(sortOrder: any, sortInfo: any) {
  const favID = props.schema?.myFavoriteId
  if (!favID) return
  const favData = (await cacheManager.get(favID)) || {}
  favData.sortOrder = sortOrder
  favData.sortInfo = sortInfo
  await cacheManager.set(favID, favData)
}

async function handleSort(event: PointerEvent, __: ColumnDef, colIndex: number) {
  if ((event.metaKey || event.ctrlKey) && event.shiftKey) {
    sortOrder = []
    sortInfo = {}
    await sortTable()
    await saveFav(sortOrder, sortInfo)
    tocTableKey.value++
    sortEventKey.value++
    return
  }

  sortInfo[colIndex] = sortInfo[colIndex] === 'u' ? 'd' : 'u'
  sortOrder = [colIndex, ...sortOrder.filter((x) => x !== colIndex)]
  if (sortOrder.length > 5) delete sortInfo[sortOrder.pop()!]
  const selectedLine = state.selectedLine
  const indexMap = await sortTable()
  await saveFav(sortOrder, sortInfo)
  await selectLine(null, indexMap?.[selectedLine] || 0)
  scrollToSelectedLine()
}

const originalIndexSymbol = Symbol('__originalIndex')

const sortTable = async (): Promise<Record<number, number>> => {
  const data = state.data || []
  if (!Array.isArray(data) || data.length === 0) return {}

  data.forEach((row: any, index) => {
    row[originalIndexSymbol] = index
  })

  const keys = props.schema?.columnDef || []
  if (!Array.isArray(sortOrder) || sortOrder.length === 0) return {}

  sortOrder
    .slice()
    .reverse()
    .forEach((i) => {
      const key = keys[i]?.field
      const direction = sortInfo[i]

      if (!key || !direction) return

      data.sort((a, b) => {
        const aValue = getNestedValue(a, key)
        const bValue = getNestedValue(b, key)

        if (aValue == null && bValue == null) return 0
        if (aValue == null) return direction === 'u' ? -1 : 1
        if (bValue == null) return direction === 'u' ? 1 : -1

        const aComparable = isFinite(aValue) ? +aValue : aValue
        const bComparable = isFinite(bValue) ? +bValue : bValue

        if (aComparable < bComparable) return direction === 'u' ? -1 : 1
        if (aComparable > bComparable) return direction === 'u' ? 1 : -1
        return 0
      })
    })

  const indexMap: Record<number, number> = {}
  data.forEach((row: any, newIndex) => {
    indexMap[row[originalIndexSymbol]] = newIndex
    delete row[originalIndexSymbol]
  })

  state.data = data
  tocKey.value++
  sortEventKey.value++

  return indexMap
}

const commandManager = inject<CommandManager>('commandManager')

const comp = getCurrentInstance()
const eventBus = useEventBus()
const callBackWithContext = (data: any) => {
  let { cb } = data || {}
  if (!cb) return
  cb({ comp: comp?.exposed || {} })
}

onMounted(async () => {
  state.data = await tocListItem({ queryParam: state.schema || {} } || {})

  if (sortableTable.value) {
    Sortable.create(sortableTable.value, {
      animation: 150,
      onEnd: (event: any) => {
        const parent = event.item.parentNode
        if (event.oldIndex < event.newIndex) {
          parent.insertBefore(event.item, parent.children[event.oldIndex])
        } else {
          parent.insertBefore(event.item, parent.children[event.oldIndex + 1])
        }

        const movedItem = state.data.splice(event.oldIndex, 1)[0]
        state.data.splice(event.newIndex, 0, movedItem)

        if (+state.selectedLine === +event.oldIndex) {
          selectLine(null, event.newIndex)
        }
      },
      ghostClass: '',
      chosenClass: ''
    })
  }

  tocTable.value?.addEventListener('keydown', handleKeydown)

  commandManager?.on(tocCommandSelectTopic, async (flow: CommandFlow) => {
    let line = (await flow.request('item_to_select', state.data)) || 0
    if (line < 0) line = 0
    if (line >= state.data.length) line = state.data.length - 1
    await selectLine(null, line)
    scrollToSelectedLine()
  })

  if (props.schema?.event?.messageEndPoint) {
    eventBus.on(props.schema?.event?.messageEndPoint, callBackWithContext)
  }
})

onUnmounted(() => {
  tocTable.value?.removeEventListener('keydown', handleKeydown)
  if (props.schema?.event?.messageEndPoint) {
    eventBus.off(props.schema?.event?.messageEndPoint, callBackWithContext)
  }
})

const tocRefreshItems = inject(eventRefreshItems, () => 1) as Function

watch(
  async () => {
    const result = await tocRefreshItems(getContext(props), props?.schema?.model)
    return result
  },
  async (newValue) => {
    if (newValue === undefined) return
    state.data = await tocListItem({ queryParam: state?.schema || {} } || {})
    await selectLine(null, 0)
  }
)

function lineName(data: object, colDef: ColumnDef, idx: number): string {
  const { field: path, format } = colDef
  const [value = ''] = JSONPath({ path: `$.${path}`, json: data }) || []
  if (format === 'tms') {
    return ServerTime.formatTms(value)
  }
  return value
}

const tocSelectItemByEvent = inject(eventSelectItem, () => 1) as Function

const doTocSelectItemByEvent = new Debouncer(50).debounce(async (selectedLine: number) =>
  tocSelectItemByEvent(
    {
      selectedLine: selectedLine,
      data: state.data?.[selectedLine]
    },
    props
  )
)

const tocOpenItemByEvent = inject(eventOpenItem, () => 1) as Function

async function openItem(event: MouseEvent | null, rowIndex: number) {
  event?.stopPropagation?.()
  event?.preventDefault?.()

  if (rowIndex < 0) rowIndex = 0
  if (rowIndex >= state.data.length) rowIndex = state.data.length - 1

  await tocOpenItemByEvent(
    {
      selectedLine: rowIndex,
      data: state.data?.[rowIndex]
    },
    props
  )
}

async function selectLine(event: MouseEvent | null, rowIndex: number, saveAsFav = false) {
  event?.stopPropagation?.()
  event?.preventDefault?.()

  const { schema = {}, context: { locModel = {} } = {} } = props

  if (rowIndex < 0) rowIndex = 0
  if (rowIndex >= state.data.length) rowIndex = state.data.length - 1

  state.selectedLine = rowIndex

  const favID = schema.myFavoriteId
  if (saveAsFav && favID) {
    const favData = (await cacheManager.get(favID)) || {}
    favData.selectedLine = rowIndex
    await cacheManager.set(favID, favData)
  }

  if (schema.selectedLineModel) {
    locModel[schema.selectedLineModel] = { data: state.data[state.selectedLine] }
  }

  await doTocSelectItemByEvent(state.selectedLine)
}

const handleKeydown = (event: KeyboardEvent) => {
  if (!props?.schema || props.schema.naviWithKey === 'no') return

  const delta = event.key === 'ArrowUp' ? -1 : event.key === 'ArrowDown' ? 1 : 0
  if (delta === 0) return

  event.preventDefault()
  event.stopPropagation()
  selectLine(null, state.selectedLine + delta, true)
  scrollToSelectedLine()
}

const nextItem = async () => {
  if (state.selectedLine + 1 >= state.data.length) return null
  await selectLine(null, state.selectedLine + 1, true)
  return state.data?.[state.selectedLine] || {}
}

const scrollToSelectedLine = () => {
  nextTick(() => {
    try {
      if (!props?.schema || props.schema.naviWithKey === 'no') return

      const selectedRow = tocTable.value?.querySelector(`tr:nth-child(${state.selectedLine + 1})`)
      if (selectedRow === undefined) return //selectRow can be 0

      let parentContainer = tocTable.value
      while (parentContainer && parentContainer.tagName !== 'DIV') {
        parentContainer = parentContainer.parentElement
      }

      if (!selectedRow || !parentContainer) return

      const rowRect = selectedRow.getBoundingClientRect()
      const parentRect = parentContainer.getBoundingClientRect()

      const isVisible = rowRect.top >= parentRect.top + 20 && rowRect.bottom <= parentRect.bottom

      if (isVisible) return

      selectedRow.scrollIntoView({ behavior: 'smooth', block: 'center' })
    } catch (e) {
      console.error('Error scrolling to selected line:', e)
    }
  })
}

const newItem = (item = {} as any) => {
  state.data.push(item)
  state.selectedLine = state.data.length - 1
}

defineExpose({ compName: 'toc', tocState: state, newItem, nextItem })
</script>

<style lang="scss" scoped>
.field-w-toc {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  overflow: auto;

  .field-w-toc-body {
    overflow: auto;
    flex: 1 1 auto;
    position: relative;
    border: 2px solid red;

    table {
      border-collapse: collapse;
      width: 100%;
      overflow: auto;
      //table-layout: fixed;

      tr:nth-child(odd) {
        td {
          background: #fafcfeff;
        }
      }

      tr:nth-child(even) {
        td {
          background: #eef3fcff;
        }
      }

      tr.fw-selectedLine {
        border: 1px solid blue;

        td {
          background: #e8eeffff;
        }
      }

      tr {
        height: 1em;
        //outline: none;
        cursor: pointer;
      }

      th {
        flex-direction: row;
        top: 0;
        position: sticky;
        z-index: 3;
        background: white;
      }

      td {
        border: none; //1px solid transparent;
      }

      .command-anchor,
      .command-anchor2 {
        //display: none;
        height: 1px;
        padding: 0 !important;
        overflow: hidden;
        z-index: 10;
      }

      .command-anchor2 {
        z-index: 6;
      }

      .command-toolbar {
        position: sticky; //use sticky style.
        right: 0; // stick to right border of the toc body.
        width: 0;
        overflow: visible;

        > div {
          position: absolute;
          right: 0;

          > div {
            background: white;
            flex: 0 0 auto;
            overflow: hidden;
            align-items: center;
            margin-right: 2px;
            box-shadow: 0 0 5px grey;
            gap: 4px;
          }
        }
      }

      .command-row:hover {
        .command-anchor {
          display: unset;
        }
      }

      th,
      td {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding: 2px 5px;
      }
    }
  }
}

//.field-w-toc-toolbar,
//.toolbar {
//  flex: 0 !important;
//}

//.table-detail.field-w-toc {
//  display: flex;
//  flex-direction: column;
//  flex: 1 1 auto;
//  overflow: hidden;
//  padding: 1px;
//
//  .field-w-toc-body {
//    display: block;
//    overflow: auto;
//    position: relative;
//    flex: 1 1 auto;
//
//    //th {
//    //  top: 0;
//      //position: sticky;
//      //z-index: 1;
//    //}
//  }
//}

.head-vertical-bar {
  position: absolute;
  top: 5px;
  bottom: 5px;
  right: 0;
  border-right: 3px solid lightgrey;
}
</style>
