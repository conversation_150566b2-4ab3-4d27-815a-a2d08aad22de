<template>
  <div
    v-if="isVisible"
    :style="props.schema?.style?.main as Record<string, string>"
    class="text-display"
  >
    <label v-if="props.schema?.label">
      <span class="label">{{ props.schema.label }}</span>
    </label>
    <div v-if="renderedText" v-html="renderedText" />
    <component
      :is="currentComponent"
      :contenteditable="props.schema?.contentEditable"
    >
    </component>
  </div>
</template>

<script lang="ts" setup>
import { computed, defineProps, inject, onMounted, ref, shallowRef, watchEffect } from 'vue'
import { evaluateShowWhen, getNestedValue } from '@/lib/getContext'
import { loadComponent } from '@/components/componentRegistry'
import { asyncHandlebar, compileTemplate2 } from '@/components/action/TextProcessors/ext_handlebars'
import { getFile } from '@/lib/aiLib/ai'
import { registerHandlebarsHelpers } from '@/lib/handlebars-helpers'

const currentComponent = shallowRef()

registerHandlebarsHelpers(asyncHandlebar)

interface Schema {
  template?: string
  templateFile?: {
    type: string
    name: string
  }
  model?: string
  label?: string
  style?: {
    main: Record<string, string>
  }
  event?: {
    loadTemplate?: string
    waitForData?: string
  }
  showWhen?: Record<string, any>
  contentEditable?: boolean
}

declare global {
  interface Window {
    templateCache: Map<string, string>
  }
}

interface Props {
  label?: string
  model?: string
  schema: Schema
  context: Record<string, any>
}

const props = defineProps<Props>()

// 计算组件是否可见
const isVisible = computed(() => evaluateShowWhen(props))

// 获取 loadTemplate 函数（如果存在）
const loadTemplateCmd = props.schema?.event?.loadTemplate || ''
const loadTemplate = loadTemplateCmd && inject<Function>(loadTemplateCmd)
const waitForDataCmd = props.schema?.event?.waitForData || ''
const waitForData = waitForDataCmd && inject<Function>(waitForDataCmd)

// 定义响应式变量以存储渲染后的文本
const renderedText = ref('')

// Initialize cache if not exists
if (!window.templateCache) {
  window.templateCache = new Map<string, string>()
}

// Load template if schema.templateFile is defined
onMounted(async () => {
  if (props.schema?.templateFile?.type && props.schema?.templateFile?.name) {
    const cacheKey = `${props.schema.templateFile.type}:${props.schema.templateFile.name}`

    if (!window.templateCache.has(cacheKey)) {
      try {
        let template =
          (await getFile(props.schema.templateFile.type, props.schema.templateFile.name)) || ''
        template = template.replaceAll(/\n/g, ' ').replaceAll(/\xA0/g, '\n')
        window.templateCache.set(cacheKey, template)
      } catch (e) {
        console.error('Failed to load template:', e)
      }
    }
  }
  startWatching()
})

// 使用 watchEffect 处理模板的加载和渲染
function startWatching() {
  watchEffect(async () => {
    if (!isVisible.value) {
      renderedText.value = ''
      return
    }
    renderedText.value = ''

    let templateString = ''
    try {
      // Check for cached template first if templateFile is defined
      if (props.schema?.templateFile?.type && props.schema?.templateFile?.name) {
        const cacheKey = `${props.schema.templateFile.type}:${props.schema.templateFile.name}`
        if (window.templateCache.has(cacheKey)) {
          templateString = window.templateCache.get(cacheKey) || ''
        }
      }
      // Fallback to schema.template
      else if (props.schema.template) {
        templateString = props.schema.template
      }
      // 如果没有，则尝试从 context 中获取
      else if (props.schema.model) {
        const nestedValue = getNestedValue(props.context, props.schema.model)
        if (nestedValue) {
          templateString = nestedValue
        }
      }

      waitForData && (await waitForData({ schema: props.schema }))

      // 如果仍然没有模板字符串，并且存在 loadTemplate 函数，则进行异步加载
      if (loadTemplate) {
        const loadedText = await loadTemplate({ schema: props.schema })
        if (loadedText) {
          templateString = loadedText
        }
      }

      // 根据获取到的模板字符串进行编译和渲染
      if (templateString) {
        // templateString = templateString.replace(/{{BASE}}/g, BASE);
        let data =
          (props.schema?.model && getNestedValue(props.context, props.schema?.model || '')) ||
          props.context
        let temp = await compileTemplate2(templateString, data)
        currentComponent.value = {
          template: `<span>${temp}</span>`,
          methods: {
            lc: (key: string) => loadComponent(key)
          }
        }
      } else {
        // 如果没有找到模板字符串，显示默认消息
        renderedText.value = '<span style="color: gray;">No template available.</span>'
      }
    } catch (e: any) {
      // 处理任何渲染过程中的错误
      // renderedText.value = `<span style="color: red;">Error rendering template: ${e.message} for ${templateString}</span>`
      // console.warn(e)
    }
  })
}

defineExpose({ compName: props?.schema?.type || '-' })
</script>

<style lang="scss" scoped>
.text-display {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin: 2px;

  label {
    display: flex;
    align-items: center;
    margin-right: 0.5rem;

    .label {
      min-width: 5rem;
      white-space: nowrap;
      text-align: right;
      padding-right: 0.5rem;
      font-weight: bold;
    }
  }

  .display-text {
    flex: 1;
    min-width: 5rem;
    /* 添加其他所需的样式 */
    &:focus {
      //outline: 1px solid lightblue;
      box-shadow: inset 1px 1px 2px lightgray;
    }

    &[contenteditable='true'] {
      padding: 5px;
      border: 1px solid lightgray;
    }
  }
}
</style>
<style lang="scss">
.slow {
  opacity: 0; /* Start with the element invisible */
  animation: fadeIn 0.1s ease-in-out 0.1s forwards; /* Apply the animation */
}

@keyframes fadeIn {
  from {
    opacity: 0; /* Start with opacity 0 */
  }
  to {
    opacity: 1; /* End with opacity 1 */
  }
}
</style>
