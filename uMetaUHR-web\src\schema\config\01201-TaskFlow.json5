{
  type: 'writingTask',
  ui: {
    type: 'layout',
    theme: 'side_by_side_2',
    myFavoriteId: '01201-taskFlow.side2.006',
    children: [
      {
        type: 'stack',
        myFavoriteId: '01201-taskFlow-stack-010',
        tabPosition: 'left',
        menu: [
          {
            id: 'workTrace',
            displayName: '日常文档'
          },
          {
            id: 'todo',
            displayName: 'To Do'
          }
        ],
        children: {
          workTrace: {
            type: 'dbFile',
            file: {
              type: 'workTrace'
            },
            subContext: {
              path: 'workTrace',
              default: {}
            }
          },
          todo: {
            type: 'external',
            externalSchema: '01300-Todo'
          }
        },
        subContext: {
          path: 'dbFile',
          default: {}
        }
      },
      {
        menu: [
          {
            id: 'text-generation',
            displayName: '文本生成'
          },
          {
            id: 'data',
            displayName: '数据'
          }
        ],
        type: 'stack',
        myFavoriteId: '01201-taskFlow.stack.051',
        children: {
          data: {
            type: 'section',
            title: '数据93',
            children: [
              {
                type: 'dataViewer',
                model: ''
              }
            ]
          },
          'text-generation': {
            type: 'window',
            title: '文本生成',
            style: {
              title: {
                background: 'lightblue',
                'font-size': '10pt'
              }
            },
            head: [
              {
                type: 'toolbar',
                style: {
                  'justify-content': 'right'
                },
                children: [
                  {
                    type: 'button',
                    event: {
                      name: 'generate-text'
                    },
                    label: '生成文本'
                  }
                ]
              }
            ],
            body: [
              {
                type: 'section',
                title: '原始文本',
                collapsible: true,
                myFavoriteId: '01201-taskFlow.094',
                children: [
                  {
                    type: 'richTextEditor',
                    model: 'inputText'
                  }
                ]
              },
              {
                type: 'section',
                title: '模版',
                collapsible: true,
                myFavoriteId: '01201-taskFlow.section.106',
                children: [
                  {
                    type: 'richTextEditor',
                    model: 'template'
                  }
                ]
              },
              {
                type: 'section',
                title: '数据',
                collapsible: true,
                myFavoriteId: '01201-taskFlow.118',
                children: [
                  {
                    type: 'dataViewer',
                    model: ''
                  }
                ]
              }
            ],
            subContext: {
              path: 'detail',
              default: {}
            }
          }
        },
        tabPosition: 'right'
      }
    ]
  },
  subContext: {
    path: '/temp.sop',
    default: {}
  }
}
