CREATE TABLE drug_ingredient
(
    id            INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    drug_id       INT NOT NULL,
    ingredient_id INT NOT NULL,
    strength      VARCHAR(50),
    is_main       BOOLEAN DEFAULT TRUE,
    created_at    BIGINT  DEFAULT EXTRACT(EPOCH FROM NOW()),
    updated_at    BIGINT  DEFAULT EXTRACT(EPOCH FROM NOW()),
    FOREIGN KEY (drug_id) REFERENCES drug (id),
    FOREIGN KEY (ingredient_id) REFERENCES active_ingredient (id),
    UNIQUE (drug_id, ingredient_id) -- Ensure unique combinations
);

COMMENT ON TABLE drug_ingredient IS '药品与成分关联表';
COMMENT ON COLUMN drug_ingredient.drug_id IS '关联药品ID';
COMMENT ON COLUMN drug_ingredient.ingredient_id IS '关联有效成分ID';
COMMENT ON COLUMN drug_ingredient.strength IS '成分含量(如：25mg)';
COMMENT ON COLUMN drug_ingredient.is_main IS '是否为主要活性成分';
COMMENT ON COLUMN drug_ingredient.created_at IS '关联记录创建时间';
