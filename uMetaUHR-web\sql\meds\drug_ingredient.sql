CREATE TABLE drug_ingredient
(
    id            INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    drug_id       INT NOT NULL,
    ingredient_id INT NOT NULL,
    strength      VARCHAR(50),
    is_main       BOOLEAN DEFAULT TRUE,
    created_at    BIGINT  DEFAULT EXTRACT(EPOCH FROM NOW()),
    updated_at    BIGINT  DEFAULT EXTRACT(EPOCH FROM NOW()),
    FOREIGN KEY (drug_id) REFERENCES drug (id),
    FOREIGN KEY (ingredient_id) REFERENCES active_ingredient (id),
    UNIQUE (drug_id, ingredient_id) -- Ensure unique combinations
);

COMMENT ON TABLE drug_ingredient IS 'Mapping between drugs and their ingredients';
COMMENT ON COLUMN drug_ingredient.drug_id IS 'Reference to drug table';
COMMENT ON COLUMN drug_ingredient.ingredient_id IS 'Reference to active ingredient table';
COMMENT ON COLUMN drug_ingredient.strength IS 'Ingredient strength (e.g. 25mg)';
COMMENT ON COLUMN drug_ingredient.is_main IS 'Whether this is the main active ingredient';
COMMENT ON COLUMN drug_ingredient.created_at IS 'Timestamp when mapping was created';
