//PatWorkspace

{
  type: 'patientWorkspace',
  ui: {
    type: 'tocDetail',
    style: {
      vars: {
        themeBC: '#EEE',
        themeContentBC: 'white'
      },
      toc: {
        padding: '0px'
      }
    },
    myFavoriteId: '01307-PatWorkspace-39',
    toc: {
      type: 'panel',
      children: [
        {
          type: 'toolbar',
          style: {
            display: 'flex',
            margin: '0 0 10px 0px',
            background: '#e6f2ff',
            flex: 0,
            borderBottom: '1px solid #CCCCCC'
          },
          children: [
            {
              type: 'button',
              label: '下一位',
              style: {
                main: {
                  background: '#365FD9',
                  borderRadius: '4px',
                  marginBottom: '10px',
                  color: '#FFFFFF'
                }
              },
              event: {
                name: 'open-next-patient'
              }
            },
            {
              type: 'button',
              label: '结束就诊',
              event: {
                name: 'pause-reception'
              }
            },
            {
              type: 'button',
              label: '取消就诊',
              event: {
                name: 'pause-reception'
              }
            }
          ]
        },
        {
          type: 'window',
          style: {
            main: {
              background: '#e6f2ff'
            }
          },
          head: [
            {
              type: 'textDisplay',
              style: {
                main: {
                  display: 'block',
                  flex: 1,
                  fontFamily: 'SourceHanSansCN-Bold',
                  fontSize: '21px',
                  color: 'rgba(0,0,0,0.80)',
                  lineHeight: '30px',
                  fontWeight: 700
                }
              },
              model: 'patData',
              template: '\
                <div style="marginLeft: 10px;">\
                  {{name}}\
                  <span style="font-size:16px; font-weight:400;">｜{{age birth_date}}｜{{codeValue gender "PATIENT.GENDER"}}</span>\
                </div>'
            },
//            {
//              type: 'dataViewer'
//            },
            {
              type: 'tag',
              mode: 'display',
              style: {
                main: {
                  gap: '8px',
                  marginTop: '5px',
                  marginBottom: '10px'
                }
              },
              options: [
                {
                  value: 'first_visit',
                  label: '初诊',
                  style: {
                    background: '#E8F1FF',
                    border: '1px solid #337DFF',
                    borderRadius: '12px',
                    fontSize: '14px',
                    color: '#2666CF',
                    lineHeight: '22px',
                    fontWeight: 400
                  }
                },
                {
                  value: 'self_pay',
                  label: '自费',
                  style: {
                    background: '#FFFCD6',
                    border: '1px solid #FAC219',
                    borderRadius: '12px',
                    fontSize: '14px',
                    color: '#D49610',
                    lineHeight: '22px',
                    fontWeight: 400
                  }
                }
              ]
            },
            {
              type: 'textDisplay',
              style: {
                main: {
                  fontSize: '14px',
                  fontWeight: 400,
                  color: '#202020',
                  borderBottom: '1px solid #CCCCCC'
                }
              },
              model: 'patData',
              template: '\
                <div style="display: flex; flex-direction: column; gap: 8px; margin: 0 0 10px -10px;">\
                  <div style="display: flex;">\
                    <span style="color:#939393; flex: 0 0 80px; text-align: right;">生日：</span>\
                    <span>{{formatDate birth_date "yyyy/MM/dd"}}</span>\
                  </div>\
                  <div style="display: flex;">\
                    <span style="color:#939393; flex: 0 0 80px; text-align: right;">身份证：</span>\
                    <span>{{id_card}}</span>\
                  </div>\
                  <div style="display: flex;">\
                    <span style="color:#939393; flex: 0 0 80px; text-align: right;">卡号：</span>\
                    <span>{{insurance_info.cardNumber}}</span>\
                  </div>\
                  <div style="display: flex;">\
                    <span style="color:#939393; flex: 0 0 80px; text-align: right;">电话：</span>\
                    <span>{{phone}}</span>\
                  </div>\
                  <div style="display: flex;">\
                    <span style="color:#939393; flex: 0 0 80px; text-align: right;">地址：</span>\
                    <span>{{address.province}}{{address.city}}{{address.detail}}</span>\
                  </div>\
              </div>'
            }
          ],
          body: [
            {
              type: 'textDisplay',
              style: {
                main: {
                  fontSize: '14px',
                  fontWeight: 400,
                  color: '#202020'
                }
              },
              model: 'patData',
              template: '\
                <div style="display: flex; flex-direction: column; gap: 8px; margin: 10px 0 10px 10px;">\
                  <div style="display: flex;">\
                    <span style="color:rgba(0,0,0,0.80); flex: 0 0 80px; font-size:16px; font-weight: 700;">挂号信息</span>\
                  </div>\
                  <div style="display: flex; flex-direction: column; gap: 8px; margin-left: -10px;">\
                    <div style="display: flex;">\
                      <span style="color:#939393; flex: 0 0 80px; text-align: right;">科室：</span>\
                      <span>{{department_id}}</span>\
                      <span style="color:#939393; flex: 0 0 80px; text-align: right; margin-left: 40px;">就诊医生：</span>\
                      <span>{{provider_id}}</span>\
                    </div>\
                    <div style="display: flex;">\
                      <span style="color:#939393; flex: 0 0 80px; text-align: right;">预约时间：</span>\
                      <span>{{appointment_date}}</span>\
                    </div>\
                  </div>\
              </div>'
            },
            {
              type: 'textDisplay',
              style: {
                main: {
                  fontSize: '14px',
                  fontWeight: 400,
                  color: '#202020'
                }
              },
              template: '\
                <div style="display: flex; flex-direction: column; gap: 8px; margin: 10px 0 10px 10px;">\
                  <div style="display: flex;">\
                    <span style="color:rgba(0,0,0,0.80); flex: 0 0 80px; font-size:16px; font-weight: 700;">健康概况</span>\
                  </div>\
                  <div style="display: flex; flex-direction: column; gap: 8px; margin-left: -10px;">\
                    <div style="display: flex;">\
                      <span style="color:#939393; flex: 0 0 80px; text-align: right;">身高：</span>\
                      <span>175cm</span>\
                      <span style="color:#939393; flex: 0 0 80px; text-align: right; margin-left: 40px;">体重：</span>\
                      <span>80kg</span>\
                    </div>\
                    <div style="display: flex;">\
                      <span style="color:#939393; flex: 0 0 80px; text-align: right;">过敏史：</span>\
                      <span>青霉素过敏、头孢过敏</span>\
                    </div>\
                    <div style="display: flex;">\
                      <span style="color:#939393; flex: 0 0 80px; text-align: right;">家族史：</span>\
                      <span>荨麻疹、高血压、糖尿病、心脏病</span>\
                    </div>\
                    <div style="display: flex;">\
                      <span style="color:#939393; flex: 0 0 80px; text-align: right;">传染病史：</span>\
                      <span>14 天内接触过新冠确诊病例</span>\
                    </div>\
                  </div>\
              </div>'
            }
          ]
        }
      ]
    },
    detail: {
      type: 'layout2',
      myFavoriteId: '01307-PatWorkspace-2',
      leftWidth: 900,
      children: [
        {
          type: 'window',
          body: [
            {
              type: 'stack',
              tabPosition: 'top',
              style: {
                vars: {
                  themeBorderColor: 'blue',
                  themeBC: 'rgba(255,255,255,0.80)',
                  themeActiveColor: '#365FD9'
                }
              },
              menu: [
                {
                  id: 'handle',
                  displayName: '处置'
                },
                {
                  id: 'healthInformation',
                  displayName: '健康信息'
                },
                {
                  id: 'preDiagnosis',
                  displayName: '预问诊'
                },
                {
                  id: 'historicalRecords',
                  displayName: '历史记录'
                }
              ],
              children: {
                handle: {
                  type: 'window',
                  body: [
                    {
                      type: 'section',
                      title: '诊断',
                      selfIcon: '/diagnosis.svg',
                      style: {
                        vars: {
                          themeBorderColor: '#B363DD'
                        }
                      },
                      collapsible: false,
                      myFavoriteId: '00341-pws-199',
                      model: 'dxList',
                      children: [
                        {
                          type: 'external',
                          externalSchema: '00401-DiagnosisEntry'
                        }
                      ]
                    },
                    {
                      type: 'section',
                      title: '医嘱',
                      style: {
                        vars: {
                          themeBorderColor: '#337DFF'
                        }
                      },
                      collapsible: true,
                      myFavoriteId: '00341-pws-212',
                      model: 'orderList',
                      children: [
                        {
                          type: 'external'
                          // externalSchema: '00401-DiagnosisEntry'
                        }
                      ]
                    }
                  ]
                },
                preDiagnosis: {
                  type: 'window',
                  body: [
                    {
                      type: 'section',
                      title: '工作内容',
                      collapsible: true,
                      children: [
                        {
                          type: 'toolbar',
                          children: [
                            {
                              type: 'tag',
                              label: '重要',
                              model: 'event.data.importance',
                              options: [
                                {
                                  value: '1',
                                  label: '普通'
                                },
                                {
                                  value: '2',
                                  label: '重要'
                                }
                              ],
                              mode: 'single'
                            },
                            {
                              type: 'tag',
                              label: '紧急',
                              model: 'event.data.urgency',
                              options: [
                                {
                                  value: '1',
                                  label: '普通'
                                },
                                {
                                  value: '2',
                                  label: '紧急'
                                }
                              ],
                              mode: 'single'
                            },
                            {
                              type: 'tag',
                              label: '进度',
                              model: 'event.data.progress',
                              options: [
                                {
                                  value: '1',
                                  label: '未开始'
                                },
                                {
                                  value: '2',
                                  label: '进行中'
                                },
                                {
                                  value: '3',
                                  label: '已完成'
                                }
                              ],
                              mode: 'single'
                            }
                          ]
                        },
                        {
                          type: 'richTextEditor',
                          model: 'event.data.note',
                          label: '内容'
                        }
                      ]
                    }
                  ]
                },
                historicalRecords: {
                  type: 'window',
                  body: [
                    {
                      type: 'section',
                      title: '上次就诊记录',
                      collapsible: true,
                      children: [
                        {
                          type: 'dataViewer',
                          model: 'lastVisitData'
                        }
                      ]
                    }
                  ]
                }
              }
            }
          ]
        },
        {
          type: 'window',
          body: [
            {
              type: 'stack',
              menu: [
                {
                  id: 'clinicalNotes',
                  displayName: '病历'
                },
                {
                  id: 'synopsis',
                  displayName: '病历摘要'
                },
                {
                  id: 'data',
                  displayName: '数据'
                }
              ],
              tabPosition: 'top',
              children: {
                clinicalNotes: {
                  type: 'external',
                  externalSchema: '01500-ClinicalNotes'
                },
                data: {
                  type: 'window',
                  body: [
                    {
                      type: 'dataViewer',
                      model: ''
                    }
                  ]
                }
              }
            }
          ]
        }
      ]
    }
  },
  subContext: {
    path: '/patient',
    default: {}
  }
}
