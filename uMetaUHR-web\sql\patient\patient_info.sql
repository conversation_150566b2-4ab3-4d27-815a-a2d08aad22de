DROP TABLE IF EXISTS patient_basic_info CASCADE;
DROP TABLE IF EXISTS patient_extension_info CASCADE;

CREATE TABLE patient_basic_info
(
    id             INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    pat_id         VARCHAR(32),
    name           VARCHA<PERSON>(100),
    gender         CHAR(1),
    birth_date     DATE,
    id_card        CHAR(18),
    phone          VARCHAR(20),
    address        JSONB  DEFAULT '{}'::JSONB,
    insurance_info JSONB  DEFAULT '{}'::JSONB,
    extensions     JSONB  DEFAULT '{}'::JSON<PERSON>,
    created_at     BIGINT DEFAULT (EXTRACT(EPOCH FROM CURRENT_TIMESTAMP)),
    updated_at     BIGINT DEFAULT (EXTRACT(EPOCH FROM CURRENT_TIMESTAMP))
);

-- Add comments
COMMENT ON TABLE patient_basic_info IS '患者基本信息表';
COMMENT ON COLUMN patient_basic_info.id IS '医院信息系统为患者分配的唯一标识代码 [数据元:PATIENT.ID]';
COMMENT ON COLUMN patient_basic_info.name IS '患者在户籍管理部门正式登记注册的姓氏和名称 [数据元:PATIENT.NAME]';
COMMENT ON COLUMN patient_basic_info.gender IS 'GB/T 2261.1-2003 人的性别代码(M-男,F-女,U-未知) [数据元:PATIENT.GENDER]';
COMMENT ON COLUMN patient_basic_info.birth_date IS '患者的出生日期 [数据元:PATIENT.BIRTH_DATE]';
COMMENT ON COLUMN patient_basic_info.id_card IS '符合GB 11643-1999标准的18位公民身份号码 [数据元:PATIENT.ID_CARD]';
COMMENT ON COLUMN patient_basic_info.phone IS '符合E.164国际标准的联系电话号码 [数据元:PATIENT.PHONE]';
COMMENT ON COLUMN patient_basic_info.address IS '地址信息 {
  "province": "省级行政区划代码 [数据元:PATIENT.ADDRESS.PROVINCE]",
  "city": "市级行政区划代码 [数据元:PATIENT.ADDRESS.CITY]",
  "county": "县级行政区划代码 [数据元:PATIENT.ADDRESS.COUNTY]",
  "street": "街道/乡镇级行政区划 [数据元:PATIENT.ADDRESS.STREET]",
  "detail": "详细地址(门牌号等) [数据元:PATIENT.ADDRESS.DETAIL]",
  "postalCode": "邮政编码 [数据元:PATIENT.ADDRESS.POSTAL_CODE]"
}';
COMMENT ON COLUMN patient_basic_info.insurance_info IS '医保类型及卡号信息 [数据元:PATIENT.INSURANCE]';
COMMENT ON COLUMN patient_basic_info.extensions IS '扩展属性';
COMMENT ON COLUMN patient_basic_info.created_at IS '创建时间';
COMMENT ON COLUMN patient_basic_info.updated_at IS '更新时间';

-- Create indexes
CREATE INDEX idx_name ON patient_basic_info (name);
CREATE INDEX idx_id_card ON patient_basic_info (id_card);
CREATE INDEX idx_phone ON patient_basic_info (phone);
CREATE INDEX idx_address ON patient_basic_info USING GIN (address jsonb_path_ops);

-- Add constraints
ALTER TABLE patient_basic_info
    ADD CONSTRAINT uniq_id_card CHECK (id_card IS NULL OR LENGTH(id_card) = 18);
ALTER TABLE patient_basic_info
    ADD CONSTRAINT chk_gender CHECK (gender IN ('M', 'F', 'U'));

CREATE TABLE patient_extension_info
(
    id                    INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    patient_basic_info_id INT,
    occupation            VARCHAR(50),
    education_level       VARCHAR(20),
    marital_status        VARCHAR(10),
    emergency_contacts    JSONB  DEFAULT '[]'::JSONB,
    medical_info          JSONB  DEFAULT '{}'::JSONB,
    living_habits         JSONB  DEFAULT '{}'::JSONB,
    family_history        JSONB  DEFAULT '{}'::JSONB,
    created_at            BIGINT DEFAULT (EXTRACT(EPOCH FROM CURRENT_TIMESTAMP)),
    updated_at            BIGINT DEFAULT (EXTRACT(EPOCH FROM CURRENT_TIMESTAMP)),
    FOREIGN KEY (patient_basic_info_id) REFERENCES patient_basic_info (id)
);

-- Add comments
COMMENT ON TABLE patient_extension_info IS '患者扩展信息表';
COMMENT ON COLUMN patient_extension_info.id IS '扩展信息记录ID';
COMMENT ON COLUMN patient_extension_info.patient_basic_info_id IS '患者ID';
COMMENT ON COLUMN patient_extension_info.occupation IS '职业';
COMMENT ON COLUMN patient_extension_info.education_level IS '教育程度';
COMMENT ON COLUMN patient_extension_info.marital_status IS '婚姻状况';
COMMENT ON COLUMN patient_extension_info.emergency_contacts IS '紧急联系人列表';
COMMENT ON COLUMN patient_extension_info.medical_info IS '医疗信息 {
  "blood_type": "ABO血型系统分类(A/B/AB/O)及Rh因子(+/-) [数据元:PATIENT.BLOOD_TYPE]",
  "allergies": [{
    "type": "过敏类型 [数据元:PATIENT.ALLERGY.TYPE]",
    "name": "过敏原名称 [数据元:PATIENT.ALLERGY.NAME]",
    "severity": "严重程度 [数据元:PATIENT.ALLERGY.SEVERITY]",
    "update_date": "最后更新日期 [数据元:PATIENT.ALLERGY.UPDATE_DATE]"
  }],
  "chronic_conditions": [{
    "code": "疾病代码 [数据元:PATIENT.CHRONIC_CONDITION.CODE]",
    "name": "疾病名称 [数据元:PATIENT.CHRONIC_CONDITION.NAME]",
    "diagnosis_date": "确诊日期 [数据元:PATIENT.CHRONIC_CONDITION.DATE]",
    "treatment": "治疗方案 [数据元:PATIENT.CHRONIC_CONDITION.TREATMENT]"
  }]
}';
COMMENT ON COLUMN patient_extension_info.living_habits IS '生活习惯';
COMMENT ON COLUMN patient_extension_info.family_history IS '家族病史';
COMMENT ON COLUMN patient_extension_info.created_at IS '创建时间';
COMMENT ON COLUMN patient_extension_info.updated_at IS '更新时间';

-- Create indexes
CREATE INDEX idx_patient_basic_info_id ON patient_extension_info (patient_basic_info_id);
CREATE INDEX idx_living_habits ON patient_extension_info USING GIN (living_habits jsonb_path_ops);

-- Add constraints
ALTER TABLE patient_extension_info
    ADD CONSTRAINT chk_education_level
        CHECK (education_level IN ('小学', '初中', '高中', '大专', '本科', '硕士', '博士', '其他'));
ALTER TABLE patient_extension_info
    ADD CONSTRAINT chk_marital_status
        CHECK (marital_status IN ('未婚', '已婚', '离异', '丧偶', '其他'));
