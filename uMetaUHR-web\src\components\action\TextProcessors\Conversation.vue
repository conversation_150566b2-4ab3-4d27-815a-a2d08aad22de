<template>
  <div class="conversation">
    <div v-for="(d, i) in data" :key="i" :class="{ me: isMe(d.role), other: !isMe(d.role) }">
      <div class="conversation-avatar">
        <img :src="schema?.avatars?.[d?.role]" alt="avatar" />
      </div>
      <div class="conversation-content">
        <component
          :is="loadComponent(schema?.messageViewer)"
          :context="getContext(props)"
          :markdownText="d.content"
          :schema="schema?.messageViewer"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { defineProps, onMounted, ref, watch } from 'vue'
import { getContext, getNestedValue, setNestedValue } from '@/lib/getContext'
import { loadComponent } from '@/components/componentRegistry'

const props = defineProps<{
  schema: {
    type: String
    children: Array<any>
  }
  meRoles?: {
    type: any
    default: () => ['user']
  }
  avatars?: {
    type: Object
    default: () => {
      '1': 'avatar1.png'
      '2': 'avatar2.png'
      '3': 'avatar3.png'
    }
  }
  context: any
}>()

let data = ref([])

onMounted(async () => {
  let aaa = getNestedValue(props.context, props?.schema?.model)
  if (!aaa) setNestedValue(props.context, props?.schema?.model, aaa || [])
  data.value = await getNestedValue(props.context, props?.schema?.model)
})

const isMe = (role: string) => {
  return (props?.schema?.meRoles || ['user']).includes(role)
}

watch(
  () => getNestedValue(props.context, props?.schema.model),
  async (newValue) => {
    data.value = newValue
  }
)
defineExpose({ compName: props?.schema?.type || '-' })
</script>

<style lang="scss" scoped>
.conversation {
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
  margin: 3px;

  > div {
    //border: 1px solid black;
    display: flex;
    flex-direction: column;
  }

  .me {
    display: flex;
    justify-content: right;
    align-self: flex-end;
    flex-flow: row-reverse;
  }

  .other {
    align-self: flex-start;
    flex-flow: row;
  }

  .conversation-avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    overflow: hidden;
    margin: 5px;
    border: 1px solid lightblue;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .conversation-content {
    max-width: 80%;
    padding: 10px;
    border-radius: 10px;
    background-color: #f8f8f8;
    margin: 5px 0;
  }

  .me .conversation-content {
    background-color: #d1e7dd;
  }

  .other .conversation-content {
    background-color: #f1f1f1;
  }
}
</style>
