import type { Periods, VisitStatus } from "@/types/pat-list";

// 查询操作符枚举
export enum QueryOperator {
  EQ = 'eq',                    // 等于
  BETWEEN = 'between',          // 范围查询（用于时间、数字等区间）
  LT = 'LT',                    // 小于
  LTE = 'LTE',                  // 小于等于
  GT = 'GT',                    // 大于
  LIKE = 'LIKE',                // 模糊匹配（用于字符串查询）
  IN = 'IN'                     // 包含于列表（用于数组值查询）
}

export enum QueryOrder {
  ASC = 'ASC',                  // 升序
  DESC = 'DESC'                 // 降序
}

export const VisitStatusEnum: Record<VisitStatus, string> = {
  pending: '待就诊',            // 待就诊
  inProgress: '就诊中',         // 就诊中
  completed: '已就诊',          // 已就诊
  missed: '已过号'              // 已过号
}

export const VisitStatusMarkColorEnum: Record<VisitStatus, string> = {
  pending: '#FAC219',            // 待就诊
  inProgress: '#337DFF',         // 就诊中
  completed: '#3CA63A',          // 已就诊
  missed: '#E64D2E'              // 已过号
}

export const PeriodsEnum: Record<Periods, string> = {
  AM: '上午',
  PM: '下午'
}