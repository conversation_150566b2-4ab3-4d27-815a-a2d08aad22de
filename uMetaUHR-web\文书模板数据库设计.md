# EMR模板数据库设计文档

## 概述

系统中模板管理的数据库表设计。先实现核心功能，后续可根据业务需求扩展。

## 表结构设计

### 主表：emr_templates

```sql
CREATE TABLE emr_templates (
  -- 基础标识字段
  template_id VARCHAR(64) PRIMARY KEY COMMENT '模板唯一标识',
  template_code VARCHAR(32) NOT NULL COMMENT '模板编码',
  template_name VARCHAR(100) NOT NULL COMMENT '模板名称',
  
  -- 分类字段
  document_category VARCHAR(20) NOT NULL COMMENT '文书分类',
  document_type VARCHAR(50) NOT NULL COMMENT '文书类型',
  template_level VARCHAR(20) NOT NULL COMMENT '模板级别',
  department_id VARCHAR(50) COMMENT '科室ID',
  
  -- 内容字段
  template_content LONGBLOB COMMENT '模板二进制内容',
  html_template TEXT COMMENT 'HTML预览内容',
  
  -- 版本控制字段
  version VARCHAR(20) DEFAULT '1.0' COMMENT '版本号',
  is_current TINYINT(1) DEFAULT 1 COMMENT '是否当前版本',
  
  -- 状态管理字段
  status TINYINT(1) DEFAULT 1 COMMENT '状态(0停用/1启用)',
  
  -- 基础管理字段
  create_user VARCHAR(50) COMMENT '创建人',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_user VARCHAR(50) COMMENT '修改人',
  update_time DATETIME COMMENT '修改时间',
  
  -- 索引
  INDEX idx_template_code (template_code),
  INDEX idx_document_type (document_type),
  INDEX idx_department (department_id, template_level)
);
```

## 字段详细说明

### 基础标识字段

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| template_id | VARCHAR(64) | 模板唯一标识，主键 | T_ADM_001_V1 |
| template_code | VARCHAR(32) | 模板编码，业务标识 | ADMISSION_001 |
| template_name | VARCHAR(100) | 模板显示名称 | 心内科入院记录模板 |

### 分类字段

| 字段名 | 类型 | 可选值 | 说明 |
|--------|------|--------|------|
| document_category | VARCHAR(20) | inpatient/outpatient/nursing/emergency | 文书大分类 |
| document_type | VARCHAR(50) | admission/discharge/progress/consultation | 文书具体类型 |
| template_level | VARCHAR(20) | hospital/department/personal | 模板适用级别 |
| department_id | VARCHAR(50) | 具体科室ID | 科室级模板必填 |

#### document_category 枚举值
- `inpatient` - 住院文书
- `outpatient` - 门诊文书  
- `nursing` - 护理文书
- `emergency` - 急诊文书

#### document_type 常用值
- `admission` - 入院记录
- `discharge` - 出院记录
- `progress` - 病程记录
- `consultation` - 会诊记录
- `operation` - 手术记录
- `nursing` - 护理记录
- `assessment` - 评估单

#### template_level 说明
- `hospital` - 全院级模板，所有科室可用
- `department` - 科室级模板，仅指定科室可用
- `personal` - 个人模板，仅创建者可用

### 内容字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| template_content | LONGBLOB | 模板的二进制内容（编辑器格式） |
| html_template | TEXT | HTML格式的预览内容（可选） |

### 版本控制字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| version | VARCHAR(20) | 版本号，如1.0、1.1、2.0 |
| is_current | TINYINT(1) | 是否为当前生效版本（0否/1是） |

**版本管理机制：**
- 同一个template_code可以有多个版本
- 只有一个版本的is_current=1
- 用户使用时自动选择当前版本

### 状态管理字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| status | TINYINT(1) | 模板状态（0停用/1启用） |

**状态说明：**
- status=0：模板停用，用户不可见
- status=1：模板启用，用户可选择使用

## 索引设计

```sql
-- 模板编码索引（用于快速查找特定模板）
INDEX idx_template_code (template_code)

-- 文书类型索引（用于按类型筛选）
INDEX idx_document_type (document_type)

-- 科室和级别复合索引（用于权限控制）
INDEX idx_department (department_id, template_level)
```

## 常用查询示例

### 1. 获取用户可选择的模板列表
```sql
SELECT template_id, template_name, document_type, version
FROM emr_templates 
WHERE status = 1 
  AND is_current = 1
  AND (template_level = 'hospital' 
       OR (template_level = 'department' AND department_id = '心内科')
       OR (template_level = 'personal' AND create_user = 'user001'))
ORDER BY document_type, template_name;
```

### 2. 获取特定文书类型的模板
```sql
SELECT template_id, template_name, version
FROM emr_templates 
WHERE document_category = 'inpatient'
  AND document_type = 'admission'
  AND status = 1 
  AND is_current = 1;
```

### 3. 版本管理操作
```sql
-- 发布新版本
BEGIN TRANSACTION;

-- 将旧版本标记为非当前版本
UPDATE emr_templates 
SET is_current = 0 
WHERE template_code = 'ADMISSION_001';

-- 插入新版本
INSERT INTO emr_templates (template_id, template_code, template_name, version, is_current, ...)
VALUES ('T_ADM_001_V2', 'ADMISSION_001', '心内科入院记录模板', '1.1', 1, ...);

COMMIT;
```

### 4. 紧急版本回滚
```sql
-- 将指定模板回滚到上一版本
UPDATE emr_templates 
SET is_current = CASE 
  WHEN version = '1.1' THEN 0  -- 停用问题版本
  WHEN version = '1.0' THEN 1  -- 启用稳定版本
  ELSE is_current 
END
WHERE template_code = 'ADMISSION_001';
```

## 数据示例

```sql
INSERT INTO emr_templates VALUES 
-- 全院级入院记录模板
('T_ADM_001_V1', 'ADMISSION_001', '标准入院记录模板', 'inpatient', 'admission', 'hospital', NULL, 
 NULL, '<html>预览内容</html>', '1.0', 1, 1, 'admin', NOW(), NULL, NULL),

-- 心内科专用入院记录模板  
('T_ADM_002_V1', 'ADMISSION_CARDIO', '心内科入院记录模板', 'inpatient', 'admission', 'department', '心内科',
 NULL, '<html>心内科预览</html>', '1.0', 1, 1, 'doctor001', NOW(), NULL, NULL),

-- 门诊病历模板
('T_OPD_001_V1', 'OUTPATIENT_001', '门诊病历模板', 'outpatient', 'progress', 'hospital', NULL,
 NULL, '<html>门诊预览</html>', '1.0', 1, 1, 'admin', NOW(), NULL, NULL);
```

## 扩展规划

### 第二期可考虑添加的字段：
- `template_alias` - 模板别名
- `parent_template_id` - 父模板ID（支持模板继承）
- `approval_status` - 审批状态
- `gender_limit` - 性别限制
- `age_range` - 年龄范围限制


## 注意事项

1. **版本管理**：同一template_code只能有一个is_current=1的记录
2. **权限控制**：根据template_level和department_id控制模板可见性
3. **数据完整性**：department级别的模板必须填写department_id
4. **性能优化**：合理使用索引，避免全表扫描
5. **备份策略**：template_content字段较大，需要考虑备份策略

## 版本历史

- v1.0 (2025-07-30) - 初始版本，包含核心字段
- 后续版本将根据业务需求逐步扩展
