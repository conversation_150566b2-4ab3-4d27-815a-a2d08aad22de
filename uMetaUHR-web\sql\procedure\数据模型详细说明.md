# 医疗操作数据模型详细说明文档

## 概述

本文档详细解释了基于Epic系统EAP/EDP主文件设计的医疗操作数据模型。该模型支持医院内所有类型的医疗操作管理，包括影像检查、实验室检验、手术操作、会诊服务等。

## 核心设计理念

### 1. 多用途操作设计
每个医疗操作可以同时具备多种用途：
- **可开立(ORDERABLE)**: 医生可以为此操作开立医嘱
- **可执行(PERFORMABLE)**: 可以记录操作的执行情况
- **可计费(CHARGEABLE)**: 可以用于费用结算
- **历史记录(HISTORICAL)**: 作为历史数据保留

### 2. 分层架构
```
系统默认配置 → 操作分类 → 具体操作 → 操作变体
```

### 3. 标准化编码体系
- **CPT**: 美国医学会诊疗操作编码
- **HCPCS**: 医疗用品和服务编码
- **LOINC**: 实验室检验编码
- **SNOMED-CT**: 临床医学术语系统
- **ICD-10-PCS**: 国际疾病分类手术编码

## 数据表结构详解

### 1. 操作主表 (procedures)
**业务角色**: 医疗操作的核心注册表，相当于Epic的EAP主文件

**关键字段**:
- `proc_code`: 系统内部唯一标识符（如：CT_HEAD, CBC）
- `proc_name`: 正式医学名称（如："头部CT平扫"）
- `proc_display_name`: 用户界面显示名称（如："头部CT"）
- `proc_type`: 操作类型（影像/检验/手术/会诊）
- 各种标准编码字段：支持多系统互操作

**典型用法**:
```sql
-- 查询所有激活的影像检查
SELECT proc_code, proc_name, cpt_code 
FROM procedures 
WHERE proc_type = 'IMAGING' AND is_active = TRUE;

-- 查找特定CPT码的操作
SELECT * FROM procedures WHERE cpt_code = '70450';
```

### 2. 操作分类表 (procedure_categories)
**业务角色**: 操作分组管理，相当于Epic的EDP主文件

**分类维度**:
- **order_type**: 业务大类（影像/检验/手术/护理）
- **specialty**: 专科归属（放射科/检验科/外科等）
- **default_priority**: 默认优先级设置

**典型用法**:
```sql
-- 获取所有影像检查分类
SELECT category_name, description 
FROM procedure_categories 
WHERE order_type = 'IMAGING';

-- 查看某分类的默认配置
SELECT default_priority, allowed_frequencies 
FROM procedure_categories 
WHERE category_code = 'IMAGING';
```

### 3. 操作用途表 (procedure_purposes)
**业务逻辑**: 解决"一个操作多种用途"的复杂场景

**实际场景示例**:
- **CT检查**: 既可作为医嘱开立，又可记录执行，还可用于计费
- **历史操作**: 可能只能查看，不能新开立

**典型用法**:
```sql
-- 查看某操作的所有用途
SELECT purpose_type, is_primary 
FROM procedure_purposes 
WHERE procedure_id = 123;

-- 查找所有可开立的操作
SELECT DISTINCT p.proc_name 
FROM procedures p
JOIN procedure_purposes pp ON p.id = pp.procedure_id
WHERE pp.purpose_type = 'ORDERABLE' AND p.is_active = TRUE;
```

### 4. 操作资源需求表 (procedure_resources)
**业务角色**: 操作执行所需的资源规划

**资源类型**:
- **STAFF**: 医护人员（如：放射科技师）
- **EQUIPMENT**: 设备（如：CT扫描仪）
- **INSTRUMENT**: 器械（如：手术刀）
- **SUPPLY**: 耗材（如：造影剂）
- **ROOM**: 场地（如：手术室）

**典型用法**:
```sql
-- 查看某操作所需的所有资源
SELECT resource_type, resource_name, quantity, is_required
FROM procedure_resources
WHERE procedure_id = 123;

-- 查找需要特定设备的所有操作
SELECT p.proc_name, pr.quantity, pr.preparation_time_minutes
FROM procedures p
JOIN procedure_resources pr ON p.id = pr.procedure_id
WHERE pr.resource_type = 'EQUIPMENT' 
  AND pr.resource_name = 'CT Scanner';
```

### 5. 操作协议表 (procedure_protocols)
**业务角色**: 标准化操作流程和质量控制

**适用场景**:
- 病理检查的标本处理流程
- 影像检查的技术参数设置
- 特殊检验的质量控制要求

**典型用法**:
```sql
-- 获取病理检查的详细协议
SELECT protocol_name, processing_steps, expected_turnaround_hours
FROM procedure_protocols
WHERE procedure_id = 456;
```

### 6. 操作费用关联表 (procedure_charge_linkages)
**业务逻辑**: 解决"一个临床操作对应多个收费项目"的问题

**收费类型**:
- **PROFESSIONAL**: 医师费用
- **TECHNICAL**: 技术费用
- **GLOBAL**: 总费用
- **FACILITY**: 设施费用

**典型用法**:
```sql
-- 查看某操作的所有收费项目
SELECT charge_type, base_price, billing_multiplier
FROM procedure_charge_linkages
WHERE procedure_id = 123 AND effective_date <= CURRENT_DATE;
```

## 高级功能表

### 7. 操作别名表 (procedure_aliases)
**业务价值**: 支持多语言、同义词、缩写，提升用户体验

**典型用法**:
```sql
-- 查找操作的所有别名（包括英文和中文）
SELECT alias_name, alias_type, language_code, is_preferred
FROM procedure_aliases
WHERE procedure_id = 123
ORDER BY language_code, is_preferred DESC;
```

### 8. 外部系统映射表 (procedure_mappings)
**业务价值**: 实现与其他医疗系统的数据互通

**支持系统**:
- HL7 FHIR标准
- DICOM影像系统
- Epic、Cerner等EMR系统
- 医保系统

### 9. 文档模板表 (procedure_templates)
**业务价值**: 标准化医疗文档，提高质量和效率

**模板类型**:
- **ORDER**: 医嘱模板
- **RESULT**: 结果报告模板
- **REPORT**: 检查报告模板
- **CONSENT**: 知情同意书模板

## 实际业务场景应用

### 场景1: 开立CT检查医嘱
```sql
-- 1. 查找可开立的CT操作
SELECT proc_code, proc_name, default_priority
FROM active_procedures
WHERE proc_type = 'IMAGING' 
  AND proc_name ILIKE '%CT%头部%'
  AND is_orderable = TRUE;

-- 2. 查看所需资源
SELECT resource_type, resource_name, preparation_time_minutes
FROM procedure_resources
WHERE procedure_id = (SELECT id FROM procedures WHERE proc_code = 'CT_HEAD');

-- 3. 获取医嘱模板
SELECT template_content
FROM procedure_templates
WHERE procedure_id = 123 AND template_type = 'ORDER' AND is_default = TRUE;
```

### 场景2: 检验科工作清单
```sql
-- 查看今日需要准备的检验操作
SELECT 
    p.proc_name,
    pr.resource_name,
    pr.quantity,
    pp.expected_turnaround_hours
FROM procedures p
JOIN procedure_resources pr ON p.id = pr.procedure_id
JOIN procedure_protocols pp ON p.id = pp.procedure_id
WHERE p.proc_type = 'LAB'
  AND p.is_active = TRUE
  AND pr.resource_type = 'EQUIPMENT';
```

### 场景3: 费用核算
```sql
-- 计算某患者检查的总费用
SELECT 
    p.proc_name,
    pcl.charge_type,
    pcl.base_price * pcl.billing_multiplier as final_price
FROM procedures p
JOIN procedure_charge_linkages pcl ON p.id = pcl.procedure_id
WHERE p.proc_code IN ('CT_HEAD', 'CBC')
  AND CURRENT_DATE BETWEEN pcl.effective_date AND COALESCE(pcl.end_date, '9999-12-31');
```

## 性能优化策略

### 1. 索引策略
- 高频查询字段建立索引（操作代码、名称、分类）
- 部分索引优化活跃操作查询
- 复合索引支持复杂查询条件

### 2. 视图封装
- `active_procedures`: 活跃操作快速查询
- `procedure_summary`: 操作概览信息

### 3. 数据分区建议
- 按操作类型分区
- 按时间范围分区历史数据

## 数据维护最佳实践

### 1. 操作生命周期管理
```sql
-- 停用旧操作（保留历史数据）
UPDATE procedures 
SET is_active = FALSE, updated_at = extract(epoch from now())
WHERE proc_code = 'OLD_PROCEDURE';

-- 添加新操作版本
INSERT INTO procedures (proc_code, proc_name, proc_category_id, ...)
VALUES ('NEW_PROCEDURE_V2', '新操作版本', ...);
```

### 2. 编码更新流程
```sql
-- 更新CPT编码
UPDATE procedures 
SET cpt_code = '70470', updated_at = extract(epoch from now())
WHERE proc_code = 'CT_HEAD_CONTRAST';
```

### 3. 资源需求变更
```sql
-- 添加新设备需求
INSERT INTO procedure_resources (procedure_id, resource_type, resource_name, quantity)
VALUES (123, 'EQUIPMENT', '新型CT机', 1);
```

## 总结

这个数据模型通过灵活的架构设计，既满足了医院日常运营的需求，又保持了与行业标准的一致性。通过多用途操作设计、标准化编码、资源需求管理等核心功能，为医院提供了完整的医疗操作管理解决方案。
