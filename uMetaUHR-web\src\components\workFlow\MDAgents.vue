<template>
  <div class="ma-agents">
    <component
      :is="loadComponent(props.schema?.ui?.type)"
      v-if="props.schema"
      :context="curContext"
      :schema="props.schema?.ui"
    />
  </div>
</template>

<script lang="ts" setup>
import { defineProps, provide, reactive } from 'vue'
import { loadComponent } from '../componentRegistry'
import { getContext } from '@/lib/getContext'
import { dbGet, dbList, dbSave } from '@/lib/dataMan'
import axios from 'axios'

const props = defineProps<{
  schema: {
    type: string
    children: Array<any>
  }
  context: { [key: string]: any }
}>()
let curContext = reactive(getContext(props))

provide('toc-list-data', async (param: any) =>
  dbList({
    table: 'agent',
    columns: ['id', 'name']
  })
)

provide('toc-get-data', async (param: any) => {
  let { id } = param || {}
  if (!id) return

  let item =
    ((await dbGet({
      table: 'agent',
      id,
      columns: ['id', 'data', 'name']
    })) as any) || ({} as any)

  let { data = {} } = item
  data.prompts = data.prompts || [{ name: '', prompt: '' }]
  return item
})

provide('promptRefreshItems', async (param: any) => {
  let { detail: { data = {} } = {} } = reactive(getContext(props))
  return data.prompts
})

provide('promptListItems', async (param: any) => {
  let { detail: { data: { prompts = [] } = {} } = {} } = reactive(getContext(props))
  return prompts.sort((a: any, b: any) => a?.name?.localeCompare(b?.name))
})

provide('promptSelectItem', async (param: any) => {
  let agentContext = reactive(getContext(props, '/temp/mdAgent'))
  let { detail: { data = {} } = {} } = reactive(getContext(props))
  let { data: { name = '' } = {} } = param || {}
  agentContext.scratch2 = data?.prompts?.find((x: any) => x.name === name)
  return {}
})

provide('promptNewItem', async (param: any) => {
  let agentContext = getContext(props, '/temp/mdAgent')
  let { detail: { data = {} } = {} } = getContext(props)
  let item = {}
  data?.prompts?.push(item)
  agentContext.scratch2 = item
  return {}
})

provide('promptDeleteItem', async (param: any) => {
  let { context: { name = '' } = {} } = param || {}
  let { detail: { data: { prompts = [] } = {} } = {} } = getContext(props)
  let i = prompts?.findIndex(({ name: n = '' }: any) => n === name)
  prompts?.splice(i, 1)

  let agentContext = getContext(props, '/temp/mdAgent')
  agentContext.scratch2 = i >= prompts.length ? prompts[i - 1] : prompts[i]
  return {}
})

provide('toc-save-data', async (param: any) => {
  let { detail: data } = curContext
  return dbSave({
    table: 'agent',
    data: [data],
    conflict_column: 'id',
    update_columns: ['id', 'data', 'name']
  })
})

provide('agent-validation', async (param: any) => {
  let {
    detail: {
      data = {},
      data: { role: { role_spec = '' } = {}, config = {}, testMessage = '' } = {}
    } = {}
  } = getContext(props)
  if (!testMessage) return alert('no testMessage')

  await (window as any).popAlert({ message: '询问GPT ... ' })
  try {
    const response = await axios.post('http://localhost:5001/api/getchat2', {
      messages: [
        {
          role: 'system',
          content: role_spec
        },
        {
          role: 'user',
          content: testMessage
        }
      ],
      config
    })

    let { data: { result: { content = '' } = {} } = {} } = response || {}
    data.testResult = content
  } catch (error) {
    await window.popAlert({ message: `结果：${JSON.stringify(error)}` })
  }
})

defineExpose({ compName: props?.schema?.type || '-' })
</script>

<style lang="scss" scoped>
.ma-agents {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  overflow: auto;
}
</style>
