{
  type: 'patent',
  ui: {
    type: 'tocDetail',
    toc: {
      type: 'toc',
      columnDef: [
        {
          field: 'id',
          displayName: 'id'
        },
        {
          field: 'name',
          displayName: '名称'
        }
      ],
      selectedLine: 0
    },
    style: {
      toc: {
        width: '10rem'
      }
    },
    detail: {
      type: 'layout',
      theme: 'side_by_side_2',
      style: {
        child2: {
          width: '30rem'
        }
      },
      children: [
        {
          body: [
            {
              type: 'section',
              title: '基本信息',
              children: [
                {
                  type: 'inputText',
                  label: 'ID',
                  model: 'id'
                },
                {
                  type: 'inputText',
                  label: '名称',
                  model: 'name'
                }
              ]
            },
            {
              type: 'section',
              title: '问答',
              children: [
                {
                  type: 'dataList',
                  nodeSchema: [
                    {
                      type: 'markdownViewer',
                      model: 'question',
                      theme: 'editMode',
                      editMode: false
                    },
                    {
                      type: 'toolbar',
                      children: [
                        {
                          type: 'button',
                          event: {
                            name: 'agent-writer'
                          },
                          label: 'AI辅助书写'
                        }
                      ]
                    },
                    {
                      type: 'richTextEditor',
                      model: 'answer'
                    }
                  ]
                }
              ],
              subContext: {
                path: 'data.listOfData',
                default: [{}]
              }
            },
            {
              type: 'section',
              title: '数据',
              children: [
                {
                  type: 'dataViewer',
                  model: ''
                }
              ]
            }
          ],
          head: [
            {
              type: 'toolbar',
              children: [
                {
                  type: 'button',
                  event: {
                    name: 'new_patent'
                  },
                  label: '新建'
                },
                {
                  type: 'button',
                  event: {
                    name: 'toc-save-data'
                  },
                  label: '保存'
                },
                {
                  type: 'button',
                  event: {
                    name: 'toc-delete-data'
                  },
                  label: '删除'
                }
              ]
            }
          ],
          type: 'window',
          subContext: {
            path: 'detail',
            default: {}
          }
        },
        {
          type: 'section',
          style: {
            main: {
              margin: 0,
              flex: '1 1 auto'
            },
            body: {
              padding: 0,
              flex: '1 1 auto'
            }
          },
          title: '1.问诊',
          children: [
            {
              type: 'external',
              style: {
                main: {
                  width: '30rem'
                }
              },
              externalSchema: '00610-ChatBox',
              subContext: {
                path: 'detail.data',
                default: {}
              }
            }
          ]
        }
      ]
    }
  },
  subContext: {
    path: '/temp.patent',
    default: {}
  }
}
