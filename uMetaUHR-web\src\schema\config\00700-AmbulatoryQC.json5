{
  type: 'ambulatoryQC',
  ui: {
    type: 'tocDetail',
    myFavoriteId: '00700-AmbulatoryQC-005',
    toc: {
      type: 'toc',
      myFavoriteId: '00700-AmbulatoryQC-008',
      columnDef: [
        {
          field: 'ID',
          displayName: 'ID',
          style: {
            width: '5.5rem',
            'text-align': 'center'
          }
        },
        {
          field: '主诉',
          displayName: '主诉'
        }
      ]
    },
    style: {
      toc: {
        'min-width': '30rem'
      }
    },
    detail: {
      type: 'layout',
      theme: 'side_by_side_2',
      children: [
        {
          body: [
            {
              type: 'section',
              title: '门诊病历',
              children: [
                {
                  label: '病历内容',
                  type: 'markdownViewer',
                  templateModel: 'dataTemplate',
                  model: 'detail'
                }
              ]
            }
          ],
          head: [
            {
              type: 'toolbar',
              children: [
                {
                  type: 'button',
                  event: {
                    name: 'toc-save'
                  },
                  label: '保存'
                },
                {
                  type: 'button',
                  event: {
                    name: 'toc-delete'
                  },
                  label: '删除'
                },
                {
                  type: 'button',
                  event: {
                    name: 'ambulatory-qc'
                  },
                  label: '质控'
                }
              ]
            }
          ],
          type: 'window'
        },
        {
          menu: [
            {
              id: 'qc',
              displayName: '质控结果'
            },
            {
              id: 'data',
              displayName: '数据'
            }
          ],
          type: 'stack',
          children: {
            qc: {
              type: 'section',
              title: '质控',
              children: [
                {
                  label: '',
                  type: 'markdownViewer',
                  templateModel: 'resultTemplate',
                  model: 'detail.qcResult'
                }
              ]
            },
            data: {
              type: 'section',
              title: '数据',
              children: [
                {
                  type: 'dataViewer',
                  model: ''
                }
              ],
              subContext: {
                path: 'detail',
                default: {}
              }
            }
          },
          activeMenuId: 'qc',
          tabPosition: 'right'
        }
      ]
    }
  },
  subContext: {
    path: '/temp.ambQC',
    default: {}
  }
}
