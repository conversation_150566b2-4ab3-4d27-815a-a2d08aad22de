<template>
  <div class="desktop">
    <component
      :is="loadComponent(props.schema?.ui?.type)"
      v-if="props.schema"
      :context="curContext"
      :schema="props.schema?.ui"
    />
  </div>
</template>

<script lang="ts" setup>
import { defineProps, provide, reactive } from 'vue'
import { loadComponent } from '../componentRegistry'
import { getContext } from '@/lib/getContext'
import eventBus from '@/components/mixins/eventBus'

const props = defineProps<{
  schema: {
    type: string
    children: Array<any>
  }
  context: { [key: string]: any }
}>()
let curContext = reactive(getContext(props))

const openPatientRecord = async (id: any, name: string) => {
  if (!id) return
  let context = getContext(props)
  context.workspace = { patient: { id } }
  const comp = await getCompInstance('patient-workspace')
  if (!comp?.pickItem) return
  await comp?.pickItem({ menuId: '就诊', name: name })
}

provide('open-patient-record', async (param = {}) => {
  let { data: { patient_id = '', patient_name = '' } = {} } = param as any
  if (!patient_id) return
  await openPatientRecord(patient_id, patient_name)
  return {}
})

async function getCompInstance(name: string) {
  let { comp } = (await new Promise((cb) => eventBus.emit(name, { cb }))) || ({} as any)
  return comp ?? {}
}

provide('open-next-patient', async (param = {} as any) => {
  const patList = await getCompInstance('toc-patient-list')
  const { id, name, message } = (await patList?.nextItem?.()) || {}

  if (message) {
    await window.popAlert({ message })
    return
  }

  if (!id) {
    await window.popAlert({ message: '无更多的患者。' })
    return
  }

  await openPatientRecord(id, name)

  return {}
})

defineExpose({ compName: 'workspace' })
</script>

<style lang="scss" scoped>
.desktop {
  display: flex;
  flex-direction: column;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
</style>
