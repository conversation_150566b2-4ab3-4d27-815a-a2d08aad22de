<template>
  <div :class="['emr-editor', schema?.theme]" :style="schema?.style?.main">
    <span v-if="schema?.label" class="label">{{ schema?.label }}</span>
    <div :id="editorId" class="editor-container"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, defineExpose, nextTick, defineEmits } from 'vue'
import { EMR_EDITOR_DEFAULT_OPTION } from '@/service/base/editorConfig'
import { OutpatientEditorOperator } from '@/lib/EditorOperator/OutpatientEditorOperator'
import { editorManager } from '@/lib/EditorOperator/EditorInstanceManager'

const emit = defineEmits(['ready'])

const props = defineProps<{
  schema?: any
  context?: any
  option?: any
}>()

const editorId = `emr-editor-${Math.random().toString(36).slice(2)}`
const editorInstance = ref<any>(null)
const editorFunction = ref<any>(null)
const editorOperator = ref<OutpatientEditorOperator | null>(null)

function loadSdkJs() {
  return new Promise<void>((resolve, reject) => {
    if ((window as any).EmrEditorSDK) return resolve();
    const script = document.createElement('script');
    const base = import.meta.env.BASE_URL || '/';
    script.src = base + '/sdk.js';
    script.onload = () => resolve();
    script.onerror = reject;
    document.body.appendChild(script);
  });
}

async function initEditor() {
  await loadSdkJs()
  const Editor = (window as any).EmrEditorSDK.Editor
  const editorDiv = document.getElementById(editorId)
  if (!Editor || !editorDiv) return

  try {
    const vm = await new Editor().init({
      dom: editorDiv,
      src: props.schema?.src || EMR_EDITOR_DEFAULT_OPTION.EMR_EDITOR_URL,
      option: {
                bShowMenu: false,
                bShowToolbar: true,
                isTest: false,
                bScrollY:false,
      }
    })

    const editor = await vm.getEditor()
    editorFunction.value = editor
    editorInstance.value = vm

    // 创建编辑器操作器
    const operator = new OutpatientEditorOperator(editor)
    editorOperator.value = operator

    // 从context获取viewId
    const viewId = props.context?.editorViewId || '默认编辑器'

    // 注册到编辑器管理器
    editorManager.setEditor(viewId, operator)

    // 触发全局事件，通知编辑器已准备就绪
    window.dispatchEvent(new CustomEvent('emrEditorReady', {
      detail: { operator, editor, instance: vm, viewId }
    }))

    // 可在此设置事件
    // vm.setEvent({...})
    emit('ready', editor)
  } catch (error) {
    console.error('Failed to initialize editor:', error)
  }
}

onMounted(() => {
  nextTick(() => {
    initEditor()
  })
})

defineExpose({
  editorInstance,
  editorFunction,
  get editorOperator() {
    return editorOperator.value
  }
})
</script>

<style scoped>
.emr-editor {
  display: flex;
  flex-direction: column;
  width: 100%;
}
.editor-container {
  width: 100%;
  height: 800px; /* 可通过props或schema控制高度 */
}
</style>