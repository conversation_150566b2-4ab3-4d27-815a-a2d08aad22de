<template>
  <div :style="schema?.style || schema?.main?.style" class="toolbar">
    <component
      :is="loadComponent(child?.type)"
      v-for="(child, index) in schema?.children"
      :key="index"
      :context="getContext(props)"
      :schema="child"
    />
  </div>
</template>

<script lang="ts" setup>
import { defineProps, type StyleValue } from 'vue'
import useSubContext from '../mixins/subContextMixin'
import { loadComponent } from '../componentRegistry'
import { getContext } from '@/lib/getContext'

const props = defineProps<{
  schema: {
    type: string
    style: StyleValue
    children: Array<any>
  }
  context: { [key: string]: any }
}>()

const { context: subContext, processSchema } = useSubContext()
subContext.value = { ...props.context }
processSchema(props.schema, subContext.value)

defineExpose({ compName: props?.schema?.type || '-' })
</script>

<style lang="scss" scoped>
.toolbar {
  position: sticky;
  top: 3px;
  display: flex;
  flex: 1 1 auto;
  max-height: 30hx;
  padding: 5px;
  background: #e6f2ff;
  // 添加渐变支持
  //background: linear-gradient(to top, #d6e9fa, #89afe5);
  z-index: 300;
}
</style>
