import { CommandFlow } from '@/components/action/commandFlow/CommandFlow'

type CommandHandler = (flow: CommandFlow) => Promise<void> | void

export class CommandManager {
  private handlers: Record<string, CommandHandler[]> = {}

  // Register a command handler
  on(commandName: string, handler: CommandHandler): void {
    if (!this.handlers[commandName]) this.handlers[commandName] = []
    this.handlers[commandName].push(handler)
  }

  // Execute a command
  async execute(commandName: string, flow: CommandFlow): Promise<CommandFlow> {
    const handlers = this.handlers[commandName] || []

    for (const handler of handlers) {
      if (flow.isStopped()) break
      await handler(flow)
    }

    return flow
  }
}
