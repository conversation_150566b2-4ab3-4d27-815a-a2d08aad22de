{
  type: 'typesenseMan',
  ui: {
    type: 'tocDetail',
    myFavoriteId: '00341-typesense-05',
    toc: {
      type: 'toc',
      myFavoriteId: '00341-typesense-08',
      event: {
        listItems: 'list-docs',
        selectItem: 'select-doc'
      },
      columnDef: [
        {
          field: 'name',
          displayName: '名称'
        }
      ],
      selectedLine: 0
    },
    detail: {
      type: 'window',
      head: [
        {
          type: 'toolbar',
          children: [
            {
              type: 'button',
              event: {
                name: 'typesense-new-collection'
              },
              label: '1 new collection'
            },
            {
              type: 'button',
              event: {
                name: 'typesense-fill-diag'
              },
              label: '2 fill dx'
            },
            {
              type: 'button',
              event: {
                name: 'typesense-fill-meds'
              },
              label: '3 fill meds'
            }
          ]
        }
      ],
      body: [
        {
          type: 'section',
          myFavoriteId: '00341-typesense-28',
          title: '测试药品检索',
          children: [
            {
              type: 'tag',
              label: '类别',
              model: 'category',
              myFavoriteId: '00341-typesense-tag-65',
              options: [
                {
                  value: 'dx',
                  label: '诊断'
                },
                {
                  value: 'meds',
                  label: '药品'
                }
              ],
              mode: 'single'
            },
            {
              type: 'recordSelector',
              label: '输入药品',
              model: 'name',
              search: {
                docType: 'meds',
                param: {
                  query_by: 'name'
                }
              },
              columnDef: [
                {
                  field: 'id',
                  displayName: 'ID'
                },
                {
                  field: 'name',
                  displayName: '名称'
                }
              ]
            }
          ]
        },
        {
          type: 'section',
          title: '检索的结果',
          collapsible: true,
          myFavoriteId: '00341-typesense.section-49',
          children: [
            {
              type: 'dataViewer',
              model: 'doc'
            }
          ]
        },
        {
          type: 'section',
          title: 'Diagnosis Entry',
          style: {
            vars: {
              themeBorderColor: 'blue'
            }
          },
          collapsible: true,
          myFavoriteId: '00341-typesense.section-113',
          model: 'orderList',
          children: [
            {
              type: 'external',
              externalSchema: '00401-DiagnosisEntry'
            }
          ]
        },
        {
          type: 'section',
          title: '数据',
          collapsible: true,
          myFavoriteId: '00341-typesense.section-61',
          children: [
            {
              type: 'dataViewer',
              model: 'detail.data'
            }
          ]
        }
      ]
    }
  },
  subContext: {
    path: '/temp.typesense',
    default: {}
  }
}
