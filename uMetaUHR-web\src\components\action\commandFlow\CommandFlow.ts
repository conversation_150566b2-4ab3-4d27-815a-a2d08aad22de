export class CommandFlow {
  private stopped = false
  private results: Record<string, any> = {}
  private readonly customHandlers: Record<string, Function>

  constructor(
    public readonly context: Record<string, any>,
    handlers: Record<string, Function> = {}
  ) {
    this.customHandlers = handlers
  }

  // Call a custom handler if it exists
  async request(key: string, ...args: any[]): Promise<any> {
    if (this.stopped)
      throw new Error(`Flow has been stopped. Cannot process request for key: ${key}`)
    if (typeof this.customHandlers.request === 'function') {
      return this.customHandlers.request(key, ...args)
    }
    throw new Error(`Request handler for key "${key}" is not defined.`)
  }

  // Stop the flow
  stop(reason: string): void {
    this.stopped = true
    console.warn(`CommandFlow stopped: ${reason}`)
  }

  // Check if the flow is stopped
  isStopped(): boolean {
    return this.stopped
  }

  // Set a result
  setResult(key: string, value: any): void {
    this.results[key] = value
  }

  // Get a result
  getResult<T = any>(key: string): T | undefined {
    return this.results[key]
  }
}
