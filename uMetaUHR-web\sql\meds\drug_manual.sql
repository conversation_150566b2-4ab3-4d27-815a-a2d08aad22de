CREATE TABLE drug_manual
(
    id                INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    drug_id           INT NOT NULL,
    version           VARCHAR(20) NOT NULL,
    indications       TEXT[]      NOT NULL,
    contraindications TEXT[]      NOT NULL,
    sections          JSONB       NOT NULL,
    created_at        BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
    updated_at        BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
    FOREIGN KEY (drug_id) REFERENCES drug (id)
);

COMMENT ON TABLE drug_manual IS 'Drug manual/prescribing information';
COMMENT ON COLUMN drug_manual.drug_id IS 'Reference to drug table';
COMMENT ON COLUMN drug_manual.version IS 'Manual version identifier';
COMMENT ON COLUMN drug_manual.indications IS 'Array of approved indications';
COMMENT ON COLUMN drug_manual.contraindications IS 'Array of contraindications';
COMMENT ON COLUMN drug_manual.sections IS 'JSON structure containing other manual sections';
COMMENT ON COLUMN drug_manual.created_at IS 'Timestamp when record was created';
COMMENT ON COLUMN drug_manual.updated_at IS 'Timestamp when record was last updated';

-- Create indexes
CREATE INDEX idx_manual_drug ON drug_manual (id);
CREATE INDEX idx_manual_sections ON drug_manual USING GIN (sections);
