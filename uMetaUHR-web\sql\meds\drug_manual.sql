CREATE TABLE drug_manual
(
    id                INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    drug_id           INT NOT NULL,
    version           VARCHAR(20) NOT NULL,
    indications       TEXT[]      NOT NULL,
    contraindications TEXT[]      NOT NULL,
    sections          JSONB       NOT NULL,
    created_at        BIGINT DEFAULT EXTRACT(EPOCH FROM NOW())*1000,
    updated_at        BIGINT DEFAULT EXTRACT(EPOCH FROM NOW())*1000,
    FOREIGN KEY (drug_id) REFERENCES drug (id)
);

COMMENT ON TABLE drug_manual IS '药品说明书信息表';
COMMENT ON COLUMN drug_manual.drug_id IS '关联药品ID';
COMMENT ON COLUMN drug_manual.version IS '说明书版本标识';
COMMENT ON COLUMN drug_manual.indications IS '适应症数组';
COMMENT ON COLUMN drug_manual.contraindications IS '禁忌症数组';
COMMENT ON COLUMN drug_manual.sections IS '包含其他说明书章节的JSON结构';
COMMENT ON COLUMN drug_manual.created_at IS '记录创建时间';
COMMENT ON COLUMN drug_manual.updated_at IS '记录最后更新时间';

-- Create indexes
CREATE INDEX idx_manual_drug ON drug_manual (id);
CREATE INDEX idx_manual_sections ON drug_manual USING GIN (sections);
