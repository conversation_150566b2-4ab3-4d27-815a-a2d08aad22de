<template>
  <a-collapse 
    v-bind="props.schema?.attrs" 
    @change="change" 
    :style="props.schema?.style"
  >
    <a-collapse-panel class="ant-collapse-content-box-class" v-for="child in props.schema.children" v-bind="child?.attrs" :key="child.key">
      <template 
        v-for="slot in child.slots" 
        :key="slot.name" 
        #[slot.name]
      >
        <div class="slot-parent">
          <img 
            v-if="slot.customIcon" 
            :src="`${BASE}${slot.customIcon.src}`" 
            :style="{width: '16px', height: '16px', ...slot.customIcon.style}" 
          />
          <component
            :is="slot?.customComponent ? loadComponent(slot.customComponent.type) : undefined"
            v-if="slot?.customComponent?.type"
            :context="getContext(props)"
            :schema="slot?.customComponent"
          />
          <p v-if="slot.content" >{{slot.content}}</p>
        </div>
      </template>
      <component
        :is="loadComponent(_child?.type)"
        v-for="(_child, index) in child.children || []"
        :key="`collapse-panel-child-${index}`"
        :context="getContext(props)"
        :schema="_child"
      />
    </a-collapse-panel>
  </a-collapse>
</template>
<script lang="ts" setup>
import { defineProps, inject } from 'vue'
import { loadComponent } from '@/components/componentRegistry'
import { getContext, getNestedValue } from '@/lib/getContext'

const BASE = import.meta.env.BASE_URL || ''

interface Title {
  icon?: string,
  customIcon?: string
}

interface Slot {
  name: 'header' | 'extra',
  content: string,
  customIcon?: {
    src: string,
    style?: Record<string, any>,
  },
  customComponent?: {
    type: string,
    [key: string]: any
  }
}

interface Panel {
  attrs?: Record<string, any>,
  key: string,
  slots: Slot[],
  children: any[]
}

const props = defineProps<{
  schema: {
    attrs?: Record<string, any>
    style?: Record<string, any>
    title: Title
    children: Panel[]
    activeKey: string[] | string | number[] | number
    event?: {
      change: string
    }
  }
  context: { [key: string]: any }
}>()

const {
  schema: {
    event: {
      change: changeEvent = 'change-collapse',
    } = {}
  } = {}
} = props

const changeFunc = inject(changeEvent, () => 1) as Function

const change = (key: string) => {
  changeFunc(key)
}
</script>

<style lang="scss" scoped>
.ant-collapse-content-box-class:deep(.ant-collapse-content-box) {
  padding: 0 !important;
  padding-block: 0;
}

.slot-parent {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;

  > * {
    margin-right: 8px
  }
}
</style>
