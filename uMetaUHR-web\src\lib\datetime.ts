// let systemTimeDiff = 0
// let lastUpdateTime = 0

export async function getTime() {
  const now = Date.now()
  // Uncomment and configure the following lines to fetch the server time
  // if (now - lastUpdateTime > 600 * 1000) {
  //   const { data } = await axios.get(`${process.env.VUE_APP_API_URL}/api/time?t=${now}`);
  //   systemTimeDiff = +data - now;
  //   lastUpdateTime = now;
  // }
  // return now + systemTimeDiff
}

export function sleep(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

export function dayToDate(day: number) {
  if (!(+day > 100)) return day

  try {
    // 基准日期：1900-01-01，序列号减1，因为序列号从1开始
    const baseTime = Date.UTC(1900, 0, 1) // 基准时间的 UTC 毫秒数
    const targetTime = baseTime + (+day - 1) * 86400000 // 86400000 = 一天的毫秒数
    return new Date(targetTime).toISOString().split('T')[0] // 转换为 YYYY-MM-DD 格式
  } catch (e) {
    return day
  }
}
