import { BaseEditorOperator } from './BaseEditorOperator';

export class InpatientEditorOperator extends BaseEditorOperator {
  async openRecord(recordId: string) {
    // 住院专属打开逻辑
    if (this.editor && this.editor.loadInpatientRecord) {
      return await this.editor.loadInpatientRecord(recordId);
    }
    // 默认回退到基类实现
    return super.openRecord(recordId);
  }

  async saveRecord(data: any) {
    // 住院专属保存逻辑
    if (this.editor && this.editor.saveInpatientRecord) {
      return await this.editor.saveInpatientRecord(data);
    }
    return super.saveRecord(data);
  }

  // ...住院专属方法
} 