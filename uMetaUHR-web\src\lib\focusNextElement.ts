const focusNextElement = (elem: any) => {
  if (!elem) return
  // Get all focusable elements in document
  const focusableElements = Array.from(
    document.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    )
  ).filter((el) => !el.hasAttribute('disabled')) as HTMLElement[]

  if (focusableElements.length > 0) {
    const currentIndex = focusableElements.indexOf(elem)
    if (currentIndex > -1 && currentIndex < focusableElements.length - 1) {
      focusableElements[currentIndex + 1].focus()
    }
  }
}

export { focusNextElement }
