import type { AuthClient, AuthTokenType } from '@uap/oidc-client'

export function useLogin() {
  function setAuthLoginInfo (authToken: AuthTokenType) {
    localStorage.setItem('userId', authToken.getUserId())
    if (authToken.profile.tenantId) {
      localStorage.setItem('tenantId', authToken.profile.tenantId)
    }
  }

  /**
   * 登录成功回调页面执行
   */
  function handleLoginSuccess (authClient: AuthClient) {
    authClient
      .loginCallback()
      .then(async () => {
        const token = await authClient.getAuthToken()
        if (token) {
          setAuthLoginInfo(token)
        }
        const redirectUrl = sessionStorage.getItem('redirectUrl')
        sessionStorage.removeItem('errorNumber')
        sessionStorage.removeItem('redirectUrl')
        window.location.replace(redirectUrl || '/')
      })
  }

  /**
   * 检查是否已经登录，没有则跳转登录
   */
  async function handleLogin(
    authClient: AuthClient,
    saveRedirectUrl: (isLogout?: boolean) => void,
  ) {
    const isLogin = await authClient.isValidated()
    if (!isLogin) {
      saveRedirectUrl()
      await authClient.login()
    }
  }

  return {
    handleLoginSuccess,
    handleLogin,
  }
}
