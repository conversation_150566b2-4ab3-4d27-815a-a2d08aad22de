<template>
  <teleport to="body">
    <!-- Overlay (only for modal dialogs) -->
    <div
      v-show="visible"
      :class="{ 'no-overlay': !isModal }"
      class="dialog-overlay"
      @click.self="onCancel"
    >
      <!-- Dialog Box -->
      <div
        ref="dialogContent"
        :class="{ minimized: isMinimized }"
        :style="{
          transform: `translate(${posX}px, ${posY}px)`,
          ...(dialogSchema?.style?.main || {})
        }"
        class="dialog-content"
        tabindex="-1"
        @keydown.esc="onCancel"
      >
        <!-- Dialog Header (draggable area) -->
        <div v-show="dialogSchema.showHeader ?? true" class="dialog-header" @mousedown="startDrag" :style="dialogSchema?.style?.header">
          <span>{{ dialogSchema?.title || '' }}</span>
          <div class="dialog-actions">
            <button v-show="dialogSchema.showMinimize ?? true" @click="toggleMinimize">{{ isMinimized ? '&#x25A2;' : '–' }}</button>
            <button v-show="dialogSchema.showClose ?? true" @click="onCancel">✕</button>
          </div>
        </div>
        <!-- Dialog Body (clickable content) -->
        <div
          v-show="!isMinimized && dialogSchema.body"
          :style="dialogSchema?.style?.body"
          class="dialog-body"
        >
          <div class="body-content" :style="dialogSchema?.style?.content">
            <component
              :is="loadComponent(child?.type)"
              v-for="(child, index) in dialogSchema.body || []"
              :key="`panel-child-${index}`"
              :context="dialogContext"
              :schema="child"
            />
          </div>
          <div v-if="dialogSchema.foot" class="foot-container" @mousedown="startDrag" :style="dialogSchema?.style?.foot">
            <component
              :is="loadComponent(child?.type)"
              v-for="(child, index) in dialogSchema.foot || []"
              :key="`panel-child-${index}`"
              :context="dialogContext"
              :schema="child"
            />
          </div>
        </div>
      </div>
    </div>
    <!-- Error Popup -->
    <div v-if="error" class="error-popup">
      <div class="error-popup-content">
        <span>{{ error }}</span>
        <button @click="closeErrorPopup">Close</button>
      </div>
    </div>
  </teleport>
</template>

<script lang="ts" setup>
import { computed, nextTick, provide, ref, watch } from 'vue'
import { loadComponent } from '@/components/componentRegistry'

// Define DialogSchema type
interface DialogSchema {
  title?: string
  showMinimize?: boolean
  showClose?: boolean
  showHeader?: boolean
  body?: any[]
  foot?: any[]
  style?: {
    main?: any
    content?: any
    body?: any
  }
  event?: { name: string }
  modal?: boolean // Controls whether the dialog is modal
}

// Reactive variables
const visible = ref(false),
  isValid = ref(false),
  dialogSchema = ref<DialogSchema>({}),
  dialogContext = ref({}),
  isMinimized = ref(false),
  posX = ref(0),
  posY = ref(0),
  error = ref<string | null>(null)
let resolveDialogPromise: (() => void) | null = null
const dialogContent = ref<HTMLElement | null>(null)

// Computed property: whether the dialog is modal
const isModal = computed(() => dialogSchema.value.modal !== false) // Defaults to true

// Watch for visibility changes
watch(visible, async (newValue) => {
  if (!newValue) {
    return resolveDialogPromise?.()
  }
  const schema = dialogSchema.value
  dialogSchema.value = {}
  await nextTick()

  dialogSchema.value = schema
  await nextTick()

  if (!dialogContent.value?.contains(document.activeElement)) dialogContent.value?.focus()
  await ensureDialogVisible()
})

// Open the dialog
async function dialog(context: any, schema: DialogSchema) {
  try {
    dialogSchema.value = schema
    dialogContext.value = context
    visible.value = true
    await new Promise<void>((resolve) => (resolveDialogPromise = resolve))
    resolveDialogPromise = null
    return isValid.value ? context : null
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'An unexpected error occurred'
    visible.value = false
    return null
  }
}

// Close the dialog
const closeDialog = (valid: boolean) => ((isValid.value = valid), (visible.value = false))
const onCancel = () => closeDialog(false)

// Toggle minimize state
const toggleMinimize = async () => {
  isMinimized.value = !isMinimized.value
  await ensureDialogVisible()
}

// Drag functionality
let isDragging = ref(false)
const startDrag = (e: MouseEvent) => {
  isDragging.value = true
  const startX = e.clientX - posX.value
  const startY = e.clientY - posY.value

  const onDrag = (e: MouseEvent) => {
    if (!isDragging.value) return
    posX.value = e.clientX - startX
    posY.value = e.clientY - startY
    ensureDialogWithinViewport()
  }

  const stopDrag = () => {
    isDragging.value = false
    document.removeEventListener('mousemove', onDrag)
    document.removeEventListener('mouseup', stopDrag)
  }

  document.addEventListener('mousemove', onDrag)
  document.addEventListener('mouseup', stopDrag)
}

// Ensure dialog stays within viewport
const ensureDialogWithinViewport = () => {
  if (!dialogContent.value) return

  const dialogRect = dialogContent.value.getBoundingClientRect()
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight

  if (dialogRect.left < 0) {
    posX.value -= dialogRect.left
  } else if (dialogRect.right > viewportWidth) {
    posX.value -= dialogRect.right - viewportWidth
  }

  if (dialogRect.top < 0) {
    posY.value -= dialogRect.top
  } else if (dialogRect.bottom > viewportHeight) {
    posY.value -= dialogRect.bottom - viewportHeight
  }
}

const ensureDialogVisible = async () => {
  await nextTick()
  ensureDialogWithinViewport()
}

// Close error popup
const closeErrorPopup = () => (error.value = null)
// Provide global methods
provide('cancel-dialog', () => closeDialog(false))
provide('confirm-dialog', () => closeDialog(true))
defineExpose({ compName: dialogSchema.value?.type || '-', dialog })
</script>

<style lang="scss" scoped>
.dialog-overlay {
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  inset: 0;
  z-index: 1000;
  background-color: rgba(0, 0, 0, 0.1);

  &.no-overlay {
    background-color: transparent; // No overlay for non-modal dialogs
    pointer-events: none; // Allow clicks to pass through
  }
}

.dialog-content {
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 4px;
  overflow: hidden;
  transition: height 0.3s ease;
  pointer-events: auto; // Ensure dialog content is clickable
  border: 1px solid lightgray;
  //outline: none;

  &.minimized {
    min-height: unset;
    min-width: 20rem;
  }
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  cursor: move; // Indicate draggable area

  .dialog-actions {
    display: flex;
    gap: 3px;

    button {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 25px;
      height: 25px;
      border: 1px solid rgb(255, 255, 255);
      border-radius: 5px;
      background: rgb(255, 255, 255);

      &:hover {
        background: lightgray;
      }
    }
  }
}

.dialog-body {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  overflow: auto;
  padding: 5px;

  .body-content {
    flex-direction: column;
    flex: 1 1 auto;
    overflow: auto;
  }

  .foot-container {
    display: flex;
    flex: 0;
    justify-content: flex-end;
    margin-bottom: 10px;
  }
}

.error-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(136, 136, 136, 0.5);
  z-index: 1001;

  .error-popup-content {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    gap: 10px;

    button {
      align-self: flex-end;
    }
  }
}
</style>
