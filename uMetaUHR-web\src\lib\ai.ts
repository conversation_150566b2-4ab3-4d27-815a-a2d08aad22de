import axios from 'axios'
import { dbGet } from '@/lib/dataMan'

declare type AIMessage = {
  role: string
  content: string
}

export async function useAI(messages: AIMessage[], config: any = null, myRole = 'user') {
  messages = messages
    .filter((x) => x.content && x.role)
    .map((x) => {
      // eslint-disable-next-line prefer-const
      let { role, content }: { role: string; content: string } = x as any
      if (role === myRole) role = 'user'
      if (!['user', 'system', 'assistant'].includes(role)) role = 'assistant'
      return { role, content }
    }) as any

  try {
    const response = await axios.post(
      'http://localhost:8080/ai/openai/req',
      { messages },
      {
        timeout: 10 * 60 * 1000 // 5 minutes in milliseconds
      }
    )
    const { data: { choices: [{ message: { role, content } = {} as any } = {}] = [] } = {} } =
      response
    return { content }
  } catch (error: any) {
    console.error(error)
    await (window as any).popAlert({ message: `错误结果：${JSON.stringify(error?.message)}` })
    return {}
  }
}

const agents = {} as any

export async function useAgents(agentNames: string[]) {
  for (const role of agentNames) {
    agents[role] = await dbGet({
      sql: "select * from agent where name = '" + role + "'"
    })
  }
}

export async function getAgent(name: string): Promise<any> {
  if (!agents[name]) {
    await useAgents([name])
  }
  return agents[name]
}
