import type { Diagnosis, MetaPiniaData } from './metaPinia-types'
import { createMetaPinia } from './metaPinia'

interface DiagnosisData extends MetaPiniaData {
  diagnoses: Diagnosis[]
}

/**
 * 诊断数据管理扩展
 */
export function createDiagnosisStore(initialData: DiagnosisData) {
  const med = createMetaPinia(initialData)

  // 诊断专用操作扩展
  const diagnosisApi = {
    ...med,

    /**
     * 添加诊断
     */
    addDiagnosis(code: string, description: string): Diagnosis {
      if (!med.data.currentPatient) {
        throw new Error('没有当前患者')
      }

      const newDiagnosis: Diagnosis = {
        id: `diag_${Date.now()}`,
        patientId: med.data.currentPatient.id,
        code,
        description,
        date: new Date()
      }

      if (!med.data.diagnoses) med.data.diagnoses = []
      med.data.diagnoses.push(newDiagnosis)
      med.logAction(`添加诊断 ${code}: ${description}`)
      return newDiagnosis
    },

    /**
     * 更新诊断描述
     */
    updateDiagnosisDescription(diagnosisId: string, newDescription: string): boolean {
      const diagnosis = med.data.diagnoses?.find((d: any) => d.id === diagnosisId)
      if (!diagnosis) return false

      const oldDescription = diagnosis.description
      diagnosis.description = newDescription
      med.logAction(`更新诊断描述 ${diagnosis.code}: ${oldDescription} → ${newDescription}`)
      return true
    },

    /**
     * 获取患者的所有诊断
     */
    getPatientDiagnoses(patientId: string): Diagnosis[] {
      return med.data.diagnoses?.filter((d: any) => d.patientId === patientId) || []
    },

    /**
     * 获取所有诊断列表
     */
    getDiagnosisList(): Diagnosis[] {
      return med.data.diagnoses || []
    }
  }

  return diagnosisApi
}

export type DiagnosisStore = ReturnType<typeof createDiagnosisStore>
