<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon/工具栏/下一个</title>
    <defs>
        <path d="M24,0 L24.0008383,13.7572782 C22.7050921,11.51151 20.2790621,10 17.5,10 C13.3578644,10 10,13.3578644 10,17.5 C10,18.3770091 10.1505299,19.2188601 10.4271621,20.0011254 L0,20 L0,0 L24,0 Z" id="path-1"></path>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#337DFF" offset="0%"></stop>
            <stop stop-color="#9CC3FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-4">
            <stop stop-color="#262626" offset="0%"></stop>
            <stop stop-color="#8C8C8C" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-5">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#9CC3FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-6">
            <stop stop-color="#262626" offset="0%"></stop>
            <stop stop-color="#8C8C8C" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-7">
            <stop stop-color="#262626" offset="0%"></stop>
            <stop stop-color="#9B9B9B" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="icon/工具栏/下一个" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="icon/UI-5.0/toolbar/开始叫号" transform="translate(0, -0)">
            <mask id="mask-2" fill="white">
                <use xlink:href="#path-1"></use>
            </mask>
            <g id="蒙版"></g>
            <g mask="url(#mask-2)">
                <path d="M5.5,12.5 L5.5,13 C5.5,14.4025375 6.14578475,15.6904636 7.20952958,16.5311859 L5.86708376,19.2594512 C3.86706233,17.937895 2.59585945,15.726016 2.50519982,13.2808154 L2.50203239,12.5 L5.5,12.5 Z" id="路径" stroke="url(#linearGradient-4)" fill="url(#linearGradient-3)"></path>
                <path d="M7.5,4.5 L7.5,12.5 L2,12.5 C1.58578644,12.5 1.21078644,12.3321068 0.939339828,12.0606602 C0.667893219,11.7892136 0.5,11.4142136 0.5,11 L0.5,6 C0.5,5.58578644 0.667893219,5.21078644 0.939339828,4.93933983 C1.21078644,4.66789322 1.58578644,4.5 2,4.5 L7.5,4.5 Z" id="矩形" stroke="url(#linearGradient-6)" fill="url(#linearGradient-5)"></path>
                <circle id="椭圆形" stroke="url(#linearGradient-7)" fill="#9CC3FF" cx="15.5" cy="8.5" r="4"></circle>
                <path d="M21,8 L24,8 L24,9 L21,9 Z M20.8705905,5.01703709 L23.768368,4.24057995 L24.027187,5.20650578 L21.1294095,5.98296291 Z" id="形状结合" fill="#545454"></path>
                <path d="M17.5,0.713849996 L17.5,16.28615 L7.5,12.6497864 L7.5,4.35021363 L17.5,0.713849996 Z" id="矩形备份" stroke="#545454" fill="#FFFFFF"></path>
            </g>
        </g>
        <circle id="椭圆形" fill="#337DFF" cx="17.5" cy="17.5" r="6.5"></circle>
        <path d="M18.2222222,14.6111111 L21.8333333,17.5 L18.2222222,20.3888889 L18.2218889,18.2221111 L13.8888889,18.2222222 L13.8888889,16.7777778 L18.2218889,16.7771111 L18.2222222,14.6111111 Z" id="形状结合" fill="#FFFFFF"></path>
    </g>
</svg>