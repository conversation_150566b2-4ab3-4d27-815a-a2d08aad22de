<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div ref="containerRef" :style="schema?.style?.main" class="field-w-layout side_by_side_3">
    <!-- Left Panel (fixed width) -->
    <div
      v-if="schema?.children?.[0]"
      ref="leftPanel"
      :style="{ ...schema?.style?.child1, width: leftWidth + 'px' }"
      class="panel left-panel"
    >
      <component
        :is="loadComponent(schema?.children?.[0]?.type)"
        :context="context"
        :schema="schema?.children?.[0]"
      />
    </div>

    <!-- Left Divider -->
    <div
      v-if="isLeftResizable && schema?.children?.[0]"
      class="resize-handle left-handle"
      @mousedown="startLeftDrag"
    >
      <div class="field-w-thumb">
        <div></div>
        <div></div>
        <div></div>
      </div>
    </div>

    <!-- Middle Panel (auto width) -->
    <div
      :class="['panel', 'middle-panel', isCollapsed(1, 'collapseState1', 'collapseState2')]"
      :style="schema?.style?.child2"
    >
      <component
        :is="loadComponent(schema?.children?.[1]?.type)"
        v-if="schema?.children?.[1]"
        :context="context"
        :schema="schema?.children?.[1]"
        @stack-collapse-status="(p: any) => handleStackCollapseStatus(1, p)"
      />
    </div>

    <!-- Right Divider -->
    <div v-if="hasRightPanel" class="resize-handle right-handle" @mousedown="startRightDrag">
      <div class="field-w-thumb">
        <div></div>
        <div></div>
        <div></div>
      </div>
    </div>

    <!-- Right Panel (fixed width) -->
    <div
      v-if="hasRightPanel"
      ref="rightPanel"
      :class="['panel', 'right-panel', isCollapsed(2, 'collapseState1', 'collapseState2')]"
      :style="{
        ...schema?.style?.child3,
        ...(!isCollapsed(2) ? { width: rightWidth + 'px' } : {})
      }"
    >
      <component
        :is="loadComponent(schema?.children?.[2]?.type)"
        :context="context"
        :schema="schema?.children?.[2]"
        @stack-collapse-status="(p: any) => handleStackCollapseStatus(2, p)"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { loadComponent } from '@/components/componentRegistry'
import { cacheManager } from '@/lib/CacheManager'
import { Debouncer } from '@/lib/Debouncer' // --- Component Props ---

// --- Component Props ---
const props = defineProps({
  /** Layout schema configuration */
  schema: Object,
  /** Component context data */
  context: Object,
  /** Custom style overrides */
  style: Object
})

// --- Template Refs ---
const containerRef = ref<HTMLElement | null>(null)
const leftPanel = ref<HTMLElement | null>(null)
const rightPanel = ref<HTMLElement | null>(null)

// --- Panel Configuration ---
const myFavoriteId = props.schema?.myFavoriteId
const MIN_PANEL_WIDTH = 50
const DEFAULT_LEFT_WIDTH = 250
const DEFAULT_RIGHT_WIDTH = 200
const leftWidth = ref(props.schema?.leftWidth ?? DEFAULT_LEFT_WIDTH)
const rightWidth = ref(DEFAULT_RIGHT_WIDTH)
const hasRightPanel = computed(() => Boolean(props.schema?.children?.[2]))
const isLeftResizable = computed(() => props.schema?.leftWidth === undefined)

// --- Drag Resize State ---
const isDragging = ref(false)
const dragStartX = ref(0)
const dragStartLeftWidth = ref(0)
const dragStartRightWidth = ref(0)
const activeResizeHandle = ref<'left' | 'right'>('left')

// --- Lifecycle Hooks ---
onMounted(async () => loadCachedDimensions())
onUnmounted(() => cleanupDragListeners())

function isCollapsed(idx: number, p1?: any, p2?: any): any {
  const v = collapsedStatuses.value[idx]
  if (!p1 || !p2) return v
  return v ? 'collapseState1' : 'collapseState2'
}

/**
 * Loads previously saved panel dimensions from cache
 */
async function loadCachedDimensions() {
  if (!myFavoriteId) return

  const cachedData = (await cacheManager.get(myFavoriteId)) || {}
  if (cachedData.leftWidth) leftWidth.value = cachedData.leftWidth
  if (cachedData.rightWidth) rightWidth.value = cachedData.rightWidth
}

// --- Panel Resizing Handlers ---
function startLeftDrag(e: MouseEvent) {
  startResize(e, 'left')
}

function startRightDrag(e: MouseEvent) {
  startResize(e, 'right')
}

/**
 * Initializes panel resize operation
 * @param e Mouse event
 * @param handle Which panel is being resized ('left' or 'right')
 */
function startResize(e: MouseEvent, handle: 'left' | 'right') {
  isDragging.value = true
  activeResizeHandle.value = handle
  dragStartX.value = e.clientX
  dragStartLeftWidth.value = leftWidth.value
  dragStartRightWidth.value = rightWidth.value

  document.addEventListener('mousemove', handleResize)
  document.addEventListener('mouseup', stopResize)
  document.body.style.cursor = 'col-resize'
  document.body.style.userSelect = 'none'
}

// Create debouncer instances
const resizeDebouncer = new Debouncer(5) // ~60fps
const saveDebouncer = new Debouncer(500) // 500ms delay for cache saves

/**
 * Handles panel resizing during mouse move (debounced)
 */
const debouncedHandleResize = resizeDebouncer.debounce((e: MouseEvent) => {
  if (!isDragging.value) return

  const containerWidth = containerRef.value?.clientWidth || 1000
  const deltaX = e.clientX - dragStartX.value

  if (activeResizeHandle.value === 'left') {
    leftWidth.value = calculateNewWidth(
      dragStartLeftWidth.value + deltaX,
      containerWidth - rightWidth.value - MIN_PANEL_WIDTH
    )
  } else {
    rightWidth.value = calculateNewWidth(
      dragStartRightWidth.value - deltaX,
      containerWidth - leftWidth.value - MIN_PANEL_WIDTH
    )
  }
})

function handleResize(e: MouseEvent) {
  debouncedHandleResize(e)
}

const collapsedStatuses = ref<Record<number, boolean>>({})

function handleStackCollapseStatus(idx: number, status: boolean) {
  collapsedStatuses.value[idx] = status
  debouncedSaveDimensions()
}

/**
 * Calculates new panel width with constraints
 */
function calculateNewWidth(targetWidth: number, maxWidth: number) {
  return Math.max(MIN_PANEL_WIDTH, Math.min(targetWidth, maxWidth))
}

/**
 * Cleans up after resize operation and saves new dimensions (debounced)
 */
const debouncedSaveDimensions = saveDebouncer.debounce(async () => {
  if (myFavoriteId) {
    await cacheManager.set(myFavoriteId, {
      // collapsedStatuses: collapsedStatuses.value,
      leftWidth: leftWidth.value,
      rightWidth: rightWidth.value
    })
  }
})

async function stopResize() {
  if (!isDragging.value) return
  cleanupDragListeners()
  await debouncedSaveDimensions()
}

/**
 * Removes drag event listeners
 */
function cleanupDragListeners() {
  isDragging.value = false
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
  document.body.style.cursor = ''
  document.body.style.userSelect = ''
}

defineExpose({ compName: 'layoutSideBySide' })
</script>

<style lang="scss" scoped>
.field-w-layout {
  --theme-color: v-bind('schema?.style?.vars?.themeColor || "black"');
  --theme-background-color: v-bind('schema?.style?.vars?.themeBC || "none"');
  --theme-active-color: v-bind('schema?.style?.vars?.themeActiveColor || "white"');
  --theme-active-background-color: v-bind('schema?.style?.vars?.themeActiveBC || "darkblue"');
  --theme-border-color: v-bind('schema?.style?.vars?.themeBorderColor || "none"');
  --theme-content-background-color: v-bind('schema?.style?.vars?.themeContentBC || "white"');

  display: flex;
  flex: 1 1 auto;
  overflow: hidden;
  background: var(--theme-background-color);
  border: 2px solid var(--theme-border-color);
  border-radius: 5px;
}

.panel {
  height: 100%;
  overflow: auto;

  &.left-panel {
    display: flex;
    overflow: auto;
    padding: 0 2px;
  }

  &.right-panel {
    display: flex;
    overflow: auto;
    padding: 0 2px;
  }

  &.middle-panel {
    display: flex;
    flex: 1 1 auto;
    min-width: 100px;
    overflow: auto;
    padding: 0 2px;
  }
}

.resize-handle {
  flex: 0 0 2px;
  background-color: transparent;
  cursor: col-resize;
  align-items: center;
  justify-content: center;
  display: flex;
  width: 2px;
  margin: 0;
  overflow: visible;

  &:hover {
    background-color: #fafafa;

    .field-w-thumb {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 4px;

      div {
        width: 5px;
        height: 5px;
        background-color: lightgrey;
        border-radius: 50%;
        transition: background-color 0.3s ease;
        box-shadow: 1px 1px 3px var(--theme-border-color);
      }
    }
  }
}
</style>
