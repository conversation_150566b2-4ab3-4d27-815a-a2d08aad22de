import axios from 'axios'

export async function dbSvc(
  svcType: string,
  qid: string,
  req: any,
  columns?: string[]
): Promise<any> {
  const req2 = { ...req }
  if (columns && columns.length > 0) {
    req2.columns = columns
  }
  if (svcType?.toLowerCase?.().includes('update')) {
    for (const key in req2) {
      const value = req2[key]
      if (value && typeof value === 'object') {
        req2[key] = JSON.stringify(value)
      }
    }
  }

  const response = await axios.post(svcType, { qid, req: req2 })
  const {
    data: { data }
  } = response
  return data
}
