CREATE TABLE medical_insurance
(
    id                        INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    drug_id                   INT NOT NULL,
    catalog_type              VARCHAR(20) NOT NULL,
    catalog_name              VARCHAR(50) NOT NULL,
    reimbursement_rate        DECIMAL(5, 2),
    reimbursement_restriction TEXT,
    effective_date            DATE        NOT NULL,
    expiry_date               DATE,
    version                   VARCHAR(20),
    created_at                BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
    updated_at                BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
    FOREIGN KEY (drug_id) REFERENCES drug (id)
);

COMMENT ON TABLE medical_insurance IS 'Medical insurance coverage information';
COMMENT ON COLUMN medical_insurance.drug_id IS 'Reference to drug table';
COMMENT ON COLUMN medical_insurance.catalog_type IS 'Catalog type (national/provincial)';
COMMENT ON COLUMN medical_insurance.catalog_name IS 'Catalog name';
COMMENT ON COLUMN medical_insurance.reimbursement_rate IS 'Reimbursement percentage';
COMMENT ON COLUMN medical_insurance.reimbursement_restriction IS 'Reimbursement restrictions';
COMMENT ON COLUMN medical_insurance.effective_date IS 'Date when coverage becomes effective';
COMMENT ON COLUMN medical_insurance.expiry_date IS 'Date when coverage expires';
COMMENT ON COLUMN medical_insurance.version IS 'Insurance catalog version';
COMMENT ON COLUMN medical_insurance.created_at IS 'Timestamp when record was created';
COMMENT ON COLUMN medical_insurance.updated_at IS 'Timestamp when record was last updated';
