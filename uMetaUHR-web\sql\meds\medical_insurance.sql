CREATE TABLE medical_insurance
(
    id                        INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    drug_id                   INT NOT NULL,
    catalog_type              VARCHAR(20) NOT NULL,
    catalog_name              VARCHAR(50) NOT NULL,
    reimbursement_rate        DECIMAL(5, 2),
    reimbursement_restriction TEXT,
    effective_date            BIGINT        NOT NULL,
    expiry_date               BIGINT        DEFAULT NULL,
    version                   VARCHAR(20),
    created_at                BIGINT DEFAULT EXTRACT(EPOCH FROM NOW())*1000,
    updated_at                BIGINT DEFAULT EXTRACT(EPOCH FROM NOW())*1000,
    FOREIGN KEY (drug_id) REFERENCES drug (id)
);

COMMENT ON TABLE medical_insurance IS '医保目录信息表';
COMMENT ON COLUMN medical_insurance.drug_id IS '关联药品ID';
COMMENT ON COLUMN medical_insurance.catalog_type IS '目录类型(国家/地方)';
COMMENT ON COLUMN medical_insurance.catalog_name IS '目录名称';
COMMENT ON COLUMN medical_insurance.reimbursement_rate IS '报销比例';
COMMENT ON COLUMN medical_insurance.reimbursement_restriction IS '报销限制条件';
COMMENT ON COLUMN medical_insurance.effective_date IS '生效日期';
COMMENT ON COLUMN medical_insurance.expiry_date IS '失效日期';
COMMENT ON COLUMN medical_insurance.version IS '医保目录版本';
COMMENT ON COLUMN medical_insurance.created_at IS '记录创建时间';
COMMENT ON COLUMN medical_insurance.updated_at IS '记录最后更新时间';
