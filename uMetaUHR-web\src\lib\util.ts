import type { ComponentInternalInstance } from 'vue'

export function isEmpty(obj: any): boolean {
  if (obj == null) return true
  if (typeof obj !== 'object') return false
  return Object.keys(obj).length === 0
}

export function notEmpty(obj: any): boolean {
  return !isEmpty(obj)
}

const requiredFieldMap = {
  label: {
    label: '',
    content: '',
    model: ''
  }
}

export function getParentExposedByInstance(
  instance: ComponentInternalInstance | null,
  componentName?: string
): any | null {
  if (!instance?.parent) return {}

  let current = instance.parent as any
  while (current) {
    const compName = current.exposed?.compName
    if (componentName ? compName === componentName : compName !== undefined) {
      return current.exposed || {}
    }
    current = current.parent
  }

  return {}
}

export function isElementVisible(element: HTMLElement) {
  if (!(element instanceof HTMLElement)) return false
  if (element.offsetWidth <= 0 || element.offsetHeight <= 0 || element.style.display === 'none')
    return false

  const rect = element.getBoundingClientRect()
  if (
    rect.top < 0 ||
    rect.left < 0 ||
    rect.bottom > (window.innerHeight || document.documentElement.clientHeight) ||
    rect.right > (window.innerWidth || document.documentElement.clientWidth)
  ) {
    return false
  }

  let parentElement = element.parentElement
  while (parentElement) {
    if (getComputedStyle(parentElement).display === 'none') return false
    parentElement = parentElement.parentElement
  }

  return true
}

export async function refreshTocDetailByEvent(
  event: any,
  { stopPropagation, cb, feedback }: any
): Promise<void> {
  if (!event || typeof event !== 'object') {
    console.error('Invalid event provided for refreshTocDetailByEvent.')
    return
  }

  let { component: comp } = event
  while (comp) {
    if (typeof comp.refreshDetail === 'function') {
      comp.refreshDetail()
      break
    }
    comp = comp.$parent
  }
}

export function removeEmptyNodes(obj: any): any {
  function isEmpty(value: any): boolean {
    return (
      value == '未提供' || value == 'NA' || value === null || value === undefined || value === ''
    )
  }

  if (Array.isArray(obj)) {
    const cleanedArray = obj.map(removeEmptyNodes).filter((item) => !isEmpty(item))
    return cleanedArray.length > 0 ? cleanedArray : []
  }

  if (typeof obj === 'object' && obj !== null) {
    const entries = Object.entries(obj).map(([key, value]) => [key, removeEmptyNodes(value)])
    const filteredEntries = entries.filter(([_, value]) => !isEmpty(value))
    if (filteredEntries.length === 0) return {}
    return Object.fromEntries(filteredEntries)
  }

  return obj
}
