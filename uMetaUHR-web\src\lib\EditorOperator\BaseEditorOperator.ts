import { EMR_EDITOR_DEFAULT_OPTION } from '@/service/base/editorConfig';

export class BaseEditorOperator {
    protected editor: any;

    constructor(editorInstance: any) {
        this.editor = editorInstance;
    }


    // 临时：执行SQL查询
    private async executeSQLQuery(sql: string) {
        try {
            // 临时：执行SQL查询 这个url 后续需要换成java的
            const baseUrl = EMR_EDITOR_DEFAULT_OPTION.EMR_EDITOR_URL;
            const url = `${baseUrl}/demo/executeSQL`;
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ sql }),
            });
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const result = await response.json();
            return result;
        } catch (error) {
            console.error('请求出错:', error);
            throw error;
        }
    }

    // 临时：通过文件名和表名获取文件内容
    public async getFileContentFromServerByName(name: string, tableName: string): Promise<Blob> {
        const sqlQuery = `SELECT content FROM ${tableName} WHERE name = '${name}';`;
        try {
            const result = await this.executeSQLQuery(sqlQuery);
            const rows = result.rows;
            if (rows.length > 0) {
                const firstRow = rows[0];
                const content = firstRow[0];
                const base64Prefix = "data:application/octet-stream;base64,";
                const rawData = content.startsWith(base64Prefix)
                    ? content.substring(base64Prefix.length)
                    : content;
                const byteCharacters = atob(rawData);
                const byteNumbers = new Array(byteCharacters.length);
                for (let i = 0; i < byteCharacters.length; i++) {
                    byteNumbers[i] = byteCharacters.charCodeAt(i);
                }
                const byteArray = new Uint8Array(byteNumbers);
                return new Blob([byteArray], { type: "application/apollo-zstd" });
            } else {
                return new Blob();
            }
        } catch (error) {
            console.error('Error inserting base64 string to database:', error);
            return new Blob();
        }
    }

    async openRecord(recordId: string) {
        // 通用打开记录逻辑
        const blob1 = await this.getFileContentFromServerByName(recordId, "businessFile");
        if (this.editor) {
            await this.editor.openDocumentWithStream(blob1);
        }
    }

    //隐藏工具栏项
    async hideToolbarItems(items: string[]) {
        if (this.editor && typeof this.editor.showToolBarItem === 'function') {
            // 构造 {item: false, ...} 对象
            const hideMap = items.reduce((acc, item) => {
                acc[item] = false;
                return acc;
            }, {} as Record<string, boolean>);
            // 转为 JSON 字符串
            const hideMapStr = JSON.stringify(hideMap);
            await this.editor.showToolBarItem(hideMapStr);
        }
    }

    //从外部同步数据到文档
    async syncData(patientId: string): Promise<{[name: string]: {content_text: string}}> {
       return {};
    }

    /**
     * 同步医嘱数据到编辑器
     */
    async syncOrderData(orderData: any[]): Promise<void> {
        try {
            console.log(`[Editor] 同步医嘱数据:`, orderData);
            // 这里实现具体的医嘱数据同步逻辑
            // 例如：将医嘱数据插入到编辑器的指定位置
            // await this.editor.insertContent('orders', orderData);
        } catch (error) {
            console.error(`[Editor] 同步医嘱数据失败:`, error);
        }
    }

    /**
     * 同步诊断数据到编辑器
     */
    async syncDiagnosisData(diagnosisData: any[]): Promise<void> {
        try {
            console.log(`[Editor] 同步诊断数据:`, diagnosisData);
            // 这里实现具体的诊断数据同步逻辑
            // 例如：将诊断数据插入到编辑器的指定位置
            // await this.editor.insertContent('diagnosis', diagnosisData);
        } catch (error) {
            console.error(`[Editor] 同步诊断数据失败:`, error);
        }
    }

    /**
     * 同步检验数据到编辑器
     */
    async syncLabData(labData: any[]): Promise<void> {
        try {
            console.log(`[Editor] 同步检验数据:`, labData);
            // 这里实现具体的检验数据同步逻辑
            // 例如：将检验数据插入到编辑器的指定位置
            // await this.editor.insertContent('labs', labData);
        } catch (error) {
            console.error(`[Editor] 同步检验数据失败:`, error);
        }
    }

    async saveRecord(data: any) {
        // 通用保存逻辑
        if (this.editor && this.editor.saveRecord) {
            return await this.editor.saveRecord(data);
        }
        throw new Error('saveRecord not implemented in editor');
    }
} 