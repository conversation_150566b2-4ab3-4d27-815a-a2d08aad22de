<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div :style="schema!.style?.main" class="tag">
    <label v-if="schema.label">
      <span :style="schema!.style?.label" class="label">{{ schema.label }}</span>
    </label>
    <template v-if="schema.mode === 'display'">
      <span
        v-for="option in schema.options"
        :key="option.value"
        class="tag display"
        v-show="!option.show || getNestedValue(context, option.show)"
        :style="option?.style || {}"
      >
        {{ option.label }}
      </span>
    </template>
    <template v-else>
      <div
      ref="tagsContainer"
      class="tags-input"
      :style="schema!.style?.tagsInput"
      tabindex="0"
      @keydown="handleKeyDown"
      @focus="handleFocus"
      @blur="handleBlur"
      >
        <template v-if="['onoff', 'single'].includes(schema.mode ?? '')">
          <span
            v-for="(option, index) in schema.options"
            :key="option.value"
            :class="{ selected: isSelected(option.value) }"
            class="tag predefined"
            @click="toggleTag(option.value)"
          >
            {{ option.label }}
          </span>
        </template>
        <template v-else>
          <span v-for="(tag, index) in modelValue" :key="index" class="tag">
            {{ tag }}
            <span class="tag-remove" @click="removeTag(index)">×</span>
          </span>
          <input
            v-model="inputValue"
            placeholder="Add a tag"
            type="text"
            @keydown.enter.prevent="addTag"
            @keydown.backspace="handleBackspace"
          />
        </template>
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { computed, defineProps, inject, onMounted, ref } from 'vue'
import { getContext, getNestedValue, setNestedValue } from '@/lib/getContext'
import { cacheManager } from '@/lib/CacheManager'
import { focusNextElement } from '@/lib/focusNextElement'

interface Option { 
  value: string | number; 
  label: string,
  show?: string
}

const props = defineProps<{
  schema: {
    label?: string
    // 数据绑定路径
    model: string
    options: Array<Option>
    // input: 可输入tag， onoff：可多选tag， single：单选tag， display：展示tag
    mode?: 'input' | 'onoff' | 'single' | 'display'
    hkeys: any
    event: any
    myFavoriteId?: any
    style?: {
      main?: string
      label?: string
    }
  }
  context: { [key: string]: any }
}>()

const { myFavoriteId, event } = props.schema
const eventName = event?.name
const act = inject(eventName, () => {}) as Function
const tagsContainer = ref<HTMLElement | null>(null)

const modelValue = computed({
  get: () => getNestedValue(getContext(props), props.schema.model) || [],
  set: (value) => setNestedValue(getContext(props), props.schema.model, value)
})

const currentFocusIndex = ref(-1)

const isSelected = (value: string | number) => modelValue.value.includes(value)

const handleFocus = () => {
  if (['onoff', 'single'].includes(props.schema.mode ?? '')) {
    const selectedIndex = props.schema.options.findIndex((option) => isSelected(option.value))
    currentFocusIndex.value = selectedIndex >= 0 ? selectedIndex : 0
  }
}

const handleBlur = () => {
  currentFocusIndex.value = -1
}

const handleKeyDown = (e: KeyboardEvent) => {
  if (e.key >= '1' && e.key <= '9') {
    const num = parseInt(e.key)
    if (num <= props.schema.options.length) {
      toggleTag(props.schema.options[num - 1].value)
      e.preventDefault()
      focusNextElement(tagsContainer.value!)
    }
  } else if (e.key === 'ArrowRight') {
    e.preventDefault()
    currentFocusIndex.value = Math.min(currentFocusIndex.value + 1, props.schema.options.length - 1)
  } else if (e.key === 'ArrowLeft') {
    e.preventDefault()
    currentFocusIndex.value = Math.max(currentFocusIndex.value - 1, 0)
  } else if (e.key === ' ' && currentFocusIndex.value >= 0) {
    toggleTag(props.schema.options[currentFocusIndex.value].value)
    e.preventDefault()
    focusNextElement(tagsContainer.value!)
  }
}

const toggleTag = async (value: string | number) => {
  const isSingleMode = props.schema.mode === 'single'
  modelValue.value = isSingleMode
    ? isSelected(value)
      ? []
      : [value]
    : isSelected(value)
      ? modelValue.value.filter((v: any) => v !== value)
      : [...modelValue.value, value]
  // Update focus to the newly selected tag
  if (['onoff', 'single'].includes(props.schema.mode ?? '')) {
    const selectedIndex = props.schema.options.findIndex((option) => isSelected(option.value))
    currentFocusIndex.value = selectedIndex >= 0 ? selectedIndex : -1
  }

  act?.({ context: getContext(props), schema: props.schema, value: modelValue.value })

  if (myFavoriteId) {
    const favData = (await cacheManager.get(myFavoriteId)) || {}
    favData.content = JSON.stringify(modelValue.value)
    await cacheManager.set(myFavoriteId, favData)
  }
}

const addTag = () => {
  const trimmedValue = inputValue.value.trim()
  if (trimmedValue && !isSelected(trimmedValue)) {
    modelValue.value = [...modelValue.value, trimmedValue]
    inputValue.value = ''
  }
}

const removeTag = (index: number) => {
  modelValue.value = modelValue.value.filter((_: unknown, i: number) => i !== index)
}

const handleBackspace = () => {
  if (!inputValue.value && modelValue.value.length > 0) {
    modelValue.value = modelValue.value.slice(0, -1)
  }
}

let getDefault = async () => {
  if (!myFavoriteId || (modelValue.value && modelValue.value.length > 0)) return
  const favData = (await cacheManager.get(myFavoriteId)) || {}
  if (favData?.content) modelValue.value = JSON.parse(favData.content)
}

onMounted(async () => {
  await getDefault()
})

defineExpose({
  compName: 'TagComponent',
  addTag,
  removeTag,
  handleBackspace,
  toggleTag
})
</script>

<style lang="scss" scoped>
.tag {
  display: flex;
  flex-direction: row;

  label {
    display: flex;
    flex-wrap: nowrap;
    flex: 0;
    align-items: center;
  }

  .label {
    white-space: nowrap;
    text-align: right;
    padding-right: 0.5rem;
    font-size: 0.9rem;
  }

  .tags-input {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    border-radius: 0.2rem;
    padding: 2px;
    gap: 5px;
    margin: 0;
    outline: none;

    .tag {
      display: inline-flex;
      align-items: center;
      border-radius: 4px;
      font-size: 14px;
      line-height: 22px;
      font-weight: 400;
      cursor: pointer;
      position: relative;
      border: none;
      background: #FFFFFF;

      &.predefined {
        border: 1px solid #CCCCCC;
        color: #202020;

        &.selected {
          border: 1px solid #284DB4;
          color: #284DB4;
        }
      }

      .tag-remove {
        cursor: pointer;
        margin-left: 0.5rem;
        color: #666;
        opacity: 0;
        transition: opacity 0.2s;

        &:hover {
          opacity: 1;
        }
      }
    }

    input {
      flex: 1;
      border: none;
      padding: 0.25rem;
      min-width: 5rem;
    }
  }
}
</style>
