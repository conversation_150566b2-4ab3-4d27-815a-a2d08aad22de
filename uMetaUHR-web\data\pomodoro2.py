import rumps
import argparse
import random
import time
from playsound import playsound
import threading

class PomodoroApp(rumps.App):
    def __init__(self, cycle, work_time, variable_time):
        super(PomodoroApp, self).__init__("Pomodoro")
        self.cycle = cycle
        self.standard_work_time = work_time * 60
        self.variable_time = variable_time * 60
        self.break_time = (self.cycle - work_time) * 60
        self.remaining_time = self.calculate_work_time()
        self.is_work_time = True

        self.menu = ["Start", "Pause", "Reset", "Set Cycle", "Set Work Time", "Set Variable Time"]
        self.timer = rumps.Timer(self.update_time, 1)
        self.minute_timer = rumps.Timer(self.play_short_sound, 60*5)

    def calculate_work_time(self):
        variable_part = self.variable_time * random.uniform(-1, 1)
        return int(self.standard_work_time + variable_part)

    def update_time(self, _):
        if self.remaining_time > 0:
            self.remaining_time -= 1
            self.title = self.format_title()
        else:
            self.timer.stop()
            self.minute_timer.stop()
            if self.is_work_time:
                playsound('end.mp3')
                rumps.notification("Pomodoro Timer", "Work Time's up!", "Take a break!")
                self.remaining_time = self.break_time
                self.is_work_time = False
            else:
                playsound('start.mp3')
                rumps.notification("Pomodoro Timer", "Break Time's up!", "Back to work!")
                self.remaining_time = self.calculate_work_time()
                self.is_work_time = True
            self.title = self.format_title()
            self.timer.start()
            self.minute_timer.start()

    def play_short_sound(self, _):
        playsound('short.mp3')

    def format_title(self):
        status = "📖" if self.is_work_time else "♨️"
        return f"{status} {self.format_time(self.remaining_time)}"

    def format_time(self, seconds):
        minutes = seconds // 60
        seconds = seconds % 60
        return f"{minutes:02}:{seconds:02}"

    @rumps.clicked("Start")
    def on_start(self, _):
        self.timer.start()
        self.minute_timer.start()
        if self.is_work_time:
            playsound('start.mp3')
        else:
            playsound('end.mp3')

    @rumps.clicked("Pause")
    def on_pause(self, _):
        self.timer.stop()
        self.minute_timer.stop()

    @rumps.clicked("Reset")
    def on_reset(self, _):
        self.timer.stop()
        self.minute_timer.stop()
        self.remaining_time = self.calculate_work_time() if self.is_work_time else self.break_time
        self.title = self.format_title()

    @rumps.clicked("Set Cycle")
    def on_set_cycle(self, _):
        response = rumps.Window("Enter cycle time in minutes:", "Set Cycle Time").run()
        if response.clicked:
            try:
                minutes = int(response.text)
                self.cycle = minutes
                self.break_time = (self.cycle - self.standard_work_time // 60) * 60
                if not self.is_work_time:
                    self.remaining_time = self.break_time
                    self.title = self.format_title()
            except ValueError:
                rumps.alert("Invalid input. Please enter a valid number.")

    @rumps.clicked("Set Work Time")
    def on_set_work_time(self, _):
        response = rumps.Window("Enter standard work time in minutes:", "Set Work Time").run()
        if response.clicked:
            try:
                minutes = int(response.text)
                self.standard_work_time = minutes * 60
                if self.is_work_time:
                    self.remaining_time = self.calculate_work_time()
                    self.title = self.format_title()
            except ValueError:
                rumps.alert("Invalid input. Please enter a valid number.")

    @rumps.clicked("Set Variable Time")
    def on_set_variable_time(self, _):
        response = rumps.Window("Enter variable time in minutes:", "Set Variable Time").run()
        if response.clicked:
            try:
                minutes = int(response.text)
                self.variable_time = minutes * 60
                if self.is_work_time:
                    self.remaining_time = self.calculate_work_time()
                    self.title = self.format_title()
            except ValueError:
                rumps.alert("Invalid input. Please enter a valid number.")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Pomodoro Timer")
    parser.add_argument("--cycle", "-c", type=int, default=30, help="Total cycle time in minutes")
    parser.add_argument("--work_time", "-w", type=int, default=25, help="Standard work time in minutes")
    parser.add_argument("--variable_time", "-v", type=int, default=5, help="Variable work time in minutes")
    args = parser.parse_args()

    app = PomodoroApp(args.cycle, args.work_time, args.variable_time)
    app.run()
