import { DateTime } from 'luxon'
import * as Handlebars from 'handlebars'
import { getCodeMapFromMetadata } from '@/lib/metadataUtils'
import { age } from '@/lib/workflow-utils'

export function registerHandlebarsHelpers(handlebars: typeof Handlebars) {
  /**
   * Date formatting helper using Luxon
   * @param timestamp Unix timestamp in seconds
   * @param format Luxon format string
   */
  handlebars.registerHelper('formatDate', (timestamp: number, format: string) => {
    try {
      if (!timestamp) return 'N/A'
      return DateTime.fromSeconds(timestamp).toFormat(format)
    } catch (e: any) {
      return `<span class="error">Date format error: ${e.message}</span>`
    }
  })

  /**
   * Age calculation helper using Luxon
   * @param timestamp Unix timestamp in seconds
   * @param referenceTime Reference time in epoch seconds (defaults to now)
   * @param format Output format - 'year', 'month' or 'day' (defaults to 'year')
   */
  handlebars.registerHelper('age', age)
  // /**
  //  * Equality check helper with type safety
  //  */
  // handlebars.registerHelper('eq', function<T>(this: any, a: T, b: T, options: Handlebars.HelperOptions) {
  //   return a === b ? options.fn(this) : options.inverse(this);
  // });

  /**
   * Appointment status display helper with enum type
   */
  handlebars.registerHelper('codeValue', async (statusCode: string, metadata: string) => {
    try {
      const codeMap = await getCodeMapFromMetadata(metadata)
      return codeMap[statusCode]
    } catch (e) {
      return `<span class="error">Code-Value fetch error: ${e instanceof Error ? e.message : String(e)}</span>`
    }
  })
}
