import { ref } from 'vue'

export default function useValidation() {
  const error = ref('')

  function validate(value: any, rules: { required: boolean }) {
    if (rules?.required) {
      if (
        value === undefined ||
        value === null ||
        (typeof value === 'string' && value.trim() === '') ||
        (Array.isArray(value) && value.length === 0)
      ) {
        error.value = 'This field is required'
        return false
      }
    }
    error.value = ''
    return true
  }

  function clearValidation() {
    error.value = ''
  }

  return {
    error,
    validate,
    clearValidation
  }
}
