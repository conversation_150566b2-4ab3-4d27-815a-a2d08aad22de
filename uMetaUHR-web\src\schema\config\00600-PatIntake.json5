{
  type: 'patIntake',
  ui: {
    type: 'tocDetail',
    style: {
      main: {
        background: '#EEE'
      },
      toc: {
        width: '10rem'
      }
    },
    toc: {
      type: 'toc',
      columnDef: [
        {
          field: 'id',
          displayName: 'id'
        },
        {
          field: 'name',
          displayName: '名称'
        }
      ],
      selectedLine: 0
    },
    detail: {
      type: 'layout',
      theme: 'side_by_side_2',
      style: {
        child2: {
          border: 'none',
          width: '30rem'
        }
      },
      children: [
        {
          body: [
            {
              type: 'section',
              title: '基本信息',
              children: [
                {
                  type: 'inputText',
                  label: 'ID',
                  model: 'id'
                },
                {
                  type: 'inputText',
                  label: '名称',
                  model: 'name'
                }
              ]
            },
            {
              type: 'section',
              title: '2.预约 / 3.预问诊',
              children: [
                {
                  type: 'toolbar',
                  children: [
                    {
                      type: 'button',
                      event: {
                        name: 'agent-appointment'
                      },
                      label: '预约助手分析'
                    },
                    {
                      type: 'button',
                      event: {
                        name: 'agent-intake'
                      },
                      label: '预问诊助手分析'
                    },
                    {
                      type: 'button',
                      event: {
                        name: 'agent-collect-idea'
                      },
                      label: '收集意见'
                    }
                  ]
                },
                {
                  type: 'dataViewer',
                  model: 'data.chat'
                }
              ]
            },
            {
              type: 'section',
              title: '分析',
              children: [
                {
                  type: 'richTextEditor',
                  model: 'data.note',
                  label: '要点笔记'
                }
              ]
            },
            {
              type: 'section',
              title: '数据',
              children: [
                {
                  type: 'dataViewer',
                  model: ''
                }
              ]
            }
          ],
          head: [
            {
              type: 'toolbar',
              children: [
                {
                  type: 'button',
                  event: {
                    name: 'toc-new-data'
                  },
                  label: '新建'
                },
                {
                  type: 'button',
                  event: {
                    name: 'toc-save-data'
                  },
                  label: '保存'
                },
                {
                  type: 'button',
                  event: {
                    name: 'toc-delete-data'
                  },
                  label: '删除'
                }
              ]
            }
          ],
          type: 'window',
          subContext: {
            path: 'detail',
            default: {}
          }
        },
        {
          type: 'section',
          style: {
            main: {
              margin: 0,
              flex: '1 1 auto'
            },
            body: {
              padding: 0,
              flex: '1 1 auto'
            }
          },
          title: '1.问诊',
          children: [
            {
              type: 'external',
              style: {
                width: '30rem'
              },
              externalSchema: '00610-ChatBox',
              subContext: {
                path: 'detail.data',
                default: {}
              }
            }
          ]
        }
      ]
    }
  },
  subContext: {
    path: '/temp.patIntake',
    default: {}
  }
}
