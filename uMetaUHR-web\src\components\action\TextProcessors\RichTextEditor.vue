<template>
  <div
    :class="['rich-text-editor', schema?.theme]"
    :style="schema?.style?.main"
    @keydown="handleKeydown($event)"
  >
    <span v-if="schema?.label" class="label">{{ schema?.label }}</span>
    <component
      :is="loadComponent('ckEditor')"
      ref="ckeditor"
      v-model="editorData"
      :config="editorConfig"
      :editor="editor"
      @blur="handleBlur"
      @focus="handleFocus"
      @input="handleInput"
      @ready="onEditorReady"
    />
    <WnDialog ref="dialog" />
  </div>
</template>

<script lang="ts" setup>
import { computed, defineProps, inject, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'
import ClassicEditor from '@ckeditor/ckeditor5-build-classic'
import { loadComponent } from '@/components/componentRegistry'
import { getContext, getNestedValue, setNestedValue } from '@/lib/getContext'
import { Debouncer } from '@/lib/Debouncer'
import WnDialog from '@/components/containers/WnDialog.vue'
import { parseInput } from '@/lib/pegParsers/SmartCommandParser'
import { cacheManager } from '@/lib/CacheManager'
import { marked } from 'marked'
import eventBus from '@/components/mixins/eventBus'
import { CommandManager } from '@/components/action/commandFlow/CommandFlowManager'
import { smartCommand } from '@/lib/aiLib/smart-command'

// Refs
const dialog = ref<InstanceType<typeof WnDialog>>()
const editorInstance = ref<any>(null)
const editorData = ref<string>('')
const oldValue = ref<string>('')

// Props
const props = defineProps<{
  label?: string
  model?: string
  schema: {
    type: string
    theme?: string
    label?: string
    myFavoriteId: string
    model?: string
    eventEndPoint?: string
    shouldNotGroupWhenFull?: boolean
    event?: {
      editorOnChange?: string
    }
  }
  context: { [key: string]: any }
}>()

// Computed
const modelValue = computed(
  () => props.schema?.model && getNestedValue(props.context, props.schema.model)
)

const shouldNotGroupWhenFull = !!props.schema?.shouldNotGroupWhenFull

// CKEditor configuration
const editor = ClassicEditor
const editorConfig = {
  toolbar: {
    items: [
      'heading',
      '|',
      'bold',
      'italic',
      'bulletedList',
      'numberedList',
      '|',
      'outdent',
      'indent',
      '|',
      'blockQuote',
      'insertTable'
    ],
    shouldNotGroupWhenFull
  }
}

// Constants
const TOOL_INSERT_MARKER = 'tool-insert-marker'

// Watchers
watch(modelValue, (newValue) => {
  editorData.value = newValue
})

// Debounced save function
const saveFavText = new Debouncer(5000).debounce(async (value: string) => {
  const myFavoriteId = props.schema.myFavoriteId
  if (!myFavoriteId) return

  const favData = (await cacheManager.get(myFavoriteId)) || {}
  favData.content = value
  await cacheManager.set(myFavoriteId, favData)
})

// Event Handlers
async function handleInput(value: string) {
  if (value === oldValue.value) return

  props.schema?.model && setNestedValue(props.context, props.schema.model, value)
  oldValue.value = value

  await saveFavText(value)
}

function handleFocus() {
  oldValue.value = editorData.value
}

async function handleBlur() {
  if (oldValue.value !== editorData.value) {
    await editorOnChange()
  }
}

const commandManager = inject<CommandManager>('commandManager')

let smartCommand2 = inject('smart-command', async (data: any, schema: any, ...rest: any) => {
  const context = getContext(props)
  return await smartCommand(data, context, props.schema, { commandManager })
})

function handleKeydown(event: KeyboardEvent) {
  // Check for "Command + J" (Mac) or "Ctrl + J" (Windows/Linux)
  if ((event.metaKey || event.ctrlKey) && event.key === 'j') {
    event.preventDefault() // Prevent the default action

    // Trigger the editorReadyCheck logic directly in "command-trigger" mode
    const editor = editorInstance.value
    if (editor) {
      editorReadyCheck(editor, 'command-trigger') // Use "command-trigger" mode
    }
  }
}

function insertTextAtCursor(text: string) {
  const editor = editorInstance.value
  if (!editor || editor.isReadOnly) {
    console.error('Editor is not initialized or is in readonly mode.')
    return
  }

  editor.model.change((writer: any) => {
    const result = marked.parse(text)
    const selection = editor.model.document.selection
    const viewFragment = editor.data.processor.toView(result)
    const modelFragment = editor.data.toModel(viewFragment)
    const { end } = editor.model.insertContent(modelFragment, selection)
    const range = writer.createRange(end, end)
    writer.setSelection(range)
  })
}

// Editor Functions
function insertResultAtMarker(result: string, range: any) {
  const editor = editorInstance.value
  if (!editor || editor.isReadOnly) {
    console.error('Editor is not initialized or is in readonly mode.')
    return
  }

  const marker = editor.model.markers.get(TOOL_INSERT_MARKER)
  if (!marker) {
    console.error('Marker not found.')
    return
  }

  editor.model.change((writer: any) => {
    const viewFragment = editor.data.processor.toView(result)
    const modelFragment = editor.data.toModel(viewFragment)
    const { end } = editor.model.insertContent(modelFragment, range)
    writer.updateMarker(TOOL_INSERT_MARKER, { range: writer.createRange(end, end) })
    writer.setSelection(writer.createRange(end, end))
  })

  editorData.value = editor.getData()
}

function focusEditorAtMarker() {
  nextTick(() => {
    const editor = editorInstance.value
    const marker = editor.model.markers.get(TOOL_INSERT_MARKER)

    if (!marker) {
      console.error('Marker not found when focusing editor.')
      return
    }

    const position = marker.getStart()
    editor.model.change((writer: any) => {
      writer.setSelection(position)
      writer.removeMarker(TOOL_INSERT_MARKER)
    })

    editor.editing.view.focus()
  })
}

function onEditorReady(editor: any) {
  editorInstance.value = editor
  editor.model.document.on('change:data', () => editorReadyCheck(editor))
}

const editorReadyCheck = new Debouncer(300).debounce(async (editor: any, mode = 'editor-match') => {
  const selection = editor.model.document.selection
  const position = selection.getFirstPosition()

  if (!position) return
  let startPosition = position.getShiftedBy(-Math.min(position.offset, 5))
  if (!startPosition) return

  let range = editor.model.createRange(startPosition, position)
  const textBeforeCursor = Array.from(range.getItems())
    .filter((item: any) => item.is('$textProxy'))
    .map((item: any) => item.data)
    .join('')

  let triggerSeqLen = 0
  if (mode === 'editor-match') {
    const regex = /(\.ai |。爱)$/
    const [, triggerSeq] = textBeforeCursor.match(regex) || []
    if (!triggerSeq) return
    triggerSeqLen = triggerSeq.length
  }

  startPosition = position.getShiftedBy(-triggerSeqLen)
  range = editor.model.createRange(startPosition, position)

  if (editor.model.markers.has(TOOL_INSERT_MARKER)) return

  editor.model.change((writer: any) => {
    writer.addMarker(TOOL_INSERT_MARKER, {
      range: writer.createRange(startPosition, startPosition),
      usingOperation: false,
      affectsData: false
    })
  })

  let userInput = `ai cmd 1000
  按要求完成任务。
  `
  if (mode === 'editor-match') {
    const { default: dialogSchema = {} } =
      (await import('./editorPlugins/001-RichTextEditor-dialog.json')) || {}
    const dRes = await dialog.value!.dialog({}, dialogSchema)
    if (!dRes) {
      insertResultAtMarker('', range)
      focusEditorAtMarker()
      return
    }
    userInput = dRes?.userInput || ''
  }

  const root = editor.model.document.getRoot()
  const allTextBeforeCursor = getTextBetweenPositions(
    editor,
    editor.model.createPositionAt(root, 0),
    position
  )
  const allTextAfterCursor = getTextBetweenPositions(
    editor,
    position,
    editor.model.createPositionAt(root, 'end')
  )

  let result = ''
  try {
    let structuredData = {
      beforeCursor: allTextBeforeCursor.substring(0, allTextBeforeCursor.length - triggerSeqLen),
      userInput,
      afterCursor: allTextAfterCursor
    }
    const commands = await parseInput(structuredData.userInput.trim())
    const context = getContext(props)
    const schema = props.schema
    const rst = await smartCommand2({ ...structuredData, commands }, context, schema)
    const { text = '' } = rst || {}
    result = await marked.parse(text || '')
  } catch (error) {
    console.error('Error in startTool:', error)
    window.popAlert({ message: (error as any).message })
  }

  insertResultAtMarker(result, range)
  focusEditorAtMarker()
})

function getTextBetweenPositions(editor: any, startPosition: any, endPosition: any) {
  const range = editor.model.createRange(startPosition, endPosition)
  return Array.from(range.getItems())
    .filter((item: any) => item.is('$textProxy'))
    .map((item: any) => item.data)
    .join('\n')
}

const editorOnChange = inject(props.schema?.event?.editorOnChange || 'editor-on-change', () =>
  Promise.resolve()
) as Function

onMounted(async () => {
  editorData.value = modelValue.value || (await getFavoriteContent())
  props.schema?.model && setNestedValue(props.context, props.schema.model, editorData.value)

  let eventEndPoint = props.schema?.eventEndPoint as string
  if (!eventEndPoint) return
  eventBus.on(eventEndPoint, handleEventEndPointEvent)
})

onUnmounted(() => {
  let eventEndPoint = props.schema?.eventEndPoint as string
  if (!eventEndPoint) return
  eventBus.off(eventEndPoint, handleEventEndPointEvent)
})

function handleEventEndPointEvent(text: string) {
  if (!text) return
  insertTextAtCursor(text)
}

async function getFavoriteContent() {
  const myFavoriteId = props.schema.myFavoriteId
  if (!myFavoriteId) return ''

  const favData = (await cacheManager.get(myFavoriteId)) || {}
  return favData.content || ''
}

defineExpose({ compName: props?.schema?.type || '-' })
</script>

<style scoped>
.rich-text-editor {
  /*height: 20rem;*/
}

/* CKEditor specific styles */
</style>

<style lang="scss">
.rich-text-editor {
  //flex: 1 1 auto;
  //border: 10px solid greenyellow;
  //overflow: hidden;
  //display: flex;
  //display: block;
  //height: 100%;
}

.ck-editor__editable_inline:not(.ck-comment__input *) {
  //flex: 1 1 auto;
  //border: 10px solid red;
  //overflow: hidden;

  min-height: 20rem;
  //max-height: 300px;
  //overflow-y: auto;
}

.standard_height {
  .ck-editor__editable_inline:not(.ck-comment__input *) {
    //min-height: auto;
    //max-height: none;
    //height: 250px;
    //overflow-y: auto;
  }
}

.ck-powered-by {
  display: none !important;
}

.ck-focused {
  border: 1px solid lightblue !important;
}
</style>
