<template>
  <div class="json-editor">
    <textarea ref="textarea" :value="getContext(props)" />
  </div>
</template>

<script lang="ts" setup>
import { defineEmits, defineProps, onMounted, ref, watch } from 'vue'
import 'script-loader'
import CodeMirror from 'codemirror'
import './CodeMirrorImp'
import { getContext } from '@/lib/getContext'

const props = defineProps({
  jsonCode: {
    type: String,
    default: '{}'
  },
  mode: {
    type: String,
    default: 'json'
  }
})

const emit = defineEmits(['change'])

const textarea = ref<HTMLTextAreaElement | null>(null)
const jsonEditor = ref<any>(null)
const jsonCode = ref(props.jsonCode)

watch(
  jsonCode,
  (newVal) => {
    if (!jsonEditor.value) return
    const editorValue = jsonEditor.value.getValue()
    if (newVal === editorValue) return
    jsonEditor.value.setValue(newVal)

    setTimeout(() => {
      jsonEditor.value.refresh()
    }, 1)
  },
  { immediate: true, deep: true }
)

onMounted(async () => {
  if (!textarea.value) return

  let jc = props.jsonCode || ''
  jsonCode.value = jc

  const modeMap: Record<string, any> = {
    javascript: { mode: 'javascript' },
    json: { mode: 'application/json' },
    html: { mode: 'htmlmixed' },
    markdown: { mode: 'text/x-markdown', theme: 'eclipse' },
    peg: { mode: 'pegjs' }
  }

  const { mode: m1, theme = 'idea' } = modeMap[props.mode] || {}

  jsonEditor.value = CodeMirror.fromTextArea(textarea.value, {
    mode: m1,
    theme, // 主题样式
    lint: true,
    tabSize: 2,
    smartIndent: true, // 是否智能缩进
    styleActiveLine: true, // 当前行高亮
    gutters: ['CodeMirror-lint-markers', 'CodeMirror-foldgutter'],
    lineWrapping: true, // 自动换行
    matchBrackets: true, // 括号匹配显示
    autoCloseBrackets: true, // 输入和退格时成对
    foldGutter: true
  })

  jsonEditor.value.setValue(jsonCode.value)
  jsonEditor.value.on('change', (cm: any) => {
    emit('change', cm.getValue())
  })
  jsonEditor.value.on('blur', (cm: any) => {
    emit('change', cm.getValue())
  })
})

function refresh() {
  jsonEditor.value && jsonEditor.value.refresh()
}

defineExpose({ compName: props?.schema?.type || '-' })
</script>

<style scoped>
.json-editor {
  height: 100%;
  position: relative;
  overflow: auto;
}

/* 高度自适应 */
.json-editor .CodeMirror {
  height: auto;
}

.json-editor .CodeMirror-scroll {
  height: auto;
  overflow-y: hidden;
  overflow-x: auto;
}

.CodeMirror-foldgutter-folded.CodeMirror-guttermarker-subtle {
  color: black !important;
  font-size: 14px;
}

.CodeMirror-foldgutter-open:after {
  color: black !important;
  font-size: 14px;
}

.json-editor .cm-string {
  color: coral !important;
}
</style>
