# 编辑器融合技术方案

## 概述

本方案解决了EMR系统中编辑器与其他业务模块（医嘱、诊断、检验等）的数据同步问题。通过统一的管理机制和Action驱动的数据同步，实现了模块间的松耦合集成。

## 核心架构

### 1. 编辑器核心层封装

#### EmrEditor.vue
编辑器的核心组件，负责编辑器SDK的初始化和操作对象的绑定。

**核心职责：**
- 自动生成唯一DOM容器(div层)：`emr-editor-${Math.random().toString(36).slice(2)}`
- 加载编辑器SDK并初始化编辑器实例
- 创建编辑器操作对象（暂定:门诊或者住院 2个类型）
- 创建的同时，将操作类与 实例类 进行绑定：`new OutpatientEditorOperator(editor)`
- 自动注册到管理器：`editorManager.setEditor(viewId, operator)`

**初始化流程：**

**1. 在模板中使用编辑器**
```vue
<template>
  <div class="my-module">
    <!-- 引入编辑器组件 -->
    <EmrEditor
      :context="editorContext"
      :schema="editorSchema"
    />
  </div>
</template>

<script setup lang="ts">
import EmrEditor from '@/components/action/TextProcessors/EmrEditor.vue'
</script>
```

**2. 编辑器内部初始化流程**
```typescript
// 2.1 加载SDK并初始化编辑器
const vm = await new Editor().init({ dom: editorDiv, src, option })
const editor = await vm.getEditor()

// 2.2 创建操作对象并绑定
const operator = new OutpatientEditorOperator(editor)

// 2.3 注册到管理器
editorManager.setEditor(viewId, operator)
```

### 2. 编辑器实例管理

#### EditorInstanceManager
负责管理所有编辑器实例，提供统一的注册和访问接口。

**核心方法：**
- `setEditor(viewId, operator)` - 注册编辑器到指定位置
- `getEditor(viewId)` - 异步获取编辑器实例（支持等待）
- `setEditorContext(viewId, patientId, documentId, documentType)` - 设置编辑器业务上下文
- `getEditorContext(viewId)` - 获取编辑器业务上下文

**设计特点：**
- 基于viewId的来管理编辑器实例（viewID 可以采用类如 "门诊工作区"、"历史查看区"）
- 支持编辑器实例复用，避免频繁创建销毁
- 随拿随用

### 3. 编辑器操作类

#### BaseEditorOperator
封装编辑器的通用操作，所有具体编辑器操作类的基类。

#### OutpatientEditorOperator
门诊编辑器的具体实现，继承BaseEditorOperator。

**核心方法：**
- `openRecord(recordId)` - 打开指定文书
- `saveRecord(data)` - 保存文书数据
- `syncOrderData(orderData)` - 同步医嘱数据
- `syncDiagnosisData(diagnosisData)` - 同步诊断数据
- `syncLabData(labData)` - 同步检验数据

**备注：**
- 未来需要扩充更多业务方法

## 使用流程

### 1. 业务层使用编辑器
```typescript
// PatientWorkspace.vue - 业务组件中
const operator = await editorManager.getEditor('门诊工作区')
editorManager.setEditorContext('门诊工作区', patientId, documentId, 'outpatient')
await operator.openRecord('门诊病历2.0')
```

### 2. 患者切换处理
```typescript
// 患者切换时更新编辑器上下文
if (editorManager.hasEditor(VIEW_ID)) {
  editorManager.setEditorContext(VIEW_ID, newPatientId, 'DOC001', 'outpatient')
}
```

## 数据同步机制

### 1. Action定义
```typescript
interface editorDataSyncAction {
  type: string           // 'ORDER_ADDED', 'DIAGNOSIS_UPDATED'等
  sourceKey: string      // '医嘱模块', '诊断模块'等
  targetViewId: string   // 目标编辑器ID
  patientId: string      // 患者ID（用于校验）
  entityId?: string      // 实体ID（可选）
  timestamp: number      // 时间戳
}
```

### 2. 同步流程

#### 2.1 业务模块发送Action
```typescript
// 医嘱模块 - 保存医嘱后发送Action
await saveOrderToDatabase(order)
editorDataSyncService.dispatch({
  type: 'ORDER_ADDED',
  sourceKey: '医嘱模块',
  targetViewId: '门诊工作区',
  patientId: currentPatientId,
  entityId: order.id,
  timestamp: Date.now()
})
```

#### 2.2 编辑器自动同步
1. **接收Action** - editorDataSyncService接收并校验Action
2. **患者ID校验** - 确保操作的是正确患者的编辑器
3. **数据库查询** - 根据Action类型从数据库获取最新数据
4. **编辑器更新** - 调用编辑器的同步方法更新内容

### 3. 支持的同步类型
- **医嘱同步** - ORDER_ADDED, ORDER_UPDATED, ORDER_DELETED
- **诊断同步** - DIAGNOSIS_ADDED, DIAGNOSIS_UPDATED, DIAGNOSIS_DELETED  
- **检验同步** - LAB_RESULT_RECEIVED

## 技术特点

### 1. 松耦合设计
- 业务模块只需发送Action，无需了解编辑器内部实现
- 编辑器根据Action自主决定获取哪些数据字段
- 模块间通过Action通信，避免直接依赖

### 2. 数据一致性
- 编辑器始终从数据库获取最新数据
- 业务模块先保存到数据库再发送Action
- 通过患者ID校验确保操作正确的编辑器

### 3. 异步处理
- 编辑器获取支持异步等待，自动处理初始化时序
- 数据同步异步执行，不阻塞业务操作
- 错误处理和超时机制完善

### 4. 可扩展性
- 新增编辑器类型只需继承BaseEditorOperator
- 新增同步类型只需添加Action类型和处理逻辑
- 支持多实例场景（不同患者、不同文书类型）

## 文件结构

```
src/lib/EditorOperator/
├── EditorInstanceManager.ts          # 编辑器实例管理器
├── BaseEditorOperator.ts             # 编辑器操作基类
├── OutpatientEditorOperator.ts       # 门诊编辑器操作类
├── InpatientEditorOperator.ts        # 住院编辑器操作类
└── editorDataSyncService.ts          # 数据同步服务

src/components/action/TextProcessors/
└── EmrEditor.vue                     # 编辑器组件

src/components/workFlow/
└── PatientWorkspace.vue              # 业务工作区组件
```

## 总结

本方案通过统一的编辑器管理机制和Action驱动的数据同步，实现了EMR系统中编辑器与业务模块的有效集成。方案具有松耦合、高可扩展、数据一致性强等特点，能够满足复杂医疗业务场景的需求。
