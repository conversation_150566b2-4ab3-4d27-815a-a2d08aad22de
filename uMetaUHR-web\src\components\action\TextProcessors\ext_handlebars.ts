import Handlebars from 'handlebars'
import { labObjInHTML, showObj } from '@/lib/textFormatter/lab'
import asyncHelpers from 'handlebars-async-helpers'
import jsonToReadableText from '@/lib/jsonToDisplayText'
import { getNestedValue } from '@/lib/getContext'
import { execAI2, getFile } from '@/lib/aiLib/ai'
import { dbList } from '@/lib/dataMan'

// Create async Handlebars instance
const asyncHandlebar = asyncHelpers(Handlebars)

// Register basic Handlebars helpers
asyncHandlebar.registerHelper({
  eq: (v1: any, v2: any) => v1 === v2,
  ne: (v1: any, v2: any) => v1 !== v2,
  lt: (v1: any, v2: any) => v1 < v2,
  gt: (v1: any, v2: any) => v1 > v2,
  lte: (v1: any, v2: any) => v1 <= v2,
  gte: (v1: any, v2: any) => v1 >= v2,
  and(...args: any[]) {
    return Array.prototype.every.call(args, Boolean)
  },
  or(...args: any[]) {
    return Array.prototype.slice.call(args, 0, -1).some(Boolean)
  },
  contains: (haystack: string, needle: string) =>
    typeof haystack === 'string' && haystack.includes(needle)
})

// Convert object to list
asyncHandlebar.registerHelper('obj2list', function (obj: Record<string, any>) {
  return Object.entries(obj || {}).map(([key, value]) => ({ key, value }))
})

// Display lab object
asyncHandlebar.registerHelper(
  'showLab',
  function (obj: any, format = 'table', displayMode: any, options: any) {
    return new Handlebars.SafeString(labObjInHTML(obj, format, displayMode))
  }
)

// Display object
asyncHandlebar.registerHelper('showObj', function (obj: any, options: any) {
  return new Handlebars.SafeString(showObj(obj))
})

// Component helper
asyncHandlebar.registerHelper('comp', (type: string, ...rest: any[]) => {
  if (type === 'button') {
    const [id, eventName, label] = rest
    const schema = JSON.stringify({
      type: 'button',
      event: { name: eventName, id },
      label
    })
    const html = `<component :is="lc('button')" :schema='${schema}' :context="{}"></component>`
    return new Handlebars.SafeString(html)
  }
  return ''
})

// Register async helpers
asyncHandlebar.registerHelper('SOP', async function (name: string) {
  const content = await getFile('SOP-guideline', name)
  console.log(content)
  return content
})

asyncHandlebar.registerHelper('ai', async function (this: any, options: any) {
  const content = await options.fn(this)
  const { text: cnt, parsedData: js } = await execAI2(content, this.context || {})
  const { nextAction: na2, message } = js || {}
  if (na2) {
    // this.workDict.nextAction = na2
    return jsonToReadableText(message)
  }
  return cnt
})

const setIt = async function (name: string, options: any) {
  const { data: { workDict = {} as any } = {} } = options || {}
  workDict[name] = await options.fn()
  return ''
}
asyncHandlebar.registerHelper('记作', setIt)
asyncHandlebar.registerHelper('set', setIt)

const getIt = async (name: string, options: any) => {
  const { data: { workDict = {} as any } = {} } = options || {}
  return workDict[name] ?? ''
}
asyncHandlebar.registerHelper('引用', getIt)
asyncHandlebar.registerHelper('get', getIt)
asyncHandlebar.registerHelper('当前病历', async function (this: any, paragraphName: string) {
  const para = getNestedValue(this.context?.note_map || {}, paragraphName)
  return para ? `${paragraphName}\n----\n${para}` : ''
})
asyncHandlebar.registerHelper(
  '病历',
  async function (this: any, note_type: string, paragraphName: string) {
    return getNote2(note_type, paragraphName, this.context)
  }
)

// Helper to fetch notes
async function getNote2(note_type: string, paragraph_name: string, context: any) {
  if (!note_type || !paragraph_name) return
  const { pat_encounter } = context || ({} as any)
  const cn1 = await dbList({
    sql: `
      select cn2.*
      from "maxEMR".cn2
             left join "maxEMR".cn1 cn1 on cn1.id = cn2.note_id
      where "enc_id" = ?::text
        and note_type = ?::text
        and "WJJGMC" = ?::text
    `,
    param: [pat_encounter, note_type, paragraph_name]
  })
  if (!cn1) return ''
  return cn1.map((x) => x.WBNR).join('\n')
}

async function compileTemplate2(template: string, context: any = {}, workDict = {}) {
  const compiledTemplate = asyncHandlebar.compile(template)
  return compiledTemplate(
    { ...context },
    {
      data: {
        workDict, // Pass workDict to helpers via Handlebars' runtime options
        context: { ...context } // Pass context to helpers
      }
    }
  )
}

export { compileTemplate2, asyncHandlebar }
