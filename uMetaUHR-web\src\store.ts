import { reactive, readonly } from 'vue'
import TreeNode from '@/components/containers/TreeNode.vue'

interface TreeNode {
  id: number
  label: string
  children: TreeNode[]
}

interface State {
  treeData: TreeNode | null
}

const state: State = reactive({
  treeData: null
})

const mutations = {
  setTreeData(newTreeData: TreeNode) {
    state.treeData = newTreeData
  },
  updateNode(nodeId: number, newLabel: string) {
    const updateNodeRecursive = (node: TreeNode): boolean => {
      if (node.id === nodeId) {
        node.label = newLabel
        return true
      }
      if (node.children) {
        for (const child of node.children) {
          if (updateNodeRecursive(child)) {
            return true
          }
        }
      }
      return false
    }
    if (state.treeData) {
      updateNodeRecursive(state.treeData)
    }
  },
  addNode(parentId: number, newNode: TreeNode) {
    const addNodeRecursive = (node: TreeNode): boolean => {
      if (node.id === parentId) {
        node.children.push(newNode)
        return true
      }
      if (node.children) {
        for (const child of node.children) {
          if (addNodeRecursive(child)) {
            return true
          }
        }
      }
      return false
    }
    if (state.treeData) {
      addNodeRecursive(state.treeData)
    }
  }
}

export default {
  state: readonly(state),
  mutations
}
