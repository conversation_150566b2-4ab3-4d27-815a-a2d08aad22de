# 电子病历诊断文本生成规则

## 1. 概述

本文档详细说明电子病历系统中诊断模块的文本生成规则，包括诊断前文字、诊断名称、诊断后文字的组合规则，以及疑似诊断的标识方式等。

## 2. 基本组成部分

诊断文本通常由以下几个部分组成：

- **诊断前文字**：如"初步诊断"、"临床诊断"、"出院诊断"等
- **诊断名称**：具体的疾病名称
- **诊断后文字**：如"待查"、"可能性大"、"确诊"等

## 3. 疑似诊断标识规则

### 3.1 问号(?)的使用规则

- **直接问号**：用于表示不确定的诊断
  - 例：`高血压?`、`糖尿病?`
- **括号问号**：用于表示更不确定的诊断
  - 例：`(高血压?)`、`(糖尿病?)`

### 3.2 括号使用规则

- **确定诊断**：通常不加括号
  - 例：`高血压`、`糖尿病`
- **疑似诊断**：使用括号
  - 轻度不确定：`高血压?`


## 4. 诊断文本生成模板

### 4.1 标准模板

```
[诊断前文字] + [诊断名称] + [诊断后文字]
```

### 4.2 常见组合示例

- **确诊诊断**：`临床诊断：高血压`
- **疑似诊断**：`临床诊断：高血压?`
- **待查诊断**：`临床诊断：(高血压?)待查`
- **可能性诊断**：`临床诊断：高血压可能性大`

## 5. 业务规则集

### 5.1 诊断状态分类

#### 1. 确诊诊断
- **定义**：医生确认的诊断
- **格式**：`诊断名称`
- **示例**：`高血压`、`糖尿病`

#### 2. 疑似诊断
- **定义**：医生怀疑但未确认的诊断
- **格式**：`诊断名称?` 或 `(诊断名称?)`
- **示例**：`高血压?`、`(高血压?)`

#### 3. 待查诊断
- **定义**：需要进一步检查确认的诊断
- **格式**：`(诊断名称?)待查`
- **示例**：`(高血压?)待查`

#### 4. 可能性诊断
- **定义**：基于症状推测的诊断
- **格式**：`诊断名称可能性大`
- **示例**：`高血压可能性大`

### 5.2 多诊断组合规则

- **主要诊断在前**：最重要的诊断放在最前面
- **次要诊断在后**：其他诊断按重要性排序
- **分隔符**：通常用分号(;)或换行分隔
- **示例**：`高血压；糖尿病?；冠心病`

## 6. 配置参数说明

### 6.1 基础控制字段

| 字段名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `isEnable` | boolean | true | 是否启用诊断文本自动生成功能 |
| `isLineBreak` | boolean | false | 是否在诊断间换行 |
| `separator` | string | "；" | 多个诊断之间的分隔符 |

### 6.2 诊断显示控制

| 字段名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `showMainDiagnose` | boolean | false | 是否显示主要诊断标识 |
| `showConfirm` | boolean | true | 是否显示确诊状态 |
| `confirmText` | string | "?" | 疑似诊断的标识符 |

### 6.3 标题显示控制

| 字段名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `showTitle` | number | 1 | 是否显示诊断标题 |
| `showTitleType` | number | 1 | 标题显示类型 |

### 6.4 序列控制

| 字段名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `seqType` | number | 0 | 诊断排序类型 |

### 6.5 配置字段详细说明

#### displayType（显示类型）
- `1`: 仅显示诊断名称
- `2`: 显示诊断名称+编码
- `3`: 显示诊断名称+置信度
- `4`: 显示完整诊断信息

#### showTitle（标题显示）
- `0`: 不显示标题
- `1`: 显示标题
- `2`: 根据上下文决定

#### showTitleType（标题类型）
- `1`: 标准标题（如"临床诊断"）
- `2`: 简化标题（如"诊断"）
- `3`: 自定义标题


## 7. 配置示例

### 7.1 标准配置

```json
{
  "mode": "append",
  "preText": "临床诊断：",
  "diagnoses": [
    {
      "name": "高血压",
      "code": "I10",
      "postText": "",
      "suspected": false
    },
    {
      "name": "糖尿病",
      "code": "E11",
      "postText": "待查",
      "suspected": true
    }
  ],
  "separator": "；",
  "isLineBreak": false,
  "showICDCode": true,
  "suspectedFormat": "({name}?)" // 可自定义
}
```