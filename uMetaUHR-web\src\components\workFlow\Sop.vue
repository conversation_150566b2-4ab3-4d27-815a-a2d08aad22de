<template>
  <div class="sop">
    <component
      :is="loadComponent(props.schema?.ui?.type)"
      v-if="props.schema"
      :key="refreshKey"
      :context="getContext(props)"
      :schema="props.schema?.ui"
    />
    <WnDialog ref="dialog" />
  </div>
</template>

<script lang="ts" setup>
import { defineProps, provide, ref } from 'vue'
import { loadComponent } from '../componentRegistry'
import { getContext } from '@/lib/getContext'
import { dbGet, dbList, dbSave } from '@/lib/dataMan'
import { useAI } from '@/lib/ai'
import { extractJsonFromText, htmlToText } from '@/lib/textFormater'
import { deepCopy, deepMerge } from '@/lib/copymerge'
import { isEmpty } from '@/lib/util'
import { waitForData } from '@/lib/datasync'
import { dayToDate } from '@/lib/datetime'
import { useSopRules } from '@/components/workFlow/sop-composible/sop-rules'

import md5 from 'md5'
import { Debouncer } from '@/lib/Debouncer'
import WnDialog from '@/components/containers/WnDialog.vue'
import { execAI2, getFile } from '@/lib/aiLib/ai'
import { cacheManager } from '@/lib/CacheManager'
import { useSopAI } from '@/components/workFlow/sop-composible/sop-ai'
import { compileTemplate2 } from '@/components/action/TextProcessors/ext_handlebars'

const props = defineProps<{
  schema: any
  context: { [key: string]: any }
}>()

let alert = async (message: string) => {
  console.log(message)
  return window.popAlert({ message })
}

let refreshKey = ref(0)
const dialog = ref<InstanceType<typeof WnDialog>>()

const CONFIG_PROMPT_KEY = 'config_prompt'

useSopRules()
useSopAI(props)

provide('list-patients', async (param: any = {}) => {
  let sql = props?.schema?.ui?.query?.search?.['sql-pat']
  if (!sql) {
    await alert('patient query is missing')
    return
  }
  return (await dbList({ sql })) || []
})

let lastPat = ''

let waitForPatSummary = async (param: any) => {
  const sql = `
    SELECT "WBNR"       AS paragraph_content,
           "WJJG_INDEX" AS paragraph_order,
           "BLH"        AS encounter,
           "WJJGMC"     AS paragraph_name
    FROM "maxEMR".cn2
    WHERE "HZXM" = ?::text
      AND s_name = '出院小结'
      AND "WJJGMC" IN ('病程与诊疗结果', '出院日期', '出院诊断')
    ORDER BY "BLH", "WJJG_INDEX"
  `
  let pat_name = await waitForData(() => getContext(props).pat_name)
  if (!pat_name) return []
  if (pat_name == lastPat) return []
  lastPat = pat_name

  try {
    // 执行数据库查询
    let noteParagraphs = await dbList({ sql, param: [pat_name] })

    let p = {} as any
    for (let d of noteParagraphs.sort((a: any, b: any) =>
      +a?.paragraph_order > +b?.paragraph_order ? 1 : -1
    )) {
      let { encounter, paragraph_name, paragraph_content } = d
      if (!encounter) continue
      if (paragraph_name == '出院日期') {
        d.paragraph_content = dayToDate(d.paragraph_content)
      }
      deepMerge(p, { [encounter]: { [paragraph_name]: d } })
    }
    let context = getContext(props)
    context.patSum = {}
    context.patSumSchema = {
      type: 'toolbar',
      children: [
        {
          type: 'button',
          event: {
            name: 'llm-summarize'
          },
          label: 'LLM总结'
        }
      ]
    }
    context.patSumData = Object.keys(p)
      .map((k) => p[k])
      .reverse()
    context.patSumTempl = `
<h1>患者就诊过程</h1>
    {{#each patSumData}}
    <h3>{{encounter}}</h3>
    <fieldset style="border: 1px solid lightblue; margin: 5px; padding: 0">
        <legend style="font-size: 8pt">出院日期：<span style="font-weight: bold">{{this.出院日期.paragraph_content}}</span></legend>
        {{comp "toolbar" ../patSumSchema this }}
        <div style="display: grid; grid-template-columns: 2fr 3fr;">
        {{#each this}}
          {{#if (ne paragraph_name "出院日期") }}
          <div style="padding: 0 3px"><span style="font-size: 10pt; color:lightblue; display: inline-block">{{paragraph_name}}:</span> {{paragraph_content}} <br/></div>
          {{/if}}
        {{/each}}
        </div>
    </fieldset>
{{/each}}
    `
    context.patSumTempl = `
<h1>患者就诊过程</h1>
{{#each patSumData}}
{{showObj this}}
{{/each}}
    `
  } catch (error: any) {
    // 处理可能的错误
    console.error('Error loading patient summary:', error)
    return []
  }
}

provide('wait-for-patient-summary', waitForPatSummary)

provide('select-patient', async (param = {}) => {
  let context = getContext(props)
  let { data: { patname = '' } = {} } = param as any
  if (!patname) return

  // get data before refresh.
  context.patData =
    (await dbGet({
      sql: 'select * from "maxEMR".pt1 where patname = ?::text',
      param: [patname]
    })) || []

  //clear patient data and refresh
  delete context.pat_encounter
  delete context.note
  delete context.aiResult
  context.pat_name = patname
  context.detail = {} //refresh

  // if (
  //   context?.flow?.activePatDetailTab == 'pat-lab-and-exam' ||
  //   context?.flow?.activePatDetailTab == 'pat-lab-and-exam-graph'
  // ) {
  await loadLabsAndExam(context.patData.id)
  // }

  return {}
})

provide('before-switching-pat-sum', (param: any) => {
  let { menuId } = param
  let context = getContext(props)
  deepMerge(context, { flow: { activePatDetailTab: menuId } })
})

provide('before-switching-pat-detail', (param: any) => {
  let context = getContext(props)
  delete context.flow
})

provide('save-pat-data', async (param = {}) => {
  let context = getContext(props) as any
  let patData = context?.patData

  await dbSave({
    table: 'pt1',
    data: [patData],
    conflict_column: 'id',
    update_columns: ['id', 'data', 'patname']
  })
  await alert('患者数据保存完毕')
})

provide('list-enc-notes', async (param: any = {}) => {
  let context = getContext(props)
  let { pat_name } = context
  let sql = props?.schema?.ui?.query?.search?.['sql-encounter']
  if (!pat_name || !sql) return []

  const d = await dbList({ sql, param: [pat_name] })
  return d.sort((a: any, b: any) => {
    return +a?.pat_encounter > +b?.pat_encounter ? 1 : -1
  })
})

provide('select-enc-note', async (param = {}, options = {}) => {
  let context = getContext(props)
  let { pat_encounter, note_id } = param?.data || {}
  if (!note_id) return

  //刷新
  context.detail.detail = {}

  const d = ((await getNote(note_id)) || []).sort(
    (a: any, b: any) => +(a?.WJJG_INDEX ?? -1) - +(b?.WJJG_INDEX ?? -1)
  )

  context.detail.d = d
  context.pat_encounter = pat_encounter
  context.note = d

  let note_map = {} as any
  for (let n of d) {
    let { WJJGMC, WBNR } = n
    if (!WBNR || !WJJGMC) continue
    note_map[WJJGMC] = WBNR
  }
  context.note_map = note_map
})

async function getNote(note_id: number) {
  if (!note_id) return
  let sql = props?.schema?.ui?.query?.search?.['sql-note']
  if (!sql) return
  let context = getContext(props)
  let cn1 = await dbGet({
    sql: `select *
          from "maxEMR".cn1
          where id = ?::int`,
    param: [note_id]
  })
  if (!cn1.data) cn1.data = {}
  context.detail.cn1 = cn1
  let note = (await dbList({ sql, param: [note_id] })) || []
  return note.filter((n: any) => !n['WBNR']?.replaceAll(' ', '').includes?.('姓名'))
}

provide('act-collect-patient-demographic-info', async (param = {}, options = {}) => {
  let context = getContext(props)
  let { note } = context
  let note2 = {} as { [key: string]: string }
  for (let n of note) {
    let { WJJGMC: title, WBNR: content } = n
    note2[title] = content
  }

  let key = context?.detail?.prompt?.curKey || ''
  if (!key) return

  let prompts = await getPrompts()
  let { prompt } = prompts[key]
  if (!prompt) return

  let { text, parsedData } = await execAI2(prompt, { c: props.context, rc: context, n: note2 })
  context.aiResult = parsedData
})

provide('list-prompts', async () => {
  let prompts = await getPrompts()
  let context = getContext(props)
  context.promptTemplates = Object.keys(prompts).map((key) => ({ key, prompt: prompts[key] }))
  return context.promptTemplates
})

provide('change-prompt', async (param = {}, options = {}) => {
  const context = getContext(props)
  const { promptTemplates = [] } = context
  const { key: newKey, curKey, prompt } = context.detail.prompt

  context.promptTemplates = promptTemplates.map((p: any) =>
    p.key === curKey ? { key: newKey, prompt } : p
  )
  context.detail.prompt = { key: newKey, curKey: newKey, prompt }
})

provide('save-prompt', async (param = {}, options = {}) => {
  let context = getContext(props)
  let { promptTemplates = [] } = context
  let { key, curKey, prompt } = context.detail.prompt as any
  let p2 = {} as any
  for (let p of promptTemplates) {
    let { key, prompt } = p
    p2[key] = prompt
  }

  delete p2[curKey]
  p2[key] = prompt

  await cacheManager.clear(CONFIG_PROMPT_KEY)

  let data = {
    category: 'sop质控',
    key: 'main',
    config: p2
  }
  await dbSave({
    table: 'config',
    data: [data],
    conflict_column: 'key',
    update_columns: ['category', 'key', 'config']
  })
})

provide('new-prompt', async (param = {}, options = {}) => {
  let context = getContext(props)
  context.promptTemplates.push({ key: 'new prompt', prompt: '' })
  return Object.keys(context.promptTemplates).map((prompt) => ({ prompt }))
})

provide('select-prompt', async (param = {}, options = {}) => {
  let context = getContext(props)
  let { key, prompt } = param?.data || {}
  if (!key) return

  //refresh
  Object.assign(context.detail.prompt, { key, prompt, curKey: key })
})

provide('merge-to-patient', async (param = {}, options = {}) => {
  let context = getContext(props)
  let { pat_name = '', aiResult = {} } = context
  if (!pat_name || !pat_name.trim() || isEmpty(aiResult)) return

  let patData =
    (await dbGet({
      sql: 'select * from "maxEMR".patient where name = ?::text',
      param: [pat_name]
    })) || {}
  let { id, data = {}, name } = patData

  id = id || +(Math.random() * 1e15).toString().substr(0, 8)
  deepMerge(patData, context.patData || {}, { id, data: aiResult, name: pat_name })
  context.patData = patData

  await window.popAlert({ message: '数据合并完毕。' })
})

provide('save-to-patient', async (param = {}, options = {}) => {
  let context = getContext(props)
  let { patData: { id = '', data = {}, patname = '' } = {} } = context
  if (!id || !patname.trim() || isEmpty(data)) return

  await dbSave({
    table: 'pt1',
    data: [{ id, data, patname }],
    conflict_column: 'id',
    update_columns: ['id', 'data', 'patname']
  })
})

provide('save-to-note', async (param = {}, options = {}) => {
  let context = getContext(props)
  let { cn1 = {}, cn1: { id = '' } = {} } = context?.detail || {}
  if (!id) return

  await dbSave({
    table: 'cn1',
    data: [cn1],
    conflict_column: 'id',
    update_columns: ['id', 'data']
  })
})

async function getPrompts() {
  return await cacheManager.fetch(CONFIG_PROMPT_KEY, async () => {
    let { config = {} } =
      (await dbGet({
        sql: `SELECT *
            FROM "maxEMR".config
            WHERE category = ?::text`,
        param: ['sop质控']
      })) ?? ({} as any)
    return config as any
  })
}

provide('parse-note-for-data', async (param = {}, options = {}) => {
  await alert('processing ...')

  let context = getContext(props)
  let note_type = context?.detail?.cn1?.note_type
  if (!note_type) return null

  let prompts = await getPrompts()
  let prompt = prompts[note_type]
  if (!prompt) {
    await alert(`未为 ${note_type} 配置提示词.`)
    return
  }

  prompt = htmlToText(prompt)
  let filledPrompt = await compileTemplate2(
    prompt,
    { c: props.context, rc: context, note: context.d },
    {}
  )
  await (async (filledPrompt: string, cn1: any) => {
    if (!cn1) return null

    let { content: cnt } = (await useAI(
      [
        { role: 'system', content: '' },
        { role: 'user', content: filledPrompt }
      ],
      {}
    )) as any

    await alert(cnt)
    let noteData = extractJsonFromText(cnt)

    cn1.data = { ...(cn1?.data || {}), extNoteData: noteData }

    await dbSave({
      table: 'cn1',
      data: [cn1],
      conflict_column: 'id',
      update_columns: ['id', 'data']
    })

    await alert(`data is saved for patient ${cn1.patname}, enc ${cn1.enc_id}`)
  })(filledPrompt, deepCopy(context.detail.cn1) as any)
})

provide('llm-extract-note-info', async (param = {}, options = {}) => {
  let context = getContext(props)
  let data = context?.detail?.cn1?.data || {}
  let { pat_id, enc_id } = context.detail.cn1

  const parser = await import('@/pegrule/lab-results')
  for (let item of ['主要检查结果', '特殊检查结果']) {
    let parsedData = []
    try {
      let t = context.note_map[item]
      parsedData = (t && parser.parse(t)) || []
    } catch (error) {
      console.error('Parsing error:', error)
    }

    for (let d of parsedData) {
      let { id, reportTime: date_time, reportName: name, content } = d || {}
      if (!date_time) continue
      let action_context = md5(content)
      if (!id) {
        ;({ id } = await dbGet({
          sql: `select id
                from "maxEMR".lab_report
                where pat_id = ?::int
                  and date_time = ?::int
                  and name = ?::text
                  and action_context = ?::text
          `,
          param: [pat_id, date_time, name, action_context]
        }))
      }
      await dbSave({
        table: 'lab_report',
        data: [
          {
            date_time,
            name,
            data: { content },
            pat_id,
            enc_id,
            id,
            action_context
          }
        ],
        conflict_column: 'id',
        update_columns: [
          ...(id ? ['id'] : []),
          'data',
          'date_time',
          'name',
          'pat_id',
          'enc_id',
          'action_context'
        ]
      })
    }
    console.log(parsedData)
    data.labResults = JSON.stringify(parsedData)
  }
  await alert('ok')
})

provide('llm-check-note', async (param = {}, options = {}) => {
  const { tag = '', sop: sopName = '', item = '' } = param?.schema?.event || {}

  // Validate required parameters
  if (!sopName) return await alert(`SOP "${sopName}" (tag: ${tag}) is not specified`)
  if (!item) return await alert(`Item "${item}" is not specified`)

  const context = getContext(props)
  const { note } = context
  const note2 = note.reduce(
    (acc: any, { WJJGMC: title, WBNR: content }: { [key: string]: string }) => {
      acc[title] = content
      return acc
    },
    {}
  )

  let namedText = {}

  // Load command template and preprocess
  let command = await getFile('SOP-note-command', sopName)

  const cur_cn1 = deepCopy(context.detail.cn1)
  await alert('Processing ...')

  cur_cn1.data = {
    ...(cur_cn1.data || {}),
    [item]: await compileTemplate2(command, { c: props.context, rc: context, n: note2 }, namedText)
  }
  await saveToDatabase(cur_cn1, context)

  await alert(`SOP ${tag}处理完毕`)
})

async function saveToDatabase(cur_cn1: any, context: any) {
  await dbSave({
    table: 'cn1',
    data: [cur_cn1],
    conflict_column: 'id',
    update_columns: ['id', 'data']
  })

  if (cur_cn1.id === context.detail.cn1.id) {
    context.detail.cn1.data = cur_cn1.data
  }
}

const loadLabsAndExam = new Debouncer(50).debounce(async (pat_id: number) => {
  if (!pat_id) return null

  let context = getContext(props)
  context.detail.lab_exam_list =
    (await dbList({
      sql: `
      select *
      from "maxEMR".lab_report
      where pat_id = ?::int
      order by date_time desc, name`,
      param: [pat_id]
    })) || []
})

provide('load-labs-and-exam', async () => {
  let context = getContext(props)
  let pat_id = context?.patData?.id
  if (!pat_id) return null
  await loadLabsAndExam(pat_id)
})

let dialogContext = ref({})
provide('lab-analysis', async (param: any) => {
  let context = getContext(props) as any
  let pat_id = context?.patData?.id
  if (!pat_id) return null

  let { id: lab_id, name } = param.schema.event || {}
  let { lab_exam_list = [] } = context?.detail || {}
  let lab = lab_exam_list.find((x: any) => +x.id === +lab_id)

  dialogContext.value = { prtParam: param, lab }
  await dialog.value!.dialog(dialogContext.value, props.schema?.dialog?.['lab-analysis'])
})

provide('extract-lab-info', async (param: any) => {
  const parser = await import('@/pegrule/lab-results-血常规')
  let { prtParam, lab: { data: labData = {} } = {} } = param?.context || ({} as any)

  let input = labData?.content || ''
  let labResult = []
  try {
    ;({ result: labResult } = parser.parse(input) || [])
    console.log('parsedData', labResult)
  } catch (error) {
    console.error(error)
  }
  labData.result = labResult
})

provide('save-lab-info', async (param: any): Promise<any> => {
  let { lab = {} } = param?.context || ({} as any)
  if (!lab?.id) {
    return await window.alert('no lab report id.')
  }

  await dbSave({
    table: 'lab_report',
    data: [lab],
    conflict_column: 'id',
    update_columns: ['id', 'data']
  })
})

provide('parse-lab-result-to-meta-data', async (param: any) => {
  let { tool = {}, lab = {} } = param?.context || {}
  let prompt = await getFile('SOP-prompts', 'labRes-metaData')
  let { text } = await execAI2(prompt, param?.context || {})
  tool.textToParse = text.replaceAll('\n', '<br>')
})

provide('save-lab-meta-data', async (param: any) => {
  let { tool = {}, lab = {} } = param?.context || {}
  if (!tool?.textToParse) return
  let text = htmlToText(tool.textToParse)
  tool.parsedResult = extractJsonFromText(text)
  if (!Array.isArray(tool.parsedResult)) return await alert('Data should be in array format')
  for (let d of tool.parsedResult) {
    let { n, v, u, s } = d
    if (!s) continue
    let db = await dbGet({
      sql: `SELECT id
            FROM "maxEMR".lab_component_md
            WHERE symbol = ?::text`,
      param: [s]
    })
    let { name = '', alias = [] } = db?.data || ({} as any)
    alias = Array.from(new Set([name, n, ...alias].filter((x) => !!x)).keys()).sort()
    db = { ...db, name: name || n, symbol: s, data: { alias } }
    if (!db.id) delete db.id
    await dbSave({
      table: 'lab_component_md',
      data: [db],
      conflict_column: 'id',
      update_columns: (db.id ? ['id'] : []).concat(['name', 'data', 'symbol'])
    })
  }
})

provide('generate-text', async (param: any) => {
  let context = getContext(props) as any
  console.log(context)
  await alert('text generated')
})

defineExpose({ compName: props?.schema?.type || '-' })
</script>
<style lang="scss" scoped>
.sop {
  display: flex;
  flex: 1 1 auto;
}
</style>
