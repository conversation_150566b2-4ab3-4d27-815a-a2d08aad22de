{
  type: 'stack',
  menu: [
    {
      id: 'pat-sum-view-1',
      displayName: '就诊总结'
    },
    {
      id: 'pat-lab-and-exam',
      displayName: '检查与检验'
    },
    {
      id: 'pat-lab-and-exam-graph',
      displayName: '检查与检验图'
    }
  ],
  event: {
    beforeSwitchTab: {
      name: 'before-switching-pat-sum'
    }
  },
  myFavoriteId: '01001-SOP-PATSUM-13',
  children: {
    'pat-sum-view-1': {
      type: 'section',
      title: '患者就诊',
      children: [
        {
          type: 'textDisplay',
          event: {
            waitForData: 'wait-for-patient-summary'
          },
          model: 'patSumTempl'
        }
      ]
    },
    'pat-lab-and-exam': {
      type: 'window',
      title: '检查与检验',
      event: {
        loadData: 'load-labs-and-exam'
      },
      head: [
        {
          type: 'toolbar',
          style: {
            'justify-content': 'right'
          },
          children: [
            {
              type: 'button',
              event: {
                name: 'load-labs-and-exam'
              },
              label: '刷新'
            }
          ]
        }
      ],
      body: [
        {
          type: 'textDisplay',
          template: "{{#if lab_exam_list.length}}<table>  <tbody>  {{#each lab_exam_list}}  <tr style=\"font-weight: bold; background: #dee;\">    <td style=\"font-weight:bold; font-size: 10pt; color: #08e\">{{date_time}}</td>    <td style=\"font-weight:bold; font-size: 10pt; color: #08e\">{{name}}<div style=\"float:right\">{{comp 'button' id 'data-summarize-from-lab' '总结'}}{{comp 'button' id 'lab-analysis' '分析与解析'}}</div></td>  </tr>  <tr>    <td colSpan=5>{{data.content}}{{showLab this 'list' ../displayMode}}</td>  </tr>  {{/each}}  </tbody> </table> {{else}}<div class=\"slow\">无数据</div>{{/if}}"
        }
      ],
      subContext: {
        path: 'detail',
        default: {}
      }
    },
    'pat-lab-and-exam-graph': {
      type: 'window',
      title: '检查与检验图',
      event: {
        loadData: 'load-labs-and-exam'
      },
      head: [
        {
          type: 'toolbar',
          style: {
            'justify-content': 'right'
          },
          children: [
            {
              type: 'button',
              event: {
                name: 'load-labs-and-exam'
              },
              label: '刷新'
            }
          ]
        }
      ],
      body: [
        {
          type: 'section',
          myFavoriteId: '01001-SOP-PATSUM.section-97',
          collapsible: true,
          title: '主要结果',
          children: [
            {
              type: 'labChart',
              model: 'lab_exam_list',
              showItems: ['ALT', 'AST', 'TP', 'WBC', 'K+', 'Na+', 'Cl-']
            }
          ]
        },
        {
          type: 'section',
          myFavoriteId: '01001-SOP-PATSUM.section-117',
          collapsible: true,
          title: '全部结果',
          children: [
            {
              type: 'labChart',
              model: 'lab_exam_list'
            }
          ]
        }
      ],
      subContext: {
        path: 'detail',
        default: {}
      }
    }
  },
  tabPosition: 'left'
}
