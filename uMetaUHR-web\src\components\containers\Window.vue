<template>
  <div :class="[...(schema?.themes || []), 'field-w-window']" :style="schema!.style?.main">
    <div v-if="schema!.title" :style="schema!.style?.title" class="field-w-window-title">
      {{ schema!.title }}
    </div>
    <div v-if="schema!.head" :key="`head${state.headRenderSeq}`" class="field-w-window-head">
      <component
        :is="loadComponent(child?.type)"
        v-for="(child, index) in schema!.head || []"
        :key="`panel-child-${index}`"
        :context="getContext(props)"
        :schema="child"
        class="field-w-window-head"
      />
    </div>
    <div
      v-if="schema!.body"
      :key="`body${state.bodyRenderSeq}`"
      :style="schema!.style?.body"
      class="field-w-window-body-container field-w-window-body"
    >
      <component
        :is="loadComponent(child?.type)"
        v-for="(child, index) in schema!.body || []"
        :key="`panel-child-${index}`"
        ref="body"
        :context="getContext(props)"
        :schema="child"
      />
    </div>
    <div v-if="schema!.foot" class="field-w-window-foot">
      <component
        :is="loadComponent(child?.type)"
        v-for="(child, index) in schema!.foot || []"
        :key="`panel-child-${index}`"
        ref="foot"
        :context="getContext(props)"
        :schema="child"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { inject, onMounted, reactive } from 'vue'
import { loadComponent } from '@/components/componentRegistry'
import { getContext } from '@/lib/getContext'

const props = defineProps({
  schema: Object,
  context: Object,
  style: Object,
  theme: String,
  bodyStyle: Object
})

const state = reactive({
  renderSeq: 1,
  bodyRenderSeq: 1,
  bodyStyle: {},
  theme: '',
  headRenderSeq: 1,
  style: {}
})

let { loadData } = props.schema!.event || {}
let loadDataFun = inject(loadData, async () => 1)

onMounted(async () => {
  await loadDataFun()
  Object.assign(state, props.schema || {})
})

defineExpose({ compName: props.schema!.type || '-' })
</script>

<style lang="scss" scoped>
.field-w-window {
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
  overflow: auto;

  .field-w-window-title {
    grid-area: window-title;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #b3c3fa;
    padding: 2px 5px;
    font-weight: bolder;
    font-size: large;
    border-radius: 5px 5px 0 0;
    border: none;
    margin: 0;
    user-select: none;
  }

  .field-w-window-head {
    grid-area: window-header;
  }

  .field-w-window-foot {
    grid-area: window-footer;
  }

  .field-w-window-body-container {
    grid-area: window-body;
    overflow-y: auto;
    display: block;
    flex-direction: column;
    flex: 1 1 auto;
  }
}

.body-flex.field-w-window {
  .field-w-window-body-container {
    display: flex;
  }
}
</style>
