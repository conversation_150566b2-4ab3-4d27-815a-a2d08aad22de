import type { Order, NewOrder, Patient } from './metaPinia-types'
import { createMetaPinia } from './metaPinia'

/**
 * 医嘱数据管理扩展
 */
export function createOrderStore(initialData: { orders?: Order[]; currentPatient?: Patient | null }) {
  const med = createMetaPinia(initialData)

  // 医嘱专用操作扩展
  const orderApi = {
    ...med,

    /**
     * 创建医嘱 (两种方式)
     */
    createOrder(order: NewOrder): Order {
      if (!order.drug || !order.dose) {
        throw new Error('药品和剂量不能为空')
      }

      const newOrder: Order = {
        id: `order_${Date.now()}`,
        status: 'pending',
        ...order
      }

      if (!med.data.orders) med.data.orders = []
      med.data.orders.push(newOrder)
      med.logAction(`创建医嘱 ${newOrder.drug}`)
      return newOrder
    },

    /**
     * 为当前患者创建医嘱
     */
    createOrderForCurrentPatient(order: Omit<Order, 'id'>): Order {
      if (!med.data.currentPatient) throw new Error('无当前患者')
      
      const newOrder: Order = {
        id: `order_${Date.now()}`,
        drug: order.drug,
        dose: order.dose,
        ...order
      }
      
      if (!med.data.currentPatient.orders) med.data.currentPatient.orders = []
      med.data.currentPatient.orders.push(newOrder)
      med.logAction(`创建医嘱 ${newOrder.drug}`)
      return newOrder
    },

    /**
     * 更新医嘱剂量
     */
    updateDose(orderId: string, newDose: string): boolean {
      const order = med.data.orders?.find((o: Order) => o.id === orderId)
      if (!order) return false

      const oldValue = order.dose
      order.dose = newDose
      med.logAction(`修改剂量 ${oldValue} → ${newDose}`)
      return true
    },

    /**
     * 删除医嘱
     */
    deleteOrder(orderId: string): boolean {
      if (!med.data.orders) return false

      const index = med.data.orders.findIndex((o: Order) => o.id === orderId)
      if (index === -1) return false

      const [deleted] = med.data.orders.splice(index, 1)
      med.logAction(`删除医嘱 ${deleted.drug}`)
      return true
    },

    /**
     * 从当前患者删除医嘱
     */
    deleteOrderFromCurrentPatient(orderId: string): boolean {
      const patient = med.data.currentPatient
      if (!patient?.orders) return false
      
      const index = patient.orders.findIndex((o: Order) => o.id === orderId)
      if (index === -1) return false
      
      const [deleted] = patient.orders.splice(index, 1)
      med.logAction(`删除医嘱 ${deleted.drug}`)
      return true
    },

    /**
     * 查找医嘱
     */
    findOrder(orderId: string): Order | undefined {
      return med.data.orders?.find((o: Order) => o.id === orderId)
    },

    /**
     * 获取当前患者的医嘱
     */
    getCurrentPatientOrders(): Order[] {
      return med.data.currentPatient?.orders || []
    }
  }

  return orderApi
}

export type OrderStore = ReturnType<typeof createOrderStore>
