<template>
  <button
    v-if="isVisible"
    :class="[
      'button',
      { pressed: isPressed },
      ...(schema?.theme?.map((t) => t.toLowerCase()) || []),
      {'disabled': isDisabled}
    ]"
    :style="schema?.style?.main"
    @click="handleClick($event)"
    @mousedown="addPressedClass"
    @touchstart="addPressedClass"
  >
    <img v-if="schema?.customIcon" :src="`${BASE}${schema.customIcon}`" class="button-icon icon-size" :style="schema?.style?.customIcon" />
    <font-awesome-icon
      v-if="schema?.icon"
      :icon="['fas', schema?.icon]"
      style="pointer-events: none"
    ></font-awesome-icon>
    {{ schema?.label }}
  </button>
</template>

<script lang="ts" setup>
import { computed, defineProps, getCurrentInstance, inject, onMounted, ref } from 'vue'
import { evaluateDisabledWhen, evaluateShowWhen, getContext } from '@/lib/getContext'
import hotkeys from 'hotkeys-js'
import { getParentExposedByInstance } from '@/lib/util'

interface ButtonSchema {
  label?: string
  icon?: string
  style?: {
    main?: Record<string, string>
    customIcon?: Record<string, string>
    vars?: {
      iconBtnSize?: string
    }
  }
  theme?: string[]
  type: string
  children?: Array<any>
  hkeys?: Array<{
    key: string
    scope?: Record<string, any>
  }>
  event?: {
    name: string
  }
  customIcon?: string
}

type ActionParams = {
  context: Record<string, any>
  schema: ButtonSchema
  getParentExposed: (componentName?: string) => any
}

const props = defineProps<{
  schema: ButtonSchema
  context: Record<string, any>
  label?: string
  event?: { name: string }
}>()
const BASE = import.meta.env.BASE_URL || ''

const isVisible = computed(() => evaluateShowWhen(props))

const isDisabled = computed(() => evaluateDisabledWhen(props))

const { hkeys = [], event } = props.schema || {}

const eventName = event?.name ?? props.event?.name
const act = eventName ? inject<(params: ActionParams) => void>(eventName, () => {
  window.popAlert?.({ message: `function "${eventName}" is not implemented.` })
}) : undefined

onMounted(async () => {
  if (!hkeys?.length) return

  for (let { key, scope = {} } of hkeys) {
    if (!key) continue
    hotkeys.unbind(key, scope as unknown as string)
    hotkeys(key, scope as unknown as string, (e) => handleClick(e))
  }
})

const instance = getCurrentInstance()

function getParentExposed(componentName?: string) {
  return getParentExposedByInstance(instance, componentName)
}

function handleClick(event = {} as any) {
  event?.stopPropagation?.()
  event?.preventDefault?.()
  try {
    let context = getContext(props)
    let schema = props?.schema || {}
    act?.({
      context,
      schema,
      getParentExposed
    })
  } catch (e) {
    console.error(e)
  }
}

const isPressed = ref(false)
const addPressedClass = () => {
  isPressed.value = true
  setTimeout(() => (isPressed.value = false), 200)
}
defineExpose({ compName: 'button' })
</script>

<style lang="scss" scoped>
.disabled {
  pointer-events: none;
}
.button {
  background: white;
  border-radius: 2px;
  border: 1px solid #d9d9d9;
  margin: 1px 5px;
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.02);
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  cursor: pointer;
  user-select: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 3px;
  font-size: 14px;
  height: 32px;
  padding: 4px 15px;
  white-space: nowrap;

  img {
    --icon-btn-size: v-bind('schema?.style?.vars?.iconBtnSize || ""');
    width: var(--icon-btn-size);
    height: var(--icon-btn-size)
  }

  .button-icon {
    flex-shrink: 0;
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  }

  &.pressed {
    box-shadow: 0 0 3px #666;
    transform: translateY(2px) scale(0.98);
  }

  &.primary {
    background: #1890ff;
    border-color: #1890ff;
    color: white;

    &:hover {
      background: #40a9ff;
      border-color: #40a9ff;
    }

    &:active {
      background: #096dd9;
      border-color: #096dd9;
    }
  }

  &.sm {
    height: 24px;
    padding: 0 7px;
    font-size: 12px;
  }
}
</style>
