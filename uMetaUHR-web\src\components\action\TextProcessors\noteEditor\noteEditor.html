<!doctype html>
<html lang='en'>
<head>
  <base href='http://localhost:7075/' />
  <meta charset='utf-8' />
  <link rel='icon' href='/favicon.ico' />
  <meta name='viewport' content='width=device-width,initial-scale=1' />
  <meta name='theme-color' content='#000000' />
  <meta name='description' content='Web site created using create-react-app' />
  <link rel='apple-touch-icon' href='/example/logo192.png' />
  <script src='/example/sdk.js'></script>
  <link href='/example/static/css/main.0732d832.css' rel='stylesheet'>
  <title></title>
</head>
<body>
<noscript>You need to enable JavaScript to run this app.</noscript>
<div id='div-editor'></div>
<script>


  // 将 base64 字符串转换为 Blob 对象
  function base64ToBlob(base64String, contentType) {
    const binaryString = atob(base64String) // 解码 base64 字符串为二进制数据
    const byteArray = new Uint8Array(binaryString.length)
    for (let i = 0; i < binaryString.length; i++) {
      byteArray[i] = binaryString.charCodeAt(i)
    }
    return new Blob([byteArray], { type: contentType })
  }

  // 确保init函数完成后再使用editorFunction
  (async () => {
    let editorFunction = null

    async function init(options) {
      let Editor = EmrEditorSDK.Editor
      const vm = await new Editor().init(options)
      const editor = await vm.getEditor()
      editor.setMenuBarVisible(true)
      // 设置事件
      vm.setEvent({
        nsoStructClick: (name, type, position) => {
          console.log(name, type, position)
        },
        nsoKeyPressedEvent: async () => {
          let base64String = await new Promise(done => editorFunction.saveToStream().then(done))
          window.parent.postMessage({
            type: 'note-content',
            data: base64String
          })
        }
      })
      return editor // 返回编辑器实例
    }

    const editorDiv = document.getElementById('div-editor') //替换成自己的div id
    try {
      const editorInstance = await init({
        dom: editorDiv,// DOM元素
        src: 'http://localhost:7075/',//替换成自己的url
        option: {
          bShowMenu: true,
          bShowToolbar: true,
          isTest: false,
          theme: {
            NewControl: {
              ShowRegionOperator: false
            }
          }
        }
      })
      editorFunction = editorInstance
    } catch (error) {
      console.error('初始化编辑器时出错:', error)
    }

    window.addEventListener('message', (event) => {
      // Verify origin for security
      // if (event.origin !== 'https://parent-domain.com') return;

      let { data: { result, editorId, method, data } } = event
      if (!data) return
      editorFunction.openDocumentWithStream(data)
    })

  })()
</script>
</body>
</html>