{"name": "@vue/runtime-dom", "version": "3.5.17", "description": "@vue/runtime-dom", "main": "index.js", "module": "dist/runtime-dom.esm-bundler.js", "types": "dist/runtime-dom.d.ts", "unpkg": "dist/runtime-dom.global.js", "files": ["index.js", "dist"], "exports": {".": {"types": "./dist/runtime-dom.d.ts", "node": {"production": "./dist/runtime-dom.cjs.prod.js", "development": "./dist/runtime-dom.cjs.js", "default": "./index.js"}, "module": "./dist/runtime-dom.esm-bundler.js", "import": "./dist/runtime-dom.esm-bundler.js", "require": "./index.js"}, "./*": "./*"}, "sideEffects": false, "buildOptions": {"name": "VueRuntimeDOM", "formats": ["esm-bundler", "esm-browser", "cjs", "global"]}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/runtime-dom"}, "keywords": ["vue"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/core/issues"}, "homepage": "https://github.com/vuejs/core/tree/main/packages/runtime-dom#readme", "dependencies": {"csstype": "^3.1.3", "@vue/reactivity": "3.5.17", "@vue/shared": "3.5.17", "@vue/runtime-core": "3.5.17"}, "devDependencies": {"@types/trusted-types": "^2.0.7"}}