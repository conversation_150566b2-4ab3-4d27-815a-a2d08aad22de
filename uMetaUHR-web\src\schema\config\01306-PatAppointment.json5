{
  type: 'patAppointment',
  ui: {
    type: 'window',
    themes: ['body-flex'],
    body: [
      {
        type: 'tocDetail',
        style: {
          vars: {
            themeBC: 'lightgrey',
            themeContentBC: 'white'
          }
        },
        myFavoriteId: '01306-PatAppointment-39',
        toc: {
          type: 'stack',
          style: {
            content: {
              background: 'white'
            },
            vars: {
              themeBC: 'none',
              themeBorderColor: 'orange'
            }
          },
          menu: [
            {
              id: 'patList',
              displayName: '患者列表'
            },
            {
              id: 'data',
              displayName: '数据'
            }
          ],
          myFavoriteId: '01306-PatAppointment-52',
          children: {
            patList: {
              type: 'window',
              themes: ['body-flex'],
              body: [
                {
                  type: 'toc',
                  toolbar: [
                    {
                      type: 'button',
                      event: {
                        name: 'new-patient'
                      },
                      label: '新建'
                    }
                  ],
                  columnDef: [
                    {
                      field: 'id',
                      displayName: 'ID'
                    },
                    {
                      field: 'name',
                      displayName: '姓名'
                    }
                  ],
                  event: {
                    listItems: 'list-patients',
                    selectItem: 'select-patient'
                  },
                  selectedLine: 0,
                  myFavoriteId: '01306-PatAppointment-87'
                }
              ]
            },
            data: {
              type: 'window',
              body: [
                {
                  type: 'dataViewer',
                  style: {
                    main: {
                      flex: '1 1 auto',
                      'max-height': '100vh'
                    }
                  },
                  model: ''
                }
              ]
            }
          }
        },
        detail: {
          type: 'window',
          themes: ['body-flex'],
          body: [
            {
              type: 'layout2',
              style: {
                vars: {
                  themeBC: '#F8F8F8',
                  themeBorderColor: 'none'
                }
              },
              myFavoriteId: '01306-PatAppointment-93',
              children: [
                {
                  type: 'window',
                  themes: ['body-flex'],
                  head: [
                    {
                      type: 'textDisplay',
                      template: "<span style='color: #444; font-weight: bolder;'>1. 患者基本信息</span>"
                    },
                    {
                      type: 'toolbar',
                      children: [
                        {
                          type: 'button',
                          event: {
                            name: 'save-patient'
                          },
                          label: '保存'
                        },
                        {
                          type: 'button',
                          event: {
                            name: 'generate-data'
                          },
                          label: '生成患者数据'
                        }
                      ]
                    }
                  ],
                  body: [
                    {
                      type: 'panel',
                      style: {
                        main: {
                          display: 'block',
                          overflow: 'auto',
                          border: 'none'
                        }
                      },
                      children: [
                        {
                          type: 'section',
                          title: '基本信息',
                          collapsible: true,
                          myFavoriteId: '01306-PatAppointment-181',
                          children: [
                            {
                              type: 'inputText',
                              label: '姓名',
                              model: 'patient.name',
                              valid: {
                                required: true
                              }
                            },
                            {
                              type: 'choice',
                              label: '性别',
                              model: 'patient.gender',
                              valid: {
                                required: true
                              },
                              options: [
                                {
                                  label: '男',
                                  value: 'M'
                                },
                                {
                                  label: '女',
                                  value: 'F'
                                },
                                {
                                  label: '未知',
                                  value: 'U'
                                }
                              ]
                            },
                            {
                              type: 'dateTime',
                              label: '出生日期',
                              format: 'yyyy/MM/dd',
                              model: 'patient.birth_date',
                              valid: {
                                required: true
                              }
                            },
                            {
                              type: 'inputText',
                              label: '身份证号',
                              model: 'patient.id_card',
                              pattern: '^\\d{17}[0-9X]$',
                              hint: '请输入18位身份证号码'
                            },
                            {
                              type: 'inputText',
                              label: '联系电话',
                              model: 'patient.phone'
                            },
                            {
                              type: 'inputText',
                              label: '地址-省份',
                              model: 'patient.address.province'
                            },
                            {
                              type: 'inputText',
                              label: '地址-城市',
                              model: 'patient.address.city'
                            },
                            {
                              type: 'inputText',
                              label: '详细地址',
                              model: 'patient.address.detail'
                            },
                            {
                              type: 'inputText',
                              label: '邮政编码',
                              model: 'patient.address.postalCode'
                            },
                            {
                              type: 'choice',
                              label: '医保类型',
                              model: 'patient.insurance_info.type',
                              options: [
                                {
                                  label: '城镇职工医保',
                                  value: '1'
                                },
                                {
                                  label: '城乡居民医保',
                                  value: '2'
                                },
                                {
                                  label: '新农合',
                                  value: '3'
                                },
                                {
                                  label: '商业保险',
                                  value: '4'
                                },
                                {
                                  label: '自费',
                                  value: '5'
                                }
                              ]
                            },
                            {
                              type: 'inputText',
                              label: '医保卡号',
                              model: 'patient.insurance_info.cardNumber'
                            }
                          ]
                        },
                        {
                          type: 'section',
                          title: '基本信息',
                          collapsible: true,
                          myFavoriteId: '01306-PatAppointment-340',
                          children: [
                            {
                              type: 'textDisplay',
                              template: 'thi is a test'
                            }
                          ]
                        }
                      ]
                    }
                  ]
                },
                {
                  type: 'window',
                  themes: ['body-flex'],
                  head: [
                    {
                      type: 'textDisplay',
                      template: "<span style='color: #444; font-weight: bolder;'>2. 预约就诊</span>"
                    }
                  ],
                  body: [
                    {
                      type: 'stack',
                      style: {
                        vars: {
                          themeBorderColor: '#ffdf9e'
                        }
                      },
                      menu: [
                        {
                          id: 'appointment',
                          displayName: '预约'
                        },
                        {
                          id: 'm2',
                          displayName: 'MyChart'
                        },
                        {
                          id: 'data',
                          displayName: '数据'
                        }
                      ],
                      myFavoriteId: '01306-PatAppointment-137',
                      tabPosition: 'top',
                      children: {
                        appointment: {
                          type: 'tocDetail',
                          style: {
                            main: {
                              margin: '3px'
                            },
                            vars: {
                              themeBorderColor: '#EEE'
                            }
                          },
                          myFavoriteId: '01306-PatAppointment-039',
                          toc: {
                            type: 'stack',
                            style: {
                              content: {},
                              vars: {
                                themeBorderColor: 'darkgreen'
                              }
                            },
                            menu: [
                              {
                                id: 'encList',
                                displayName: '就诊列表'
                              },
                              {
                                id: 'data',
                                displayName: '数据'
                              }
                            ],
                            myFavoriteId: '01306-PatAppointment-052',
                            children: {
                              encList: {
                                type: 'window',
                                body: [
                                  {
                                    type: 'toc',
                                    toolbar: [
                                      {
                                        type: 'button',
                                        event: {
                                          name: 'new-appointment'
                                        },
                                        label: '新预约'
                                      }
                                    ],
                                    columnDef: [
                                      {
                                        field: 'id',
                                        displayName: 'ID'
                                      },
                                      {
                                        field: 'date',
                                        displayName: '就诊时间'
                                      }
                                    ],
                                    event: {
                                      listItems: 'list-encounters',
                                      selectItem: 'select-encounter'
                                    },
                                    selectedLine: 0,
                                    myFavoriteId: '01306-PatAppointment-87'
                                  }
                                ]
                              },
                              data: {
                                type: 'window',
                                body: [
                                  {
                                    type: 'dataViewer',
                                    model: ''
                                  }
                                ]
                              }
                            }
                          },
                          detail: {
                            type: 'window',
                            themes: ['body-flex'],
                            body: [
                              {
                                type: 'stack',
                                style: {
                                  vars: {
                                    themeBorderColor: 'lightgreen'
                                  }
                                },
                                menu: [
                                  {
                                    id: 'appointment',
                                    displayName: '就诊'
                                  },
                                  {
                                    id: 'data',
                                    displayName: '数据'
                                  }
                                ],
                                myFavoriteId: '01306-PatAppointment-137',
                                tabPosition: 'top',
                                children: {
                                  appointment: {
                                    type: 'window',
                                    themes: ['body-flex'],
                                    head: [
                                      {
                                        type: 'toolbar',
                                        children: [
                                          {
                                            type: 'button',
                                            event: {
                                              name: 'save-encounter'
                                            },
                                            label: '保存预约'
                                          },
                                          {
                                            type: 'button',
                                            event: {
                                              name: 'generate-data'
                                            },
                                            label: '生成就诊数据'
                                          }
                                        ]
                                      }
                                    ],
                                    body: [
                                      {
                                        type: 'layout2',
                                        myFavoriteId: '01306-PatAppointment-523',
                                        children: [
                                          {
                                            type: 'panel',
                                            style: {
                                              main: {
                                                display: 'block',
                                                overflow: 'auto',
                                                border: 'none'
                                              }
                                            },
                                            children: [
                                              {
                                                type: 'section',
                                                title: '本次就诊信息',
                                                collapsible: true,
                                                myFavoriteId: '01306-PatAppointment-232',
                                                children: [
                                                  {
                                                    type: 'choice',
                                                    label: '就诊类型',
                                                    model: 'encounter.enc_type_c',
                                                    valid: {
                                                      required: true
                                                    },
                                                    metadata: 'ENCOUNTER.TYPE'
                                                  },
                                                  {
                                                    type: 'dateTime',
                                                    label: '就诊日期',
                                                    model: 'encounter.contact_date',
                                                    valid: {
                                                      required: true
                                                    }
                                                  },
                                                  {
                                                    type: 'inputText',
                                                    label: '医生ID',
                                                    model: 'encounter.provider_id'
                                                  },
                                                  {
                                                    type: 'inputText',
                                                    label: '科室ID',
                                                    model: 'encounter.department_id'
                                                  },
                                                  {
                                                    type: 'choice',
                                                    label: '预约状态',
                                                    model: 'encounter.appt_status_c',
                                                    options: [
                                                      {
                                                        label: '待确认',
                                                        value: '1'
                                                      },
                                                      {
                                                        label: '已确认',
                                                        value: '2'
                                                      },
                                                      {
                                                        label: '已取消',
                                                        value: '3'
                                                      }
                                                    ]
                                                  },
                                                  {
                                                    type: 'choice',
                                                    label: '就诊完成',
                                                    model: 'encounter.enc_closed_yn',
                                                    options: [
                                                      {
                                                        label: '是',
                                                        value: 'Y'
                                                      },
                                                      {
                                                        label: '否',
                                                        value: 'N'
                                                      }
                                                    ]
                                                  },
                                                  {
                                                    type: 'dateTime',
                                                    label: '签到时间',
                                                    model: 'encounter.checkin_time'
                                                  },
                                                  {
                                                    type: 'dateTime',
                                                    label: '签退时间',
                                                    model: 'encounter.checkout_time'
                                                  },
                                                  {
                                                    type: 'section',
                                                    title: '住院信息',
                                                    collapsible: true,
                                                    children: [
                                                      {
                                                        type: 'dateTime',
                                                        label: '入院时间',
                                                        model: 'encounter.hosp_admsn_time'
                                                      },
                                                      {
                                                        type: 'dateTime',
                                                        label: '出院时间',
                                                        model: 'encounter.hosp_disch_time'
                                                      },
                                                      {
                                                        type: 'inputText',
                                                        label: '住院账号',
                                                        model: 'encounter.hsp_account_id'
                                                      },
                                                      {
                                                        type: 'inputText',
                                                        label: '床位号',
                                                        model: 'encounter.bed_number'
                                                      },
                                                      {
                                                        type: 'choice',
                                                        label: '入院类型',
                                                        model: 'encounter.admission_type',
                                                        options: [
                                                          {
                                                            label: '普通',
                                                            value: '1'
                                                          },
                                                          {
                                                            label: '急诊',
                                                            value: '2'
                                                          },
                                                          {
                                                            label: '转院',
                                                            value: '3'
                                                          }
                                                        ]
                                                      },
                                                      {
                                                        type: 'inputText',
                                                        label: '出院去向',
                                                        model: 'encounter.discharge_disposition'
                                                      }
                                                    ]
                                                  }
                                                ]
                                              },
                                              {
                                                type: 'section',
                                                title: '基本信息',
                                                collapsible: true,
                                                myFavoriteId: '01306-PatAppointment-340',
                                                children: [
                                                  {
                                                    type: 'textDisplay',
                                                    template: 'thi is a test'
                                                  }
                                                ]
                                              }
                                            ]
                                          },
                                          {
                                            type: 'section',
                                            title: '编辑',
                                            collapsible: true,
                                            myFavoriteId: '01306-PatAppointment-354',
                                            children: [
                                              {
                                                type: 'dataViewer',
                                                model: ''
                                              }
                                            ]
                                          }
                                        ]
                                      }
                                    ]
                                  },
                                  data: {
                                    type: 'window',
                                    body: [
                                      {
                                        type: 'section',
                                        title: '工作内容',
                                        collapsible: true,
                                        myFavoriteId: '01306-PatAppointment-373',
                                        children: [
                                          {
                                            type: 'dataViewer',
                                            model: ''
                                          }
                                        ]
                                      }
                                    ]
                                  }
                                }
                              }
                            ]
                          }
                        },
                        data: {
                          type: 'window',
                          body: [
                            {
                              type: 'section',
                              title: '工作内容',
                              collapsible: true,
                              myFavoriteId: '01306-PatAppointment-373',
                              children: [
                                {
                                  type: 'dataViewer',
                                  model: ''
                                }
                              ]
                            }
                          ]
                        }
                      }
                    }
                  ],
                  subContext: {
                    path: 'detail',
                    default: {}
                  }
                }
              ]
            }
          ],
          subContext: {
            path: 'detail',
            default: {}
          }
        }
      }
    ]
  },
  dialog: {
    genPatData: {
      type: 'dialog',
      visible: true,
      modal: false,
      title: '生成患者数据',
      body: [
        {
          type: 'section',
          title: '查询',
          collapsible: true,
          myFavoriteId: '01306-PatAppointment-722',
          children: [
            {
              type: 'textarea',
              label: '提示词',
              model: 'prompt',
              style: {
                main: {
                  height: '20rem'
                }
              },
              event: {
                enter: {
                  name: 'confirm-dialog'
                }
              }
            }
          ]
        },
        {
          type: 'section',
          title: '数据',
          collapsible: true,
          myFavoriteId: '01306-PatAppointment-745',
          children: [
            {
              type: 'dataViewer',
              model: ''
            }
          ]
        }
      ],
      foot: [
        {
          type: 'button',
          event: {
            name: 'cancel-dialog'
          },
          label: '取消'
        },
        {
          type: 'button',
          event: {
            name: 'confirm-dialog'
          },
          label: '确定'
        }
      ]
    }
  },
  subContext: {
    path: '/appointment',
    default: {}
  }
}
