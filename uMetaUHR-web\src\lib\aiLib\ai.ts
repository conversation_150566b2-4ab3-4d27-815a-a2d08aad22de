import { dbGet } from '@/lib/dataMan'
import { extractJsonFromText, htmlToText, toHalfWidthPunctuation } from '@/lib/textFormater'
import { CacheManager } from '@/lib/CacheManager'
import { throttleAsync } from '@/lib/throttling'
import { useAI } from '@/lib/ai'
import { compileTemplate2 } from '@/components/action/TextProcessors/ext_handlebars'

const SOP_CACHE_KEY = 'sop_cache_key'
const cacheManager = CacheManager.getInstance<any>({
  strategy: 'indexeddb'
})

async function getFile(type: string, name: string) {
  const key = SOP_CACHE_KEY + type + name
  await cacheManager.clear(key)
  return await cacheManager.fetch(key, async () => {
    const sop =
      (await dbGet({
        sql: `SELECT *
            FROM "maxEMR".file_system
            WHERE type = ?::text
              and name = ?::text`,
        param: [type, name]
      })) || {}
    const content = sop?.data?.content
    if (!content) return ''
    return htmlToText(toHalfWidthPunctuation(content))
  })
}

async function tryThrottlingAI(content: string) {
  const throttlingAI = throttleAsync(async (content) => {
    console.log(content)
    const rst = await useAI(
      [
        { role: 'system', content: '' },
        { role: 'user', content }
      ],
      {}
    )
    console.log(rst)
    if (!rst.content) {
      throw new Error('retry ...')
    }
    return rst.content
  }, 10000)

  for (let attempt = 0; attempt < 3; attempt++) {
    try {
      return await throttlingAI(content)
    } catch (e) {
      console.error(`Attempt ${attempt + 1} failed:`, e)
      if (attempt === 2) {
        throw new Error('Max retries reached. Operation failed.')
      }
    }
  }
}

const alert = async (message: string) => {
  console.log(message)
  return window.popAlert({ message })
}

async function execAI2(prompt: string, context: any) {
  prompt = htmlToText(prompt)
  const filledPrompt = await compileTemplate2(prompt, context)
  console.log('filledPrompt', filledPrompt)
  await alert('processing...')

  const cnt = await tryThrottlingAI(filledPrompt)
  console.log('cnt', cnt)

  const aiData = {
    text: cnt,
    parsedData: extractJsonFromText(cnt) || {}
  }
  // await alert(JSON.stringify(aiData).substring(0, 200))
  console.log('aiData', aiData)
  await alert('done')
  return aiData
}

export { getFile, execAI2, tryThrottlingAI, alert }
