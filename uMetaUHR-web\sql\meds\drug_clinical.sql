DROP TABLE IF EXISTS drug_interaction;
DROP TABLE IF EXISTS adverse_reaction;


CREATE TABLE drug_interaction
(
    id             INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    drug_id1       INT NOT NULL,
    drug_id2       INT NOT NULL,
    severity       VARCHAR(20) NOT NULL,
    description    TEXT        NOT NULL,
    mechanism      TEXT,
    recommendation TEXT,
    evidence_level VARCHAR(20),
    created_at     BIGINT DEFAULT EXTRACT(EPOCH FROM NOW())*1000,
    updated_at     BIGINT DEFAULT EXTRACT(EPOCH FROM NOW())*1000,
    FOREIGN KEY (drug_id1) REFERENCES drug (id),
    FOREIGN KEY (drug_id2) REFERENCES drug (id),
    UNIQUE (drug_id1, drug_id2)
);

COMMENT ON TABLE drug_interaction IS '药品相互作用信息';
COMMENT ON COLUMN drug_interaction.drug_id1 IS '相互作用药物1';
COMMENT ON COLUMN drug_interaction.drug_id2 IS '相互作用药物2';
COMMENT ON COLUMN drug_interaction.severity IS '相互作用严重程度(严重/中度/轻度)';
COMMENT ON COLUMN drug_interaction.description IS '相互作用描述';
COMMENT ON COLUMN drug_interaction.mechanism IS '相互作用机制';
COMMENT ON COLUMN drug_interaction.recommendation IS '用药建议';
COMMENT ON COLUMN drug_interaction.evidence_level IS '相互作用证据等级';
COMMENT ON COLUMN drug_interaction.created_at IS '记录创建时间';
COMMENT ON COLUMN drug_interaction.updated_at IS '记录最后更新时间';

CREATE TABLE drug_adverse_reaction
(
    id            INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    drug_id       INT  NOT NULL,
    reaction_term VARCHAR(100) NOT NULL,
    frequency     VARCHAR(50),
    severity      VARCHAR(20),
    organ_system  VARCHAR(50),
    case_count    INT,
    source        VARCHAR(100),
    created_at    BIGINT DEFAULT EXTRACT(EPOCH FROM NOW())*1000,
    updated_at    BIGINT DEFAULT EXTRACT(EPOCH FROM NOW())*1000,
    FOREIGN KEY (drug_id) REFERENCES drug (id)
);

COMMENT ON TABLE drug_adverse_reaction IS '药品不良反应信息表';
COMMENT ON COLUMN drug_adverse_reaction.drug_id IS '引发不良反应的药品ID';
COMMENT ON COLUMN drug_adverse_reaction.reaction_term IS '不良反应术语';
COMMENT ON COLUMN drug_adverse_reaction.frequency IS '发生频率';
COMMENT ON COLUMN drug_adverse_reaction.severity IS '不良反应严重程度';
COMMENT ON COLUMN drug_adverse_reaction.organ_system IS '受累器官系统'; 
COMMENT ON COLUMN drug_adverse_reaction.case_count IS '报告病例数';
COMMENT ON COLUMN drug_adverse_reaction.source IS '数据来源';
COMMENT ON COLUMN drug_adverse_reaction.created_at IS '记录创建时间';
COMMENT ON COLUMN drug_dverse_reaction.updated_at IS '记录最后更新时间';
