DROP TABLE IF EXISTS drug_interaction;
DROP TABLE IF EXISTS adverse_reaction;


CREATE TABLE drug_interaction
(
    id             INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    drug_id1       INT NOT NULL,
    drug_id2       INT NOT NULL,
    severity       VARCHAR(20) NOT NULL,
    description    TEXT        NOT NULL,
    mechanism      TEXT,
    recommendation TEXT,
    evidence_level VARCHAR(20),
    created_at     BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
    updated_at     BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
    FOREIGN KEY (drug_id1) REFERENCES drug (id),
    FOREIGN KEY (drug_id2) REFERENCES drug (id),
    UNIQUE (drug_id1, drug_id2)
);

COMMENT ON TABLE drug_interaction IS 'Drug-drug interaction information';
COMMENT ON COLUMN drug_interaction.drug_id1 IS 'First drug in interaction';
COMMENT ON COLUMN drug_interaction.drug_id2 IS 'Second drug in interaction';
COMMENT ON COLUMN drug_interaction.severity IS 'Interaction severity (severe/moderate/mild)';
COMMENT ON COLUMN drug_interaction.description IS 'Interaction description';
COMMENT ON COLUMN drug_interaction.mechanism IS 'Interaction mechanism';
COMMENT ON COLUMN drug_interaction.recommendation IS 'Management recommendation';
COMMENT ON COLUMN drug_interaction.evidence_level IS 'Evidence level for interaction';
COMMENT ON COLUMN drug_interaction.created_at IS 'Timestamp when record was created';
COMMENT ON COLUMN drug_interaction.updated_at IS 'Timestamp when record was last updated';

CREATE TABLE adverse_reaction
(
    id            INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    drug_id       INT  NOT NULL,
    reaction_term VARCHAR(100) NOT NULL,
    frequency     VARCHAR(50),
    severity      VARCHAR(20),
    organ_system  VARCHAR(50),
    case_count    INT,
    source        VARCHAR(100),
    created_at    BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
    updated_at    BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
    FOREIGN KEY (drug_id) REFERENCES drug (id)
);

COMMENT ON TABLE adverse_reaction IS 'Adverse drug reaction information';
COMMENT ON COLUMN adverse_reaction.drug_id IS 'Drug causing the reaction';
COMMENT ON COLUMN adverse_reaction.reaction_term IS 'Adverse reaction term';
COMMENT ON COLUMN adverse_reaction.frequency IS 'Frequency of occurrence';
COMMENT ON COLUMN adverse_reaction.severity IS 'Severity of reaction';
COMMENT ON COLUMN adverse_reaction.organ_system IS 'Affected organ system';
COMMENT ON COLUMN adverse_reaction.case_count IS 'Number of reported cases';
COMMENT ON COLUMN adverse_reaction.source IS 'Data source';
COMMENT ON COLUMN adverse_reaction.created_at IS 'Timestamp when record was created';
COMMENT ON COLUMN adverse_reaction.updated_at IS 'Timestamp when record was last updated';
