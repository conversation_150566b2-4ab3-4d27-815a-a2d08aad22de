<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon/UI 5.0/toolbar/guaqi</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#808080" offset="0%"></stop>
            <stop stop-color="#BFBFBF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#262626" offset="0%"></stop>
            <stop stop-color="#8C8C8C" offset="100%"></stop>
        </linearGradient>
        <circle id="path-3" cx="17.5" cy="16.5" r="6.5"></circle>
    </defs>
    <g id="icon/UI-5.0/toolbar/guaqi" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <path d="M18.5,2.5 L18.5,19.5 L3.5,19.5 L3.5,2.5 L18.5,2.5 Z" id="形状结合" stroke="url(#linearGradient-2)" fill="url(#linearGradient-1)"></path>
        <path d="M6,6 L16,6 L16,7 L6,7 Z M6,9 L11,9 L11,10 L6,10 Z M6,12 L9,12 L9,13 L6,13 Z" id="形状结合" fill="#FFFFFF"></path>
        <path d="M7,1 L9,1 L9,4 L7,4 Z M13,1 L15,1 L15,4 L13,4 Z" id="形状结合" fill="#545454"></path>
        <g id="形状结合">
            <circle stroke="#FF8500" stroke-linejoin="square" fill="#FFDC86" fill-rule="evenodd" cx="17.5" cy="16.5" r="6"></circle>
            <circle stroke="#FFFFFF" cx="17.5" cy="16.5" r="7"></circle>
        </g>
        <rect id="矩形" fill="#FF8500" x="14" y="15.5" width="7" height="2"></rect>
    </g>
</svg>