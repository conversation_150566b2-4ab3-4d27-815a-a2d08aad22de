import type { DataTable } from "./table";
import { DataTablesEnum, QueryOperator, QueryOrder } from "../enums";
import type { Periods, VisitStatus, PatientRecord } from "./pat-list";

declare module 'axios' {
  interface AxiosRequestConfig {
    timeoutHandling?: boolean;
    loadingTimeout?: number;
    requestTimeout?: number;
  }
}

// 基础查询条件接口（修改op类型）
interface Condition<T = unknown> {
  column: string
  op: QueryOperator  // 改用枚举类型
  params: T
}

// 排序规则接口
interface OrderBy {
  field: string
  direction: QueryOrder
}

// 通用分页查询接口
export interface ListDataParams {
  table?: DataTable
  columns: string[]
  condition?: string
  orderBy?: OrderBy[]
  limit?: number
}

// 叫号PA列表查询接口
export interface PatListParams {
  startDate?: string       // 就诊日期起始范围
  endDate?: string        // 就诊日期结束范围
  visitStatus?: string   // 就诊状态: pending/inProgress/completed/missed
  patientName?: string   // 模糊匹配：患者姓名
  medicalCardNo?: string // 精确匹配：就诊卡号
  keyword?: string       // 模糊匹配：患者姓名/就诊卡号
  periods?: string[]     // 午别：支持复选（上午/下午）
  pageNum: number          // 分页页码
  pageSize: number         // 分页大小
}

// 患者基本信息查询接口
export interface PatientBasicInfoParams {
  patientId: string
}

// 患者扩展信息查询接口
export interface PatientExtensionInfoParams extends ListDataParams {
  table?: DataTablesEnum.PATIENT_EXTENSION_INFO
}

// 诊断记录查询接口
export interface DiagnosisParams extends ListDataParams {
  table?: DataTablesEnum.DIAGNOSIS
}

// 检查检验记录查询接口
export interface OrderProcParams extends ListDataParams {
  table: DataTablesEnum.ORDER_PROC
}


// 基础响应接口
interface BaseResponse {
  code: string
  msgCode: string
}

// 地址信息接口
interface Address {
  province: string
  city: string
  detail: string,
  district: string
}

// 过敏信息接口
interface Allergy {
  type: string
  name: string
  severity: string
  update_date: string
}

// 慢性病信息接口
interface ChronicCondition {
  code: string
  name: string
  diagnosis_date?: string
  treatment?: string
}

// 家族病史接口
interface FamilyHistory {
  member: string
  chronic_conditions: ChronicCondition[]
}

// 检查结果详情接口
interface CheckResult {
  check_type: string
  check_view: string
}

// 动态属性接口（诊断相关）
interface DiagnosisDynamicAttrs {
  chief_complaints: string
  diagnosis: string
  drug_prescription: string[]
  med_duratuion: string
}

// 以下是具体的响应接口
export interface PatientListResponse extends BaseResponse {
  data: {
    current: number
    pageSize: number
    total: number
    data: PatientRecord[]
  }
}

export interface PatientCountResponse extends BaseResponse {
  data:  {
    totalCount: number
    pendingCount: number
    inProgressCount: number
    completedCount: number
    missedCount: number
  }
}

export interface InsuranceInfo {
  number: string
  type: string
  valid_until: string
}

export interface PatientBaseInfo {
  address: Address
  birth_date: string
  gender: "M" | "F" | "U"
  id: string
  name: String
  id_card: string
  phone: string
  insurance_info: InsuranceInfo
}

export interface PatientBasicInfoResponse extends BaseResponse {
  data: PatientBaseInfo
}

interface EmergencyContact {
  name: string
  phone: string
  relation: string
}

export interface FamilyMemberHistory {
  disease: string[]
  relation: string
}

interface MedicalInfo {
  allergies: Allergy[]
  blood_type: string
  chronic_conditions: ChronicCondition[]
}

export interface PatientExtensionInfo {
  id: number
  occupation: string
  education_level: string
  marital_status: string
  emergency_contacts: string  // JSON字符串格式
  medical_info: MedicalInfo
  family_history: string     // JSON字符串格式
}


export interface PatientExtensionInfoResponse extends BaseResponse {
  data: PatientExtensionInfo
}

export interface DiagnosisResponse extends BaseResponse {
  data: {
    patient_id: string
    recorded_date: string
    recorded_name: string
    dynamic_attrs: DiagnosisDynamicAttrs
  }
}

export interface OrderProcResponse extends BaseResponse {
  data: Array<{
    patient_id: string
    order_date: string
    order_class_c: string
    result_details: CheckResult[]
  }>
}

export interface DiagnosisListParams {
  patient_id: number
  encounter_id?: number
}

export interface DiagnosisListResponse extends BaseResponse {
  data: {
    id:  number
    patient_id: number
    encounter_id: number
    diagnosis_id: number
    diagnosis_name: string
    diagnosis_modifier: JSON
    diagnosis_type: string
    diagnosis_status: string
    onset_date: number
    recorded_date: number
    recorded_by: number
    verified_by: number
    verified_date: number
    is_primary: boolean
    notes: string
    evidence_data: string
    timeline_data: string
    created_at: string
    updated_at: string
    code: string
  }
}

export interface DiagnosisSearchParams {
  keyword: string
}

export interface DiagnosisSearchResponse extends BaseResponse {
  data: {
    id:  number
    pid: string
    code: string
    name: string
    category_id: number
    sub_class: string
    sub_class_name: string
    description: string
    created_at: number
    updated_at: number
  }
}

export interface DiagnosisAddParams {
  patient_id: number
  diagnosis_id:  number
  diagnosis_name?: string
  diagnosis_type: string
  encounter_id: number
  diagnosis_status: string
  recorded_by: number
  diagnosis_modifier?: string
  is_primary?: boolean
}

export interface DiagnosisAddResponse extends BaseResponse {
  data: {
    id:  number
  }
}

export interface DiagnosisUpdateAffixParams {
  id: number
  prefix?:  string
  suffix?: string
}

export interface DiagnosisUpdateAffixResponse extends BaseResponse{
}
