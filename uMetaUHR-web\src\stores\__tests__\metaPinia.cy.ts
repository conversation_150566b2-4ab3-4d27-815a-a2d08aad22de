/// <reference types="cypress" />

import { createMetaPinia } from '../index'
import type { MetaPiniaData, MetaPiniaInstance } from '../metaPinia-types'

declare global {
  namespace Cypress {
    interface Chainable {
      wrap(subject: any, options?: any): Chainable
    }
  }
}

describe('MetaPinia Store', () => {
  const initialData: MetaPiniaData = {
    patients: [
      {
        id: 'p001',
        name: 'Patient One',
        orders: [{ id: 'o001', drug: 'Insulin', dose: '10IU', frequency: 'daily' }],
        labs: []
      },
      {
        id: 'p002',
        name: 'Patient Two',
        orders: [],
        labs: []
      }
    ],
    currentPatient: null
  }

  it('should initialize with provided data', () => {
    const store = createMetaPinia(initialData)
    cy.wrap(store.data).should('deep.equal', initialData)
  })

  describe('Path-based Data Access', () => {
    const store = createMetaPinia(initialData)

    it('should access top-level properties', () => {
      const result = store.use('patients', { reactive: true })
      cy.wrap(result).should('have.length', 2)
    })

    it('should access nested properties', () => {
      const result = store.use('patients[0].name', { reactive: true })
      if (result !== undefined) {
        cy.wrap({ value: result }).should('have.property', 'value', 'Patient One')
      } else {
        throw new Error('Expected result to be defined')
      }
    })

    it('should handle array indices', () => {
      const result = store.use('patients[1].id', { reactive: true })
      cy.wrap(result).should('equal', 'p002')
    })

    it('should return undefined for invalid paths', () => {
      const result = store.use('nonexistent.path', { reactive: true })
      cy.wrap(result).should('be.undefined')
    })

    it('should accept options parameter', () => {
      const result = store.use('patients', { reactive: true })
      cy.wrap(result).should('have.length', 2)
    })
  })

  describe('Medical Operations', () => {
    let store = createMetaPinia(initialData)

    beforeEach(() => {
      store = createMetaPinia(initialData)
    })

    it('should switch current patient', () => {
      store.switchPatient('p001')
      const patient = store.getCurrentPatient()
      if (patient) {
        cy.wrap({ id: patient.id }).should('have.property', 'id', 'p001')
      } else {
        throw new Error('Expected patient to be defined')
      }
    })

    it('should not switch to invalid patient', () => {
      store.switchPatient('invalid')
      const patient = store.getCurrentPatient()
      cy.wrap({ isNull: patient === null }).should('have.property', 'isNull', true)
    })

    // Order operations are now tested in orderStore.cy.ts
  })

  describe('Audit Logging', () => {
    let store: ReturnType<typeof createMetaPinia>

    beforeEach(() => {
      store = createMetaPinia(initialData)
    })

    it('should log patient switch', () => {
      store.switchPatient('p001')
      const log = store.getAuditLog()
      cy.wrap(log).should('have.length', 1)
      if (log[0]) {
        cy.wrap({ action: log[0].action })
          .should('have.property', 'action')
          .and('match', /切换患者 p001/)
      } else {
        throw new Error('Expected log entry to exist')
      }
    })

    it('should log custom actions', () => {
      store.logAction('Test action')
      const log = store.getAuditLog()
      if (log[0]) {
        cy.wrap({ action: log[0].action }).should('have.property', 'action', 'Test action')
      } else {
        throw new Error('Expected log entry to exist')
      }
    })
  })

  describe('Reactivity', () => {
    it('should return reactive data when requested', () => {
      const store = createMetaPinia(initialData)
      const reactivePatient = store.use('patients[0]', { reactive: true })
      cy.wrap(reactivePatient).should('exist')
    })
  })
})
