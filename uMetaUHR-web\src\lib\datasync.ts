// @/utils/waitForData.ts
import { watch } from 'vue'

/**
 * 等待特定数据满足条件或超时。
 * @param getData - 一个返回要监视的数据的 getter 函数。
 * @param predicate - 一个函数，用于判断数据是否满足条件。默认检查数据是否为真值。
 * @param timeout - 最大等待时间（毫秒）。默认值为30s。
 * @returns 一个 Promise，当数据满足条件时解析为数据本身，或在超时后拒绝。
 */
export function waitForData<T>(
  getData: () => T | undefined,
  predicate: (data: T | undefined) => boolean = (data) => !!data,
  timeout: number = 30000
): Promise<T> {
  return new Promise((resolve, reject) => {
    const currentData = getData()
    if (predicate(currentData)) {
      resolve(currentData as T)
      return
    }

    // 设置 watcher 监听数据的变化
    const stopWatch = watch(
      getData,
      (newVal) => {
        if (predicate(newVal)) {
          stopWatch() // 停止 watcher，防止内存泄漏
          clearTimeout(timer) // 清除超时定时器
          resolve(newVal as T)
        }
      },
      { immediate: true, deep: true }
    )

    // 设置超时机制
    const timer = setTimeout(() => {
      stopWatch() // 超时后停止 watcher
      reject(new Error('等待数据设置超时'))
    }, timeout)
  })
}
