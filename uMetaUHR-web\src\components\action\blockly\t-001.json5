{
  contents: [
    {
      kind: 'category',
      name: '逻辑',
      colour: 'green',
      contents: [
        {
          kind: 'block',
          type: 'controls_if'
        },
        {
          kind: 'block',
          type: 'controls_whileUntil'
        },
        {
          kind: 'block',
          type: 'logic_compare'
        },
        {
          kind: 'block',
          type: 'logic_operation'
        },
        {
          kind: 'block',
          type: 'logic_boolean'
        }
      ]
    },
    {
      kind: 'category',
      name: '业务',
      colour: '#38E',
      contents: [
        {
          kind: 'block',
          type: 'order_list2'
        },
        {
          kind: 'block',
          type: 'patient'
        },
        {
          kind: 'block',
          type: 'my_order'
        },
        {
          kind: 'block',
          type: 'text'
        }
      ]
    },
    {
      kind: 'category',
      name: '测试',
      colour: 'purple',
      contents: [
        {
          kind: 'block',
          type: 'this_is_a_block'
        }
      ]
    }
  ]
}
