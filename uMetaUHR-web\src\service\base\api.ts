import axios from "axios";
import { Urls } from "./urls";

import type {
  ListDataParams,
  DiagnosisListParams,
  DiagnosisSearchParams,
  DiagnosisAddParams,
  DiagnosisUpdateAffixParams,
  DiagnosisDeleteParams,
} from '../../types';

import type { SyncDataRequest, SyncDataResponse } from '../editor/api';

export const BaseApis = {
  listData: async <T>(param: ListDataParams): Promise<T> => {
    const response = await axios.post<T>(Urls.listData, param);
    return response.data;
  },
  
  getData: async <T>(param: ListDataParams): Promise<T> => {
    const response = await axios.post<T>(Urls.getData, param);
    return response.data;
  },

  listDiagnosis: async <T>(param: DiagnosisListParams): Promise<T> => {
    const response = await axios.post<T>(Urls.listDiagnosis, param);
    return response.data;
  },

  searchDiagnosis: async <T>(param: DiagnosisSearchParams): Promise<T> => {
    const response = await axios.post<T>(Urls.searchDiagnosis, param);
    return response.data;
  },

  addDiagnosis: async <T>(param: DiagnosisAddParams): Promise<T> => {
    const response = await axios.post<T>(Urls.addDiagnosis, param);
    return response.data;
  },

  updateDiagnosisAffix: async <T>(param: DiagnosisUpdateAffixParams): Promise<T> => {
    const response = await axios.post<T>(Urls.updateDiagnosisAffix, param);
    return response.data;
  },

  deleteDiagnosis: async <T>(param: DiagnosisDeleteParams): Promise<T> => {
    const response = await axios.delete<T>(Urls.deleteDiagnosis, { data: param });
  },

  // 批量获取结构化数据
  batchValue: async <T>(serverUrl: string, data: any): Promise<T> => {
    const response = await axios.post<T>(`${serverUrl}/api/emr/batchValue`, { data });
    return response.data;
  },

  // 编辑器相关接口（如果后端提供专门的批量接口）
  batchSyncData: async (param: SyncDataRequest): Promise<SyncDataResponse> => {
    const response = await axios.post<SyncDataResponse>(Urls.batchSyncData, param);
    return response.data;
  },
}