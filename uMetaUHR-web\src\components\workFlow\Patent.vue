<template>
  <div class="patent">
    <component
      :is="loadComponent(schema?.ui?.type)"
      v-if="schema"
      :context="computedContext"
      :schema="schema?.ui"
    />
  </div>
</template>

<script lang="ts" setup>
import { computed, defineProps, provide } from 'vue'
import { loadComponent } from '../componentRegistry'
import { getContext } from '@/lib/getContext'
import { dbGet, dbList, dbSave } from '@/lib/dataMan'
import { getAgent, useAI } from '@/lib/ai'
import { htmlToText } from '@/lib/textFormater'
import { compileTemplate2 } from '@/components/action/TextProcessors/ext_handlebars'

const props = defineProps<{
  schema: {
    type: string
    ui?: { type?: string }
    children: Array<any>
  }
  context: { [key: string]: any }
}>()

const computedContext = computed(() => getContext(props))

// Provide functions
provide('toc-list-data', () =>
  dbList({
    table: 'patent',
    columns: ['id', 'name']
  })
)

async function fetchData(param: any) {
  const { id } = param || {}
  if (!id) {
    const context = computedContext.value
    context['detail'] = {}
    return
  }

  let record = (await (id
    ? dbGet({
        table: 'patent',
        id,
        columns: ['id', 'data', 'name']
      })
    : Promise.resolve({}))) as any
  const { data = {}, data: { listOfData = [] } = {} } = record

  if (listOfData.length === 0) {
    const { data: { prompts = [] } = {} } = (await dbGet({ table: 'agent', id: 17 })) as any
    record.data.listOfData = prompts.map(({ name = '', question = '', answer = '' }) => ({
      name,
      question,
      answer
    }))
  }

  const context = computedContext.value
  context['detail'] = record
  return record
}

provide('toc-get-data', fetchData)
provide('new_patent', fetchData)

provide('agent-writer', async (param: any) => {
  const { name: tName, question, answer } = param?.context || {}
  const agentData = await getAgent('书写助手')
  const {
    config = {},
    prompts = [],
    role: { player_role = '', role_spec = '' } = {}
  } = agentData.data || {}
  const {
    detail: { data: { chat: { conversationChatBox = [] } = {} } = {} }
  } = computedContext.value

  if (!tName) {
    return await window.popAlert({ message: '未定义步骤名称。' })
  }

  await window.popAlert({ message: 'AI 辅助书写 ... ' })
  const prompt = prompts.findLast(({ name }: any) => name === tName)?.prompt
  if (!prompt) {
    return await window.popAlert({ message: '未找到提示词模版。' })
  }

  const content = await compileTemplate2(prompt, {
    role_spec,
    question,
    answer: htmlToText(answer)
  })
  const { content: cnt } = (await useAI(
    [
      { role: 'system', content: role_spec },
      {
        role: 'user',
        content
      }
    ],
    config
  )) as any

  conversationChatBox.push({ role: player_role, content: cnt })
  await window.popAlert({ message: '完毕 ... ' })
})

provide('toc-save-data', async () => {
  const { detail: data } = computedContext.value
  await dbSave({
    table: 'patent',
    data: [data],
    conflict_column: 'id',
    update_columns: ['id', 'data', 'name']
  })
})

provide('reply', async () => {
  const agentData = await getAgent('书写助手')
  const { config = {}, role: { player_role = '', role_spec = '' } = {} } = agentData.data || {}
  const {
    detail: { data: { chat = {}, chat: { conversationChatBox = [] } = {} } = {} }
  } = computedContext.value

  chat.conversationChatBox.push({ role: 'user', content: chat.reply })
  await window.popAlert({ message: '询问书写助手 ... ' })

  try {
    const { content: cnt } = (await useAI(
      [{ role: 'system', content: role_spec }, ...chat.conversationChatBox],
      config
    )) as any
    chat.conversationChatBox.push({ role: player_role, content: cnt })
  } catch (error) {
    await window.popAlert({ message: `结果：${JSON.stringify(error)}` })
  }
})

provide('clear', (param: any) => {
  const { context } = param
  context.conversationChatBox = []
})

defineExpose({ compName: props?.schema?.type || '-' })
</script>

<style lang="scss" scoped>
.patent {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  overflow: auto;
}
</style>
