CREATE TABLE function_points
(
    -- 核心标识字段
    id                   INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    function_point_code  VARCHAR(20)  NOT NULL,
    /* Unique security point code (e.g. 44000, 179)
     * Encoding rules:
     * - First 2 digits represent business domain (44=LTC, 17=Order Transmission)
     * - Predefined by Epic system, cannot be modified
     * Examples:
     * - 44000 = Allow Fatal Errors
     * - 179 = Ord Tran Rule Editor Full Access */
    CONSTRAINT uk_function_points_code UNIQUE (function_point_code),

    name                 VARCHAR(100) NOT NULL,
    /* 安全点标准名称，描述权限功能
     * 示例：
     * - "Preference List Editor"（偏好列表编辑器）
     * - "Flowsheet Editor"（流工作表编辑器）
     * 命名规范：
     * 1. 使用动词+名词结构（如"Edit SmartText"）
     * 2. 长度不超过100字符 */

    function_class       text,

    -- Permission control fields
    access_level         VARCHAR(20)  NOT NULL,
    CONSTRAINT ck_function_points_access_level CHECK (access_level IN ('Read', 'Write', 'Admin')),
    /* 权限级别定义：
     * - Read：只读权限（如180-Ord Tran Rule Editor Read Only）
     * - Write：编辑权限（可修改配置）
     * - Admin：管理权限（可分配权限给其他用户） */

    is_shared            BOOLEAN      NOT NULL DEFAULT FALSE,
    /* 是否为共享安全点（跨角色/部门共用）
     * TRUE示例：
     * - 32-Preference List Editor（偏好列表编辑器）
     * FALSE示例：
     * - 44003-Edit MDS Assessment Reference Date（编辑MDS评估参考日期）
     * 默认值FALSE表示通常为专属权限 */

    override_flag        BOOLEAN      NOT NULL DEFAULT FALSE,
    /* 是否允许覆盖系统警告
     * TRUE示例：
     * - 44000（允许推进存在致命错误的MDS评估）
     * FALSE示例：
     * - 366（标记警告为单独过滤）
     * 重要：临床人员通常不应具有此权限 */

    -- 业务关联字段
    module               VARCHAR(50)  NOT NULL,
    /* 关联的Epic模块名称
     * 示例：
     * - EpicCare（门诊）
     * - Inpatient（住院）
     * - Beacon（肿瘤）
     * 用于权限分类管理 */

    function_type        VARCHAR(50),
    /* 功能类型分类
     * 示例：
     * - Order（医嘱相关）
     * - Documentation（文书相关）
     * - Assessment（评估工具）
     * 用于构建权限矩阵 */

    required_system_def  jsonb        not null default '{}',
    /* 启用该安全点所需的系统定义配置
     * JSON格式存储，示例：
     * {
     *   "settingPath": "Clinical Administration > System Definitions",
     *   "requiredValue": "Allow users to filter warnings=Yes"
     * }
     * 用于自动验证权限启用条件 */

    -- 工作流控制字段
    acknowledge_required BOOLEAN      NOT NULL DEFAULT FALSE,
    /* 是否需要确认操作
     * TRUE示例：
     * - 195（需确认BPA最佳实践建议）
     * FALSE示例：
     * - 139（SmartForm基础编辑器）
     * 用于关键操作审计 */

    allow_filtering      BOOLEAN      NOT NULL DEFAULT FALSE,
    /* 是否允许用户自定义过滤
     * TRUE示例：
     * - 366（药物警告过滤）
     * FALSE示例：
     * - 44004（MDS评估导出）
     * 需配合System Definitions配置使用 */

    restrict_to_test     BOOLEAN      NOT NULL DEFAULT FALSE,
    /* 是否仅限测试环境使用
     * TRUE示例：
     * - 219（访问测试发布的SmartSets）
     * FALSE示例：
     * - 200（正式环境笔记模板编辑器） */

    -- 系统控制字段
    status               VARCHAR(20)  NOT NULL DEFAULT 'Active' CHECK (status IN ('Active', 'Deprecated', 'Draft')),
    /* 安全点状态：
     * - Active：生效中
     * - Deprecated：已弃用（保留历史记录）
     * - Draft：测试中
     * 默认Active确保新权限立即可用 */

    created_by           VARCHAR(50)  NOT NULL DEFAULT 'UII',
    /* 创建者标识
     * 系统预定义安全点为'EPIC'
     * 客户自定义安全点为创建人ID */

    release_version      VARCHAR(20),
    /* 关联的Epic版本号
     * 示例：'2014'、'2022'
     * 用于版本兼容性检查 */

    created_at           INT          NOT NULL DEFAULT EXTRACT(EPOCH FROM NOW()),
    /* Creation timestamp (seconds since epoch)
     * Auto-filled, cannot be modified
     * Used for audit tracking */

    updated_at           INT          NOT NULL DEFAULT EXTRACT(EPOCH FROM NOW()),
    /* 最后更新时间
     * 通过触发器自动维护
     * 用于识别权限变更 */

    deleted_at           INT,
    /* 删除时间(秒数), NULL表示未删除
     * Deletion timestamp (epoch seconds), NULL means not deleted */

    -- Constraints
    CONSTRAINT ck_override_requires_admin CHECK (
        override_flag = FALSE OR access_level = 'Admin'
        ) /* Ensures only Admin can override warnings */
);

-- Table comments
COMMENT ON TABLE function_points IS 'System function points defining all permission controls (安全点主表)';

-- Column comments
COMMENT ON COLUMN function_points.id IS 'Primary key identifier';
COMMENT ON COLUMN function_points.function_point_code IS 'Unique security point code';
COMMENT ON COLUMN function_points.name IS 'Security point display name';
COMMENT ON COLUMN function_points.function_class_id IS 'Associated security class ID';
COMMENT ON COLUMN function_points.access_level IS 'Access level: Read|Write|Admin';
COMMENT ON COLUMN function_points.is_shared IS 'Whether shared across roles/departments';
COMMENT ON COLUMN function_points.override_flag IS 'Allows overriding system warnings';
COMMENT ON COLUMN function_points.module IS 'Associated Epic module name';
COMMENT ON COLUMN function_points.function_type IS 'Functional type classification';
COMMENT ON COLUMN function_points.required_system_def IS 'Required system definitions (JSON)';
COMMENT ON COLUMN function_points.acknowledge_required IS 'Requires confirmation for actions';
COMMENT ON COLUMN function_points.allow_filtering IS 'Allows custom user filtering';
COMMENT ON COLUMN function_points.restrict_to_test IS 'Restricted to test environments';
COMMENT ON COLUMN function_points.status IS 'Status: Active|Deprecated|Draft';
COMMENT ON COLUMN function_points.created_by IS 'Creator identifier';
COMMENT ON COLUMN function_points.release_version IS 'Associated Epic release version';
COMMENT ON COLUMN function_points.created_at IS 'Creation timestamp (epoch seconds)';
COMMENT ON COLUMN function_points.updated_at IS 'Last update timestamp (epoch seconds)';
COMMENT ON COLUMN function_points.deleted_at IS 'Deletion timestamp (epoch seconds), NULL means not deleted / 删除时间(秒数), NULL表示未删除';

-- Indexes for performance
CREATE INDEX idx_function_points_code ON function_points (function_point_code);
CREATE INDEX idx_function_points_name ON function_points (name);
CREATE INDEX idx_function_points_module ON function_points (module);
CREATE INDEX idx_function_points_class ON function_points (function_class_id);
CREATE INDEX idx_function_points_status ON function_points (status) WHERE status = 'Active';
CREATE INDEX idx_function_points_type ON function_points (function_type) WHERE function_type IS NOT NULL;
