<template>
  <component
    :is="resolvedComponent"
    :ref="useRef"
    :context="getContext(props)"
    :schema="resolvedSchema"
  />
</template>

<script lang="ts" setup>
import { onMounted, shallowRef, watch } from 'vue'
import { loadSchema } from '@/schema/schemaRegistry'
import { loadComponent } from '@/components/componentRegistry'
import { getContext } from '@/lib/getContext'

// 定义组件实例引用
const comp = shallowRef(null)

function useRef(ref: any) {
  comp.value = ref
}

// 定义 props
const props = defineProps({
  schema: Object,
  context: Object,
  style: Object,
  externalSchema: String // 外部 schema 的标识符
})

// 响应式数据：已解析的 schema 和组件
const resolvedSchema = shallowRef(null)
const resolvedComponent = shallowRef(null)

// 加载 schema 的方法
const loadSchemaData = async () => {
  try {
    const { default: extSchema } = await loadSchema(props?.schema?.externalSchema)
    resolvedSchema.value = extSchema
  } catch (error) {
    console.error(`Failed to load schema for ${props.externalSchema}:`, error)
  }
}

// 加载组件的方法
const loadComponentData = () => {
  if (resolvedSchema?.value?.type) {
    resolvedComponent.value = loadComponent(resolvedSchema.value.type)
  } else {
    console.warn('No valid type found in resolved schema')
  }
}

// 加载 schema 和组件
const loadAndSetSchema = async () => {
  if (resolvedSchema.value && resolvedComponent.value) return
  await loadSchemaData()
  loadComponentData()
}

// 监听 externalSchema 的变化
watch(
  () => props.externalSchema,
  async () => {
    resolvedSchema.value = null
    resolvedComponent.value = null
    await loadAndSetSchema()
  }
)

// 在组件挂载时加载数据
onMounted(async () => {
  await loadAndSetSchema()
})

// 暴露组件名称
defineExpose({ compName: props?.schema?.type || '-' })
</script>
