{
  type: 'mdAgents',
  ui: {
    type: 'tocDetail',
    toc: {
      type: 'toc',
      columnDef: [
        {
          field: 'id',
          displayName: 'id'
        },
        {
          field: 'name',
          displayName: '名称'
        }
      ]
    },
    style: {
      toc: {
        width: '10rem'
      }
    },
    detail: {
      body: [
        {
          type: 'section',
          title: '基本信息',
          children: [
            {
              type: 'inputText',
              label: 'ID',
              model: 'id'
            },
            {
              type: 'inputText',
              label: '名称',
              model: 'name'
            }
          ]
        },
        {
          type: 'section',
          title: '模型',
          children: [
            {
              type: 'inputText',
              label: 'model',
              model: 'data.config.model'
            },
            {
              type: 'inputText',
              label: 'BaseURL',
              model: 'data.config.base_url'
            },
            {
              type: 'inputText',
              label: 'API key',
              model: 'data.config.api_key'
            }
          ]
        },
        {
          type: 'section',
          title: '业务',
          children: [
            {
              type: 'inputText',
              label: '角色',
              model: 'data.role.player_role'
            },
            {
              type: 'markdownViewer',
              label: '角色定义',
              model: 'data.role.role_spec',
              theme: 'editMode',
              editMode: false
            },
            {
              type: 'tocDetail',
              toc: {
                type: 'toc',
                columnDef: [
                  {
                    field: 'name',
                    displayName: '提示词'
                  }
                ],
                event: {
                  selectItem: 'promptSelectItem',
                  listItems: 'promptListItems',
                  shouldRefreshItems: 'promptRefreshItems'
                },
                model: ''
              },
              style: {
                main: {
                  margin: '5px 0px',
                  height: '25rem'
                },
                toc: {
                  width: '10rem'
                }
              },
              detail: {
                body: [
                  {
                    type: 'section',
                    style: {
                      main: {}
                    },
                    children: [
                      {
                        type: 'inputText',
                        label: '名称',
                        model: 'name'
                      }
                    ]
                  },
                  {
                    type: 'section',
                    style: {
                      main: {}
                    },
                    children: [
                      {
                        label: '问题',
                        type: 'markdownViewer',
                        model: 'question',
                        editMode: false
                      },
                      {
                        label: '参考答案',
                        type: 'markdownViewer',
                        model: 'answer',
                        editMode: false
                      },
                      {
                        label: '提示词',
                        type: 'markdownViewer',
                        model: 'prompt',
                        editMode: false
                      }
                    ]
                  }
                ],
                head: [
                  {
                    type: 'toolbar',
                    children: [
                      {
                        type: 'button',
                        event: {
                          name: 'promptNewItem'
                        },
                        label: '新建prompt'
                      },
                      {
                        type: 'button',
                        event: {
                          name: 'promptDeleteItem'
                        },
                        label: '删除'
                      }
                    ]
                  }
                ],
                type: 'window',
                subContext: {
                  path: '/temp.mdAgent.scratch2',
                  default: {}
                }
              }
            }
          ]
        },
        {
          type: 'section',
          title: '测试',
          children: [
            {
              type: 'toolbar',
              children: [
                {
                  type: 'button',
                  event: {
                    name: 'agent-validation'
                  },
                  label: '测试agent'
                }
              ]
            },
            {
              type: 'section',
              title: '测试角色',
              children: [
                {
                  type: 'richTextEditor',
                  model: 'data.testMessage'
                }
              ]
            },
            {
              type: 'section',
              title: '测试结果',
              children: [
                {
                  type: 'markdownViewer',
                  model: 'data.testResult'
                }
              ]
            }
          ]
        },
        {
          type: 'section',
          title: '数据',
          myFavoriteId: '00320-MD-Agents.section-3',
          children: [
            {
              type: 'dataViewer',
              model: ''
            }
          ]
        }
      ],
      head: [
        {
          type: 'toolbar',
          children: [
            {
              type: 'button',
              event: {
                name: 'toc-new-data'
              },
              label: '新建'
            },
            {
              type: 'button',
              event: {
                name: 'toc-save-data'
              },
              label: '保存'
            },
            {
              type: 'button',
              event: {
                name: 'toc-delete-data'
              },
              label: '删除'
            }
          ]
        }
      ],
      type: 'window',
      subContext: {
        path: 'detail',
        default: {}
      }
    }
  },
  subContext: {
    path: '/temp.mdAgent',
    default: {}
  }
}
