<template>
  <a-tooltip :title="getNestedValue(context, schema.model)">
    <span class="sensitive-text">{{ displayText }}</span>
  </a-tooltip>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { getNestedValue } from '@/lib/getContext'

interface SensitiveDisplaySchema {
  model: string
  attrs?: {
    prefixLen?: number
    suffixLen?: number
    middleLen?: number
    maskChar?: string
  }
}

const props = defineProps<{
  schema: SensitiveDisplaySchema
  context?: { [key: string]: any }
}>()

const displayText = computed(() => {
  const { model, attrs = {} } = props.schema
  const text = getNestedValue(props.context, model)
  if (!text) return '';
  const { 
    prefixLen = 3, 
    suffixLen = 4, 
    middleLen = 1 
  } = attrs
  
  if (text.length <= prefixLen + suffixLen + middleLen) {
    return text
  }

  const prefix = text.slice(0, prefixLen)
  const suffix = text.slice(-suffixLen)
  const middle = text.slice(prefixLen, -suffixLen)
  
  const { maskChar = '*' } = attrs
  const maskedMiddle = middle.length > middleLen 
    ? maskChar.repeat(middle.length)
    : middle

  return `${prefix}${maskedMiddle}${suffix}`
})
</script>

<style scoped>
.sensitive-text {
  cursor: default;
  user-select: none;
}
</style>
