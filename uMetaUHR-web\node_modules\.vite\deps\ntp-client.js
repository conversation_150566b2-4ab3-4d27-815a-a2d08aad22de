import {
  __commonJS
} from "./chunk-G3PMV62Z.js";

// browser-external:dgram
var require_dgram = __commonJS({
  "browser-external:dgram"(exports, module) {
    module.exports = Object.create(new Proxy({}, {
      get(_, key) {
        if (key !== "__esModule" && key !== "__proto__" && key !== "constructor" && key !== "splice") {
          console.warn(`Module "dgram" has been externalized for browser compatibility. Cannot access "dgram.${key}" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
        }
      }
    }));
  }
});

// node_modules/ntp-client/lib/ntp-client.js
var require_ntp_client = __commonJS({
  "node_modules/ntp-client/lib/ntp-client.js"(exports) {
    (function(exports2) {
      "use strict";
      var dgram = require_dgram();
      exports2.defaultNtpPort = 123;
      exports2.defaultNtpServer = "pool.ntp.org";
      exports2.ntpReplyTimeout = 1e4;
      exports2.getNetworkTime = function(server, port, callback) {
        if (callback === null || typeof callback !== "function") {
          return;
        }
        server = server || exports2.defaultNtpServer;
        port = port || exports2.defaultNtpPort;
        var client = dgram.createSocket("udp4"), ntpData = new Buffer(48);
        ntpData[0] = 27;
        for (var i = 1; i < 48; i++) {
          ntpData[i] = 0;
        }
        var timeout = setTimeout(function() {
          client.close();
          callback("Timeout waiting for NTP response.", null);
        }, exports2.ntpReplyTimeout);
        var errorFired = false;
        client.on("error", function(err) {
          if (errorFired) {
            return;
          }
          callback(err, null);
          errorFired = true;
          clearTimeout(timeout);
        });
        client.send(ntpData, 0, ntpData.length, port, server, function(err) {
          if (err) {
            if (errorFired) {
              return;
            }
            clearTimeout(timeout);
            callback(err, null);
            errorFired = true;
            client.close();
            return;
          }
          client.once("message", function(msg) {
            clearTimeout(timeout);
            client.close();
            var offsetTransmitTime = 40, intpart = 0, fractpart = 0;
            for (var i2 = 0; i2 <= 3; i2++) {
              intpart = 256 * intpart + msg[offsetTransmitTime + i2];
            }
            for (i2 = 4; i2 <= 7; i2++) {
              fractpart = 256 * fractpart + msg[offsetTransmitTime + i2];
            }
            var milliseconds = intpart * 1e3 + fractpart * 1e3 / 4294967296;
            var date = /* @__PURE__ */ new Date("Jan 01 1900 GMT");
            date.setUTCMilliseconds(date.getUTCMilliseconds() + milliseconds);
            callback(null, date);
          });
        });
      };
      exports2.demo = function(argv) {
        exports2.getNetworkTime(
          exports2.defaultNtpServer,
          exports2.defaultNtpPort,
          function(err, date) {
            if (err) {
              console.error(err);
              return;
            }
            console.log(date);
          }
        );
      };
    })(exports);
  }
});
export default require_ntp_client();
//# sourceMappingURL=ntp-client.js.map
