{
  type: 'login',
  ui: {
    type: 'panel',
    children: [
      {
        type: 'section',
        title: '登录',
        children: [
          {
            type: 'inputText',
            label: '姓名',
            model: 'username',
            validate: {
              required: true
            }
          },
          {
            type: 'inputText',
            label: '密码',
            inputType: 'password',
            model: 'password',
            validate: {
              required: true
            }
          }
        ]
      },
      {
        type: 'section',
        title: '注册信息',
        children: [
          {
            type: 'inputText',
            label: '再次输密码',
            inputType: 'password',
            model: 'data.password2',
            validate: {
              required: true
            }
          },
          {
            type: 'inputText',
            label: '真实姓名',
            model: 'data.fullName'
          }
        ],
        showWhen: "context.mode === 'register' || schema.purpose !== 'register'",
        purpose: 'register'
      },
      {
        type: 'toolbar',
        style: {
          'justify-content': 'right'
        },
        children: [
          {
            type: 'button',
            event: {
              name: 'login-register-start'
            },
            label: '开始注册',
            showWhen: "context.mode !== 'register' && schema.purpose === 'register'",
            purpose: 'register'
          },
          {
            type: 'button',
            event: {
              name: 'login-register'
            },
            label: '注册并登录',
            showWhen: "context.mode === 'register' && schema.purpose === 'register'",
            purpose: 'register'
          },
          {
            type: 'button',
            event: {
              name: 'login-login'
            },
            label: '登陆',
            showWhen: "context.mode !== 'register' && schema.purpose === 'register'",
            purpose: 'register'
          }
        ]
      }
    ],
    subContext: {
      path: 'user.loginInfo',
      default: {}
    }
  }
}
