CREATE TABLE manufacturer
(
    id              INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    name            <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    license_number  VARCHAR(50),
    address         VARCHAR(200),
    contact_phone   VARCHAR(20),
    website         VARCHAR(100),
    country         VARCHAR(50) DEFAULT '中国',
    gmp_certificate VARCHAR(50),
    created_at      BIGINT      DEFAULT EXTRACT(EPOCH FROM NOW()),
    updated_at      BIGINT      DEFAULT EXTRACT(EPOCH FROM NOW())
);

COMMENT ON TABLE manufacturer IS 'Drug manufacturer information';
COMMENT ON COLUMN manufacturer.name IS 'Manufacturer name';
COMMENT ON COLUMN manufacturer.license_number IS 'Manufacturing license number';
COMMENT ON COLUMN manufacturer.address IS 'Company address';
COMMENT ON COLUMN manufacturer.contact_phone IS 'Contact phone number';
COMMENT ON COLUMN manufacturer.website IS 'Company website';
COMMENT ON COLUMN manufacturer.country IS 'Country of origin (default China)';
COMMENT ON COLUMN manufacturer.gmp_certificate IS 'GMP certificate number';
COMMENT ON COLUMN manufacturer.created_at IS 'Timestamp when record was created';
COMMENT ON COLUMN manufacturer.updated_at IS 'Timestamp when record was last updated';
