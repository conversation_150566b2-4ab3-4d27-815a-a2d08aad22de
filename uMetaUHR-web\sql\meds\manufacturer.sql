CREATE TABLE manufacturer
(
    id              INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    name            VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    license_number  VARCHAR(50),
    address         VARCHAR(200),
    contact_phone   VARCHAR(20),
    website         VARCHAR(100),
    country         VARCHAR(50) DEFAULT '中国',
    gmp_certificate VARCHAR(50),
    created_at      BIGINT      DEFAULT EXTRACT(EPOCH FROM NOW()),
    updated_at      BIGINT      DEFAULT EXTRACT(EPOCH FROM NOW())
);

COMMENT ON TABLE manufacturer IS '药品生产企业信息表';
COMMENT ON COLUMN manufacturer.name IS '企业名称';
COMMENT ON COLUMN manufacturer.license_number IS '生产许可证号';
COMMENT ON COLUMN manufacturer.address IS '企业地址';
COMMENT ON COLUMN manufacturer.contact_phone IS '联系电话';
COMMENT ON COLUMN manufacturer.website IS '企业官网';
COMMENT ON COLUMN manufacturer.country IS '所在国家(默认中国)';
COMMENT ON COLUMN manufacturer.gmp_certificate IS 'GMP证书编号';
COMMENT ON COLUMN manufacturer.created_at IS '记录创建时间';
COMMENT ON COLUMN manufacturer.updated_at IS '记录最后更新时间';
