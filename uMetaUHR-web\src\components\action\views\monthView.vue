<template>
  <div class="month-view">
    <div class="weekdays-header">
      <div v-for="day in weekdayHeaders" :key="day" class="weekday">
        {{ day }}
      </div>
    </div>

    <div class="month-grid">
      <div v-for="(week, weekIndex) in monthWeeks" :key="weekIndex" class="week-row">
        <div
          v-for="day in week"
          :key="day.toISODate()"
          :class="{ 'current-month': day.month === currentDate.month }"
          class="day-cell"
          @dblclick="handleDayDoubleClick(day)"
          @drop="handleDrop($event, day)"
          @dragover.prevent="handleDragOver(day, $event)"
        >
          <div class="day-header">
            {{ day.day }}
          </div>
          <div class="day-events">
            <div
              v-for="event in dayEvents(day)"
              :key="event.id"
              :class="{ active: event.id === props?.activeEventId }"
              class="month-event"
              draggable="true"
              @dragstart="handleDragStart($event, event)"
              @click.stop="handleEdit(event)"
            >
              <div class="event-time">{{ formatTime(event) }}</div>
              <div class="event-title">{{ event.title }}</div>
            </div>
            <div v-if="dayEvents(day).length > 3" class="more-events">
              + {{ dayEvents(day).length - 3 }} more
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, defineProps, ref } from 'vue'
import { DateTime } from 'luxon'
import type { CalendarEvent } from '@/components/action/views/CalendarType'
import ServerTime from '@/lib/ServerTime'
import { getContext } from '@/lib/getContext'
import { isEmpty } from '@/lib/util'

const props = defineProps<{
  schema?: any
  context?: { [key: string]: any }
  currentDate: DateTime
  activeEventId?: string
}>()

const emit = defineEmits(['event-drop', 'edit-event', 'add-event', 'reload-events'])

const weekdayHeaders = computed(() => {
  return Array.from({ length: 7 }, (_, i) =>
    DateTime.local().startOf('week').plus({ days: i }).toFormat('EEE')
  )
})

const monthWeeks = computed(() => {
  const start = props.currentDate.startOf('month').startOf('week')
  const end = props.currentDate.endOf('month').endOf('week')

  const weeks = []
  let current = start

  while (current < end) {
    const week = Array.from({ length: 7 }, (_, i) => current.plus({ days: i }))
    weeks.push(week)
    current = current.plus({ week: 1 })
  }

  return weeks
})

const calculateNearest15MinuteInterval = (dateTime: DateTime): DateTime => {
  const minutes = dateTime.minute
  const remainder = minutes % 15
  if (remainder < 8) {
    return dateTime.minus({ minutes: remainder })
  } else {
    return dateTime.plus({ minutes: 15 - remainder })
  }
}

const handleDayDoubleClick = (day: DateTime) => {
  const now = DateTime.local()
  const roundedTime = calculateNearest15MinuteInterval(now)
  const newEventStartTime = day.set({
    hour: roundedTime.hour,
    minute: roundedTime.minute,
    second: 0,
    millisecond: 0
  })

  emit('add-event', newEventStartTime)
}

const dayEvents = (day: DateTime) => {
  let { events = [] } = getContext(props)
  if (isEmpty(events)) return []
  return events
    .filter((event: any) => DateTime.fromSeconds(event.startTime).hasSame(day, 'day'))
    .slice(0, 3) // Show max 3 events per day
}

const formatTime = (event: CalendarEvent) => {
  return ServerTime.fromSeconds(event.startTime).toFormat('HH:mm')
}

const draggedEvent = ref<CalendarEvent | null>(null)

const handleDragStart = (e: DragEvent, event: CalendarEvent) => {
  if (!e.dataTransfer) return

  e.dataTransfer.setData('text/plain', JSON.stringify(event))
  draggedEvent.value = event
}

const handleDragOver = (day: DateTime, e: DragEvent) => {
  e.preventDefault()
}

const handleDrop = (e: DragEvent, day: DateTime) => {
  const eventData = JSON.parse(e.dataTransfer?.getData('text/plain') || '{}')
  const zone = day.zoneName || 'Asia/Shanghai'
  const eventStartTime = DateTime.fromSeconds(eventData.startTime, { zone })
  const seconds = eventStartTime.diff(eventStartTime.startOf('day')).as('seconds')
  const newStart = day.startOf('day').plus({ seconds })

  emit('event-drop', eventData, newStart)
}

const handleEdit = (event: CalendarEvent) => emit('edit-event', event)
</script>

<style lang="scss" scoped>
.month-view {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: auto;
}

.weekdays-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  padding: 0.5rem;
  background: #eee;
  font-weight: bold;
  text-align: center;
}

.month-grid {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #ddd;
}

.week-row {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  flex: 1;
  min-height: 120px;
  border-bottom: 1px solid #ddd;
}

.day-cell {
  padding: 4px;
  border-right: 1px solid #ddd;
  background: #fff;

  &:last-child {
    border-right: none;
  }

  &.current-month {
    background: #f8f9fa;
  }
}

.day-header {
  font-weight: bold;
  text-align: right;
  padding: 4px;
  color: #666;

  .current-month & {
    color: #000;
  }
}

.day-events {
  overflow-y: auto;
  max-height: calc(100% - 28px);
}

.month-event {
  padding: 4px;
  margin: 2px;
  background: #e3f2fd;
  border-left: 3px solid #2196f3;
  border-radius: 3px;
  cursor: pointer;
  font-size: 0.8rem;

  &.active {
    background: #bbdefb;
    border-left-color: brown;
  }
}

.event-time {
  color: #666;
  font-size: 0.7rem;
}

.event-title {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.more-events {
  color: #666;
  font-size: 0.8rem;
  padding: 4px;
  cursor: pointer;
}
</style>
