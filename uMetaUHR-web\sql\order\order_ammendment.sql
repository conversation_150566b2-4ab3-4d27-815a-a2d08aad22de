CREATE TABLE order_amendment
(
    id                 INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    order_id           BIGINT      NOT NULL,
    amended_order_id   BIGINT,

    -- Amendment information
    amendment_type     VARCHAR(20) NOT NULL,
    amendment_reason   TEXT        NOT NULL,
    original_values    JSONB       NOT NULL,
    new_values         JSONB,

    -- Operation information
    amended_by         VARCHAR(50) NOT NULL,
    amendment_datetime BIGINT  DEFAULT EXTRACT(EPOCH FROM NOW()),
    approval_required  BOOLEAN DEFAULT FALSE,
    approval_status    VARCHAR(20),

    created_at         BIGINT  DEFAULT EXTRACT(EPOCH FROM NOW()),
    updated_at         BIGINT  DEFAULT EXTRACT(EPOCH FROM NOW()),

    FOREIGN KEY (order_id) REFERENCES medication_order (id),
    FOREIGN KEY (amended_order_id) REFERENCES medication_order (id)
);

COMMENT ON TABLE order_amendment IS 'Medication order amendment records';
COMMENT ON COLUMN order_amendment.order_id IS 'Original order ID';
COMMENT ON COLUMN order_amendment.amended_order_id IS 'New order ID (if modification rather than discontinuation)';
COMMENT ON COLUMN order_amendment.amendment_type IS 'Amendment type (MODIFY/DISCONTINUE/RENEW)';
COMMENT ON COLUMN order_amendment.amendment_reason IS 'Reason for amendment';
COMMENT ON COLUMN order_amendment.original_values IS 'Snapshot of original values';
COMMENT ON COLUMN order_amendment.new_values IS 'New values (for modifications)';
COMMENT ON COLUMN order_amendment.amended_by IS 'Staff ID who made the amendment';
COMMENT ON COLUMN order_amendment.amendment_datetime IS 'Amendment timestamp';
COMMENT ON COLUMN order_amendment.approval_required IS 'Whether approval is required';
COMMENT ON COLUMN order_amendment.approval_status IS 'Approval status';
COMMENT ON COLUMN order_amendment.created_at IS 'Timestamp when record was created';
COMMENT ON COLUMN order_amendment.updated_at IS 'Timestamp when record was last updated';
