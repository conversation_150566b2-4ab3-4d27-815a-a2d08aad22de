CREATE TABLE order_amendment
(
    id                 INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    order_id           BIGINT      NOT NULL,
    amended_order_id   BIGINT,

    -- Amendment information
    amendment_type     VARCHAR(20) NOT NULL,
    amendment_reason   TEXT        NOT NULL,
    original_values    JSONB       NOT NULL,
    new_values         JSONB,

    -- Operation information
    amended_by         VARCHAR(50) NOT NULL,
    amendment_datetime BIGINT  DEFAULT EXTRACT(EPOCH FROM NOW()),
    approval_required  BOOLEAN DEFAULT FALSE,
    approval_status    VARCHAR(20),

    created_at         BIGINT  DEFAULT EXTRACT(EPOCH FROM NOW()),
    updated_at         BIGINT  DEFAULT EXTRACT(EPOCH FROM NOW()),

    FOREIGN KEY (order_id) REFERENCES medication_order (id),
    FOREIGN KEY (amended_order_id) REFERENCES medication_order (id)
);

COMMENT ON TABLE order_amendment IS '药品医嘱修改记录表';
COMMENT ON COLUMN order_amendment.order_id IS '原始医嘱ID';
COMMENT ON COLUMN order_amendment.amended_order_id IS '新医嘱ID（如果是修改而非停用）';
COMMENT ON COLUMN order_amendment.amendment_type IS '修改类型（MODIFY-修改/DISCONTINUE-停用/RENEW-续用）';
COMMENT ON COLUMN order_amendment.amendment_reason IS '修改原因';
COMMENT ON COLUMN order_amendment.original_values IS '原始值快照';
COMMENT ON COLUMN order_amendment.new_values IS '新值（针对修改操作）';
COMMENT ON COLUMN order_amendment.amended_by IS '执行修改操作的员工ID';
COMMENT ON COLUMN order_amendment.amendment_datetime IS '修改时间戳';
COMMENT ON COLUMN order_amendment.approval_required IS '是否需要审批';
COMMENT ON COLUMN order_amendment.approval_status IS '审批状态';
COMMENT ON COLUMN order_amendment.created_at IS '记录创建时间戳';
COMMENT ON COLUMN order_amendment.updated_at IS '记录最后更新时间戳';