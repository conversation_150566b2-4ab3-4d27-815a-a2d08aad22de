CREATE TABLE order_execution
(
    id                   INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    order_id             BIGINT      NOT NULL,
    sequence_number      INT         NOT NULL,

    -- Execution information
    scheduled_time       BIGINT      NOT NULL,
    actual_time          BIGINT,
    administered_by      INT,
    administered_name    VARCHAR(100),

    -- Administration details
    administered_dose    JSONB,
    administration_notes TEXT,

    -- Execution status
    status               VARCHAR(20) NOT NULL,
    refusal_reason       TEXT,

    -- Signature verification
    witness_id           INT,
    signature_time       BIGINT,

    created_at           BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
    updated_at           BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),

    FOREIGN KEY (order_id) REFERENCES medication_order (id)
);

COMMENT ON TABLE order_execution IS 'Medication order execution records';
COMMENT ON COLUMN order_execution.order_id IS 'Reference to medication order';
COMMENT ON COLUMN order_execution.sequence_number IS 'Execution sequence number';
COMMENT ON COLUMN order_execution.scheduled_time IS 'Scheduled execution time';
COMMENT ON COLUMN order_execution.actual_time IS 'Actual execution time';
COMMENT ON COLUMN order_execution.administered_by IS 'Administering staff ID';
COMMENT ON COLUMN order_execution.administered_name IS 'Administering staff name';
COMMENT ON COLUMN order_execution.administered_dose IS 'Actual administered dose in JSON format';
COMMENT ON COLUMN order_execution.administration_notes IS 'Administration notes';
COMMENT ON COLUMN order_execution.status IS 'Execution status (SCHEDULED/GIVEN/REFUSED/MISSED)';
COMMENT ON COLUMN order_execution.refusal_reason IS 'Reason for refusal if applicable';
COMMENT ON COLUMN order_execution.witness_id IS 'Witness ID for high-risk medications';
COMMENT ON COLUMN order_execution.signature_time IS 'Signature timestamp';
COMMENT ON COLUMN order_execution.created_at IS 'Timestamp when record was created';
COMMENT ON COLUMN order_execution.updated_at IS 'Timestamp when record was last updated';

-- Indexes
CREATE INDEX idx_execution_order ON order_execution (order_id);
CREATE INDEX idx_execution_time ON order_execution (scheduled_time, actual_time);
CREATE INDEX idx_execution_status ON order_execution (status);
