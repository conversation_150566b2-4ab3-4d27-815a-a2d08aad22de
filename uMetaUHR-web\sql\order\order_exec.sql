CREATE TABLE order_execution
(
    id                   INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    order_id             BIGINT      NOT NULL,
    sequence_number      INT         NOT NULL,

    -- Execution information
    scheduled_time       BIGINT      NOT NULL,
    actual_time          BIGINT,
    administered_by      INT,
    administered_name    VARCHAR(100),

    -- Administration details
    administered_dose    JSONB,
    administration_notes TEXT,

    -- Execution status
    status               VARCHAR(20) NOT NULL,
    refusal_reason       TEXT,

    -- Signature verification
    witness_id           INT,
    signature_time       BIGINT,

    created_at           BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
    updated_at           BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),

    FOREIGN KEY (order_id) REFERENCES medication_order (id)
);

COMMENT ON TABLE order_execution IS '药品医嘱执行记录表';
COMMENT ON COLUMN order_execution.order_id IS '关联药品医嘱ID';
COMMENT ON COLUMN order_execution.sequence_number IS '执行序号';
COMMENT ON COLUMN order_execution.scheduled_time IS '计划执行时间';
COMMENT ON COLUMN order_execution.actual_time IS '实际执行时间';
COMMENT ON COLUMN order_execution.administered_by IS '执行人员ID';
COMMENT ON COLUMN order_execution.administered_name IS '执行人员姓名';
COMMENT ON COLUMN order_execution.administered_dose IS '实际给药剂量(JSON格式)';
COMMENT ON COLUMN order_execution.administration_notes IS '给药备注';
COMMENT ON COLUMN order_execution.status IS '执行状态(SCHEDULED-待执行/GIVEN-已执行/REFUSED-拒绝执行/MISSED-漏执行)';
COMMENT ON COLUMN order_execution.refusal_reason IS '拒绝执行原因(如适用)';
COMMENT ON COLUMN order_execution.witness_id IS '高风险药品见证人ID';
COMMENT ON COLUMN order_execution.signature_time IS '签名时间戳';
COMMENT ON COLUMN order_execution.created_at IS '记录创建时间戳';
COMMENT ON COLUMN order_execution.updated_at IS '记录最后更新时间戳';

-- Indexes
CREATE INDEX idx_execution_order ON order_execution (order_id);
CREATE INDEX idx_execution_time ON order_execution (scheduled_time, actual_time);
CREATE INDEX idx_execution_status ON order_execution (status);
