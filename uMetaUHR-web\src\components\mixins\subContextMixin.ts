import { ref } from 'vue'

export default function useSubContext() {
  const context = ref<{ [key: string]: any }>({})

  function processSchema(schema: any, parentContext: { [key: string]: any }) {
    // schema?.children?.forEach((child: any) => {
    //   if (child.subContext) {
    //     const subPath = child.subContext.path
    //     if (!parentContext[subPath]) {
    //       parentContext[subPath] = child.subContext.default
    //     }
    //     processSchema(child, parentContext[subPath])
    //   }
    // })
  }

  return {
    context,
    processSchema
  }
}
