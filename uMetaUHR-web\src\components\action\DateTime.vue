<template>
  <div v-if="isVisible" class="input-text">
    <label>
      <span class="label">
        {{ props?.schema?.label }}
        <span v-if="props?.schema?.valid?.required" class="required">*</span>
      </span>
      <input
        :key="inputKey"
        :class="{ 'input-error': error }"
        :type="props?.schema?.inputType ?? 'text'"
        :value="formattedValue"
        ref="inputRef"
        @blur="handleParse($event)"
        @keydown.enter="handleParse($event)"
      />
    </label>
    <span v-if="error" :title="error" class="error"> * </span>
  </div>
</template>

<script lang="ts" setup>
import { computed, defineProps, ref, watchEffect } from 'vue'
import useValidation from '../mixins/validationMixin'
import { evaluateShowWhen, getNestedValue, setNestedValue } from '@/lib/getContext'
import ServerTime from '@/lib/ServerTime'
import { DateTime } from 'luxon'
import { focusNextElement } from '@/lib/focusNextElement'

const props = defineProps<{
  schema: {
    type: string
    label?: string
    format?: string
    model?: string
    inputType?: string
    showWhen?: string
    valid?: {
      required?: boolean
    }
  }
  context: { [key: string]: any }
}>()

const { error, validate } = useValidation()
const inputKey = ref(1)
const inputRef = ref({})

// 定义公共假期列表
const publicHolidays: Set<string> = new Set([
  '2023-01-01', // 元旦
  '2023-01-22', // 春节
  '2023-04-05', // 清明节
  '2023-05-01', // 劳动节
  '2023-06-22', // 端午节
  '2023-09-29', // 中秋节
  '2023-10-01' // 国庆节
  // 可以根据需要添加更多假期
])

// 判断日期是否为工作日
function isWorkday(date: DateTime): boolean {
  const dayOfWeek: number = date.weekday // 1-7, 1=Monday, 7=Sunday
  // 如果是周六或周日，则不是工作日
  if (dayOfWeek === 6 || dayOfWeek === 7) {
    return false
  }

  // 在公共假期列表中，则不是工作日
  const dateString: string = date.toISODate() || '' // 转换为 YYYY-MM-DD 格式
  return !publicHolidays.has(dateString)
}

// 计算工作日 +n
function countWorkDays(startDate: DateTime, n: number): DateTime {
  if (n === 0) return startDate // 如果 n 为 0，直接返回起始日期

  const direction: number = n > 0 ? 1 : -1 // 确定日期的增减方向
  let workdaysCount: number = 0

  let currentDate: DateTime = startDate
  while (workdaysCount < Math.abs(n)) {
    currentDate = currentDate.plus({ days: direction })
    if (isWorkday(currentDate)) workdaysCount++
  }

  return currentDate
}

// 获取 tms 值
const tmsValue = computed(() => getNestedValue(props.context, props.schema?.model || ''))

// 将 tms 值格式化为可读字符串
const formattedValue = computed(() => {
  const tms = tmsValue.value
  let { schema: { format = 'MM/dd HH:mm' } = {} } = props
  return tms ? ServerTime.formatTms(tms, { format }) : ''
})

async function handleParse(event: any) {
  if (!event) return
  event?.preventDefault?.()
  event?.stopPropagation?.()
  await parseAndSetValue(event?.target?.value)
}

// 解析输入值并设置到上下文中
async function parseAndSetValue(value: string) {
  const parser = await import('@/pegrule/datetime')

  let parsedTms = null
  try {
    parsedTms =
      (value &&
        parser.parse(value, {
          getDateTime: () => DateTime.now(),
          countWorkDays
        })) ||
      []
  } catch (error) {
    console.error('Parsing error:', error)
  }

  // 将用户输入的时间字符串解析为 tms 值
  if (parsedTms == null) return
  setNestedValue(props.context, props?.schema?.model || '', ~~parsedTms)
  // Force input re-render by changing its key
  inputKey.value++
  focusNextElement(inputRef.value!)
}

// 控制组件是否可见
const isVisible = computed(() => evaluateShowWhen(props))

watchEffect(() => {
  if (props.schema?.valid?.required) {
    validate(tmsValue.value, {
      required: props.schema.valid.required
    })
  }
})

defineExpose({ compName: props?.schema?.type || '-' })
</script>

<style lang="scss" scoped>
.input-text {
  display: flex;
  flex-wrap: nowrap;
  margin: 2px;

  label {
    display: flex;
    flex-wrap: nowrap;
    flex: 1 1 auto;

    span {
      min-width: 5rem;
      white-space: nowrap;
      text-align: right;
      padding-right: 0.5rem;

      &.required {
        color: red;
      }
    }

    input {
      width: 100%;
      min-width: 5rem;
      border-radius: 0.2rem;
      border: 1px solid #ccc;
    }
  }

  .error {
    color: red;
    margin-left: 0.5rem;
  }
}
</style>
