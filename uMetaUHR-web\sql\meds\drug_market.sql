CREATE TABLE drug_price
(
    id             INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    drug_id        INT    NOT NULL,
    price          DECIMAL(10, 2) NOT NULL,
    currency       VARCHAR(3) DEFAULT 'CNY',
    price_type     VARCHAR(20)    NOT NULL,
    region         VARCHAR(50),
    effective_date BIGINT        NOT NULL,
    expiry_date    BIGINT     DEFAULT NULL,
    source         VARCHAR(100),
    created_at     BIGINT     DEFAULT EXTRACT(EPOCH FROM NOW())*1000,
    updated_at     BIGINT     DEFAULT EXTRACT(EPOCH FROM NOW())*1000,
    FOREIGN KEY (drug_id) REFERENCES drug (id)
);

COMMENT ON TABLE drug_price IS '药品价格信息表';
COMMENT ON COLUMN drug_price.drug_id IS '关联药品ID';
COMMENT ON COLUMN drug_price.price IS '价格金额';
COMMENT ON COLUMN drug_price.currency IS '货币代码(默认CNY)';
COMMENT ON COLUMN drug_price.price_type IS '价格类型(零售/招标等)';
COMMENT ON COLUMN drug_price.region IS '适用地区';
COMMENT ON COLUMN drug_price.effective_date IS '价格生效日期';
COMMENT ON COLUMN drug_price.expiry_date IS '价格失效日期';
COMMENT ON COLUMN drug_price.source IS '数据来源';
COMMENT ON COLUMN drug_price.created_at IS '记录创建时间';

CREATE TABLE drug_property
(
    id             INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    drug_id        INT  NOT NULL,
    property_name  VARCHAR(50)  NOT NULL,
    property_value VARCHAR(200) NOT NULL,
    source         VARCHAR(100),
    effective_date BIGINT        NOT NULL,
    expiry_date    BIGINT     DEFAULT NULL,
    created_at     BIGINT     DEFAULT EXTRACT(EPOCH FROM NOW())*1000,
    updated_at     BIGINT     DEFAULT EXTRACT(EPOCH FROM NOW())*1000,
    FOREIGN KEY (drug_id) REFERENCES drug (id)
);

COMMENT ON TABLE drug_property IS '药品属性信息表';
COMMENT ON COLUMN drug_property.drug_id IS '关联药品ID';
COMMENT ON COLUMN drug_property.property_name IS '属性名称(如："医保药品")';
COMMENT ON COLUMN drug_property.property_value IS '属性值';
COMMENT ON COLUMN drug_property.source IS '数据来源';
COMMENT ON COLUMN drug_property.effective_date IS '属性生效日期';
COMMENT ON COLUMN drug_property.expiry_date IS '属性失效日期';
COMMENT ON COLUMN drug_property.created_at IS '记录创建时间';
