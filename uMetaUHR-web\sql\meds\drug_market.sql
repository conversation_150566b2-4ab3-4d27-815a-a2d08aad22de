CREATE TABLE drug_price
(
    id             INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    drug_id        INT    NOT NULL,
    price          DECIMAL(10, 2) NOT NULL,
    currency       VARCHAR(3) DEFAULT 'CNY',
    price_type     VARCHAR(20)    NOT NULL,
    region         VARCHAR(50),
    effective_date DATE           NOT NULL,
    expiry_date    DATE,
    source         VARCHAR(100),
    created_at     BIGINT     DEFAULT EXTRACT(EPOCH FROM NOW()),
    updated_at     BIGINT     DEFAULT EXTRACT(EPOCH FROM NOW()),
    FOREIGN KEY (drug_id) REFERENCES drug (id)
);

COMMENT ON TABLE drug_price IS 'Drug pricing information';
COMMENT ON COLUMN drug_price.drug_id IS 'Reference to drug table';
COMMENT ON COLUMN drug_price.price IS 'Price amount';
COMMENT ON COLUMN drug_price.currency IS 'Currency code (default CNY)';
COMMENT ON COLUMN drug_price.price_type IS 'Price type (retail/bid/etc)';
COMMENT ON COLUMN drug_price.region IS 'Applicable region';
COMMENT ON COLUMN drug_price.effective_date IS 'Date when price becomes effective';
COMMENT ON COLUMN drug_price.expiry_date IS 'Date when price expires';
COMMENT ON COLUMN drug_price.source IS 'Data source';
COMMENT ON COLUMN drug_price.created_at IS 'Timestamp when record was created';

CREATE TABLE drug_property
(
    id             INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    drug_id        INT  NOT NULL,
    property_name  VARCHAR(50)  NOT NULL,
    property_value VARCHAR(200) NOT NULL,
    source         VARCHAR(100),
    effective_date DATE,
    expiry_date    DATE,
    created_at     BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
    updated_at     BIGINT DEFAULT EXTRACT(EPOCH FROM NOW()),
    FOREIGN KEY (drug_id) REFERENCES drug (id)
);

COMMENT ON TABLE drug_property IS 'Drug property information';
COMMENT ON COLUMN drug_property.drug_id IS 'Reference to drug table';
COMMENT ON COLUMN drug_property.property_name IS 'Property name (e.g. "is_insurance")';
COMMENT ON COLUMN drug_property.property_value IS 'Property value';
COMMENT ON COLUMN drug_property.source IS 'Data source';
COMMENT ON COLUMN drug_property.effective_date IS 'Date when property becomes effective';
COMMENT ON COLUMN drug_property.expiry_date IS 'Date when property expires';
COMMENT ON COLUMN drug_property.created_at IS 'Timestamp when record was created';
