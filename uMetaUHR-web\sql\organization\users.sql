drop table if exists users cascade;


CREATE TABLE users
(
    -- 核心标识
    id                  INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    user_id             VARCHAR(18)  NOT NULL UNIQUE,        -- 用户唯一ID (EMP.1)
    name                VARCHAR(100) NOT NULL,               -- 用户姓名 (EMP.2)
    prov_id             VARCHAR(18),                         -- 关联的provider ID (EMP 17500)
    user_status_c       INT          NOT NULL DEFAULT 1,     -- 用户状态：1=active, 0=inactive (EMP 50)
    phone               VARCHAR(20),                         -- 电话号码 (EMP 140)
    login_department_id VARCHAR(18),                         -- 登录部门ID (EMP 20660)
    user_alias          VARCHAR(50),                         -- 用户别名 (EMP 180)
    is_supervisor       BOOLEAN      NOT NULL DEFAULT FALSE, -- 是否为主管 (EMP 13160)

    -- 安全信息
    security_class_id   VARCHAR(18),                         -- 安全类别ID
    default_role        VARCHAR(50),                         -- 默认角色

    -- 时间信息
    last_access_date    INT,                                 -- 最后访问日期 (EPT 21891)
    created_at          INT          NOT NULL,               -- 创建时间(秒数)
    updated_at          INT          NOT NULL,               -- 更新时间(秒数)
    deleted_at          INT                                  -- 删除时间(秒数), NULL表示未删除
);
COMMENT ON TABLE users IS '系统用户表，存储所有可以登录系统的用户信息，包括临床和非临床用户';

-- 列注释
COMMENT ON COLUMN users.id IS '自增主键';
COMMENT ON COLUMN users.user_id IS '用户唯一业务ID (EMP.1)';
COMMENT ON COLUMN users.name IS '用户姓名 (EMP.2)';
COMMENT ON COLUMN users.prov_id IS '关联的provider ID (EMP 17500)';
COMMENT ON COLUMN users.user_status_c IS '用户状态：1=active, 0=inactive (EMP 50)';
COMMENT ON COLUMN users.phone IS '电话号码 (EMP 140)';
COMMENT ON COLUMN users.login_department_id IS '登录部门ID (EMP 20660)';
COMMENT ON COLUMN users.user_alias IS '用户别名 (EMP 180)';
COMMENT ON COLUMN users.is_supervisor IS '是否为主管 (EMP 13160)';
COMMENT ON COLUMN users.security_class_id IS '安全类别ID';
COMMENT ON COLUMN users.default_role IS '默认角色';
COMMENT ON COLUMN users.last_access_date IS '最后访问日期(秒数) (EPT 21891)';
COMMENT ON COLUMN users.created_at IS '创建时间(秒数)';
COMMENT ON COLUMN users.updated_at IS '更新时间(秒数)';
COMMENT ON COLUMN users.deleted_at IS '删除时间(秒数), NULL表示未删除';
