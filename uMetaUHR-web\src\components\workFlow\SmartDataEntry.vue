<template>
  <div class="master-data">
    <component
      :is="loadComponent(props.schema.ui?.type)"
      v-if="props.schema"
      :ref="useRef"
      :context="curContext"
      :schema="props.schema.ui"
    />
  </div>
</template>

<script lang="ts" setup>
import { defineProps, provide, reactive, shallowRef } from 'vue'
import { loadComponent } from '../componentRegistry'
import { getContext } from '@/lib/getContext'
import { dbGet, dbList, dbSave } from '@/lib/dataMan'
import { isEmpty } from '@/lib/util'

const comp = shallowRef(null)

function useRef(ref: any) {
  comp.value = ref
}

// Define props
const props = defineProps<{
  schema: {
    type: string
    children: Array<any>
  }
  context: { [key: string]: any }
}>()

// Reactive context
const curContext = reactive(getContext(props))
let queryParam = {}
let menuId = ''

// Error handler
const showError = async (message: string) => await window.popAlert({ message })

// Provide: List data for TOC
provide('toc-list-data', async (param: any = {}) => {
  const {
    queryParam: qp = {},
    queryParam: { query: { search: { table = '', tocItems: columns = [] } = {} } = {} } = {}
  } = param

  queryParam = qp

  if (!table) return await showError('Database table is not defined.')
  if (isEmpty(columns)) return await showError('Database columns are not defined.')

  return dbList({ table, columns })
})

// // Provide: Get data for TOC
let tocGetData = async (param: { id?: string } = {}) => {
  const { id } = param
  if (!id) return await showError('Data ID is not defined.')
  const { query: { search: { table = '', detailItems: columns = [] } = {} } = {} } = queryParam
  return dbGet({ table, id, columns })
}
provide('toc-get-data', tocGetData)

// Provide: Save data for TOC
provide('toc-save-data', async () => {
  const { detail: data } = curContext
  const { query: { search: { table = '', updateItems: updateColumns = [] } = {} } = {} } =
    queryParam

  if (isEmpty(updateColumns)) return await showError('Database columns are not defined.')

  const result = await dbSave({
    table,
    data: [data],
    conflict_column: 'id',
    update_columns: updateColumns
  })

  return result
})
defineExpose({ compName: props?.schema?.type || '-' })
</script>

<style lang="scss" scoped>
.master-data {
  display: flex;
  flex-direction: column;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
</style>
