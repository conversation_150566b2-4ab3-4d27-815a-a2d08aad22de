import { dbGet } from '@/lib/dataMan'
import { cacheManager } from '@/lib/CacheManager'

export async function getCodeMapFromMetadata(metadataCode: string) {
  const cacheKey = `metadataOptions:${metadataCode}`
  return cacheManager.fetch(
    cacheKey,
    async () => {
      const sql = `
        SELECT *
        FROM "maxEMR".metadata_info m
        WHERE code = ?::text
      `
      const data = await dbGet({
        sql,
        param: [metadataCode]
      })

      if (!data?.codevalue) {
        return {}
      }

      const result: Record<string, string> = {}
      JSON.parse(data.codevalue).forEach(({ code, name }: any) => {
        result[code] = name
      })
      return result
    },
    3600 * 24 // Cache for 1 day
  )
}
