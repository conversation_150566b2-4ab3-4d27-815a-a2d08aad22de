import { cacheManager } from '@/lib/CacheManager'
import { dbSvc } from '@/service/base/dbSvc'
import { Urls } from '@/service/base/urls'

export async function getCodeMapFromMetadata(metadataCode: string) {
  const cacheKey = `metadataOptions:${metadataCode}`
  return cacheManager.fetch(
    cacheKey,
    async () => {
      const [data] =
        (await dbSvc(Urls.xQuery, 'xGetCodeMapFromMetadata', { code: metadataCode })) || []
      if (!data?.codevalue) {
        return {}
      }

      const result: Record<string, string> = {}
      JSON.parse(data.codevalue).forEach(({ code, name }: any) => {
        result[code] = name
      })
      return result
    },
    3600 * 24 // Cache for 1 day
  )
}
