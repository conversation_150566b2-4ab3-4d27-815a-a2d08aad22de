// CacheManager.ts

type CacheStrategy = 'memory' | 'indexeddb' | 'both';

interface CacheManagerOptions {
  ttl?: number; // 默认 TTL（秒）
  strategy?: CacheStrategy; // 缓存策略选择
  persist?: boolean; // 启用持久化存储（仅适用于内存缓存）
  storageKey?: string; // 持久化存储的键名（仅适用于内存缓存）
}

interface CacheRecord<T> {
  key: string;
  data: T;
  expireAt: number; // 过期时间戳
}

class CacheManager<T = any> {
  private static instance: CacheManager<any> | null = null
  private static db: IDBDatabase | null = null // 静态 IndexedDB 实例
  private static isIndexedDBSupported: boolean = true
  private static dbPromise: Promise<void> | null = null // 确保单一初始化
  private static cleanupInitialized: boolean = false // 确保清理机制只初始化一次
  private memoryCache: Record<string, CacheRecord<T>> = {}
  private options: CacheManagerOptions

  // 私有构造函数，防止外部实例化
  private constructor(options: CacheManagerOptions = {}) {
    this.options = {
      ttl: 24 * 3600, // 默认 TTL 为 24 小时
      strategy: 'indexeddb',
      persist: false,
      storageKey: 'cache_manager',
      ...options
    }

    // 根据策略选择缓存机制
    const { strategy } = this.options
    const useMemory = strategy === 'memory' || strategy === 'both'
    const useIndexedDB = strategy === 'indexeddb' || strategy === 'both'

    // 初始化 IndexedDB
    if (useIndexedDB) {
      this.initIndexedDB().catch((error) => {
        console.error('IndexedDB 初始化失败，切换到内存缓存。', error)
        this.options.strategy = 'memory'
      })
    }

    // 如果启用持久化存储且使用内存缓存，则加载内存缓存
    if (useMemory && this.options.persist) {
      this.loadFromLocalStorage()
    }

    // 设置定时自动清理内存缓存中的过期数据
    if (useMemory) {
      this.setupMemoryCleanup()
    }
  }

  /**
   * 获取 CacheManager 的单例实例
   * @param options 配置选项
   * @returns CacheManager 实例
   */
  public static getInstance<U = any>(options?: CacheManagerOptions): CacheManager<U> {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager<U>(options)
    }
    return CacheManager.instance
  }

  /**
   * 设置缓存数据
   * @param key 缓存键
   * @param value 缓存值
   * @param ttl 可选的 TTL（秒）
   */
  public async set(key: string, value: T, ttl?: number): Promise<void> {
    if (!key) return

    const expireAt = Date.now() + ((ttl ?? this.options.ttl!) * 1000)
    const record: CacheRecord<T> = { key, data: value, expireAt }

    // 使用内存缓存
    if (this.isMemoryEnabled()) {
      this.memoryCache[key] = record
      if (this.options.persist) {
        this.saveToLocalStorage()
      }
    }

    // 使用 IndexedDB
    if (this.isIndexedDBEnabled()) {
      try {
        await this.ensureIndexedDBInitialized()
        if (CacheManager.db) {
          const tx = CacheManager.db.transaction('cache', 'readwrite')
          const store = tx.objectStore('cache')
          store.put(record)
          await new Promise<void>((resolve, reject) => {
            tx.oncomplete = () => resolve()
            tx.onerror = () => reject(tx.error)
            tx.onabort = () => reject(tx.error)
          })
        }
      } catch (error) {
        console.error('存储 IndexedDB 缓存数据失败:', error)
        if (this.isMemoryEnabled()) {
          console.warn('将数据仅存储到内存缓存。')
          // 如果 IndexedDB 存储失败，保持数据在内存缓存中
        }
      }
    }
  }

  /**
   * 获取缓存数据
   * @param key 缓存键
   * @returns 缓存值或 undefined
   */
  public async get(key: string): Promise<T | undefined> {
    if (!key) return {} as T

    const now = Date.now()

    // 先从内存缓存获取
    if (this.isMemoryEnabled()) {
      const record = this.memoryCache[key]
      if (record) {
        if (record.expireAt > now) {
          return record.data
        } else {
          await this.clear(key)
        }
      }
    }

    // 再从 IndexedDB 获取
    if (this.isIndexedDBEnabled()) {
      try {
        await this.ensureIndexedDBInitialized()
        if (CacheManager.db) {
          const tx = CacheManager.db.transaction('cache', 'readonly')
          const store = tx.objectStore('cache')
          const request = store.get(key)
          const record: CacheRecord<T> | undefined = await new Promise((resolve, reject) => {
            request.onsuccess = () => resolve(request.result)
            request.onerror = () => reject(request.error)
          })

          if (record) {
            if (record.expireAt > now) {
              if (this.isMemoryEnabled()) {
                this.memoryCache[key] = record
                if (this.options.persist) {
                  this.saveToLocalStorage()
                }
              }
              return record.data
            } else {
              await this.clear(key)
            }
          }
        }
      } catch (error) {
        console.error('获取 IndexedDB 缓存数据失败:', error)
      }
    }

    return undefined as T
  }

  /**
   * 获取缓存数据，如果未命中则调用 fetchFunction 获取并缓存
   * @param key 缓存键
   * @param fetchFunction 获取数据的异步函数
   * @param ttl 可选的 TTL（秒）
   * @returns 缓存值
   */
  public async fetch(key: string, fetchFunction: () => Promise<T>, ttl?: number): Promise<T> {
    const cached = await this.get(key)
    if (cached !== undefined) {
      return cached
    }

    try {
      const data = await fetchFunction()
      await this.set(key, data, ttl)
      return data
    } catch (error) {
      console.error(`获取数据失败并缓存时出错: key=${key}`, error)
      throw error
    }
  }

  /**
   * 清除特定的缓存条目
   * @param key 缓存键
   */
  public async clear(key: string): Promise<void> {
    // 清除内存缓存
    if (this.isMemoryEnabled()) {
      delete this.memoryCache[key]
      if (this.options.persist) {
        this.saveToLocalStorage()
      }
    }

    // 清除 IndexedDB 缓存
    if (this.isIndexedDBEnabled()) {
      try {
        await this.ensureIndexedDBInitialized()
        if (CacheManager.db) {
          const tx = CacheManager.db.transaction('cache', 'readwrite')
          const store = tx.objectStore('cache')
          store.delete(key)
          await new Promise<void>((resolve, reject) => {
            tx.oncomplete = () => resolve()
            tx.onerror = () => reject(tx.error)
            tx.onabort = () => reject(tx.error)
          })
        }
      } catch (error) {
        console.error('删除 IndexedDB 缓存数据失败:', error)
      }
    }
  }

  /**
   * 清除所有缓存数据
   */
  public async clearAll(): Promise<void> {
    // 清除内存缓存
    if (this.isMemoryEnabled()) {
      this.memoryCache = {}
      if (this.options.persist) {
        localStorage.removeItem(this.options.storageKey || '')
      }
    }

    // 清除 IndexedDB 缓存
    if (this.isIndexedDBEnabled()) {
      try {
        await this.ensureIndexedDBInitialized()
        if (CacheManager.db) {
          const tx = CacheManager.db.transaction('cache', 'readwrite')
          const store = tx.objectStore('cache')
          store.clear()
          await new Promise<void>((resolve, reject) => {
            tx.oncomplete = () => resolve()
            tx.onerror = () => reject(tx.error)
            tx.onabort = () => reject(tx.error)
          })
        }
      } catch (error) {
        console.error('清除所有 IndexedDB 缓存数据失败:', error)
      }
    }
  }

  /**
   * 初始化 IndexedDB（静态方法）
   */
  private async initIndexedDB(): Promise<void> {
    if (!window.indexedDB) {
      console.warn('This browser doesn\'t support IndexedDB. Falling back-ignore to memory cache only.')
      CacheManager.isIndexedDBSupported = false
      return
    }

    // 如果已经初始化，则直接返回
    if (CacheManager.db) {
      this.setupIndexedDBCleanup()
      return
    }

    // 确保初始化只进行一次
    if (!CacheManager.dbPromise) {
      CacheManager.dbPromise = new Promise<void>((resolve, reject) => {
        const request = indexedDB.open('CacheManagerDB', 1)

        request.onerror = (event) => {
          console.error('IndexedDB 打开失败:', (event.target as IDBRequest).error)
          CacheManager.isIndexedDBSupported = false
          CacheManager.dbPromise = null // 允许重试
          reject((event.target as IDBRequest).error)
        }

        request.onsuccess = () => {
          CacheManager.db = request.result
          this.setupIndexedDBCleanup()
          resolve()
        }

        request.onupgradeneeded = () => {
          const db = request.result
          if (!db.objectStoreNames.contains('cache')) {
            db.createObjectStore('cache', { keyPath: 'key' })
          }
        }
      })

      try {
        await CacheManager.dbPromise
      } catch (error) {
        throw new Error('IndexedDB 初始化失败')
      }
    } else {
      // 等待已有的初始化 Promise
      await CacheManager.dbPromise
      this.setupIndexedDBCleanup()
    }
  }

  /**
   * 设置 IndexedDB 的自动清理机制（静态）
   */
  private setupIndexedDBCleanup(): void {
    if (!CacheManager.db) return

    if (!CacheManager.cleanupInitialized) {
      setInterval(() => {
        this.cleanupIndexedDB().catch((error) => {
          console.error('定时清理 IndexedDB 过期数据失败:', error)
        })
      }, 24 * 60 * 60 * 1000) // 每24小时清理一次

      CacheManager.cleanupInitialized = true
    }
  }

  /**
   * 清理 IndexedDB 中的过期数据
   */
  private async cleanupIndexedDB(): Promise<void> {
    if (!CacheManager.db) return
    try {
      const tx = CacheManager.db.transaction('cache', 'readwrite')
      const store = tx.objectStore('cache')
      const now = Date.now()

      const request = store.openCursor()
      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest<IDBCursorWithValue>).result
        if (cursor) {
          const record: CacheRecord<T> = cursor.value
          if (record.expireAt < now) {
            cursor.delete()
          }
          cursor.continue()
        }
      }

      request.onerror = (event) => {
        console.error('清理 IndexedDB 过期数据失败:', (event.target as IDBRequest).error)
      }
    } catch (error) {
      console.error('清理 IndexedDB 过期数据异常:', error)
    }
  }

  /**
   * 设置内存缓存的自动清理机制
   */
  private setupMemoryCleanup(): void {
    // 为防止多个实例重复设置清理定时器，采用单例模式
    setInterval(() => {
      const now = Date.now()
      for (const key in this.memoryCache) {
        if (this.memoryCache[key].expireAt < now) {
          this.clear(key).catch((error) => {
            console.error(`清除缓存键 "${key}" 失败:`, error)
          })
        }
      }
    }, 60 * 60 * 1000) // 每小时清理一次
  }

  /**
   * 确保 IndexedDB 已初始化
   */
  private async ensureIndexedDBInitialized(): Promise<void> {
    if (this.isIndexedDBEnabled() && !CacheManager.db) {
      await this.initIndexedDB()
    }
    if (this.isIndexedDBEnabled() && !CacheManager.db) {
      throw new Error('IndexedDB 初始化失败，无法执行操作。')
    }
  }

  /**
   * 从 localStorage 加载内存缓存数据
   */
  private loadFromLocalStorage(): void {
    if (!this.isMemoryEnabled()) return

    const stored = localStorage.getItem(this.options.storageKey!)
    if (stored) {
      try {
        const parsed = JSON.parse(stored) as Record<string, CacheRecord<T>>
        const now = Date.now()
        // 仅加载未过期的条目
        for (const key in parsed) {
          if (parsed[key].expireAt > now) {
            this.memoryCache[key] = parsed[key]
          }
        }
      } catch (error) {
        console.error('从 localStorage 加载缓存数据失败:', error)
        this.memoryCache = {}
      }
    }
  }

  /**
   * 将内存缓存数据保存到 localStorage
   */
  private saveToLocalStorage(): void {
    if (!this.isMemoryEnabled() || !this.options.persist) return

    try {
      localStorage.setItem(this.options.storageKey!, JSON.stringify(this.memoryCache))
    } catch (error) {
      console.error('保存缓存数据到 localStorage 失败:', error)
    }
  }

  /**
   * 检查是否启用内存缓存
   */
  private isMemoryEnabled(): boolean {
    return this.options.strategy === 'memory' || this.options.strategy === 'both'
  }

  /**
   * 检查是否启用 IndexedDB 缓存
   */
  private isIndexedDBEnabled(): boolean {
    return (this.options.strategy === 'indexeddb' || this.options.strategy === 'both') && CacheManager.isIndexedDBSupported
  }
}

// 导出单例实例
const cacheManager = CacheManager.getInstance()

export { CacheManager, cacheManager }

// 使用示例

async function fetchUserConfig(): Promise<{ theme: string; language: string }> {
  // 模拟网络请求
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({ theme: 'dark', language: 'en' })
    }, 1000)
  })
}

async function exampleUsage() {
  const key = 'user_config'

  // 1. 使用默认策略（IndexedDB）
  const cacheManagerDefault = CacheManager.getInstance<{ theme: string; language: string }>({
    strategy: 'indexeddb'
  })

  const configDefault = await cacheManagerDefault.fetch(key, fetchUserConfig, 3600) // 1小时 TTL
  console.log('默认策略（IndexedDB）用户配置:', configDefault)

  // 2. 使用内存缓存
  const cacheManagerMemory = CacheManager.getInstance<{ theme: string; language: string }>({
    strategy: 'memory',
    persist: true, // 启用持久化
    storageKey: 'memory_cache' // 自定义存储键名
  })

  const configMemory = await cacheManagerMemory.fetch(key, fetchUserConfig, 3600)
  console.log('内存缓存用户配置:', configMemory)

  // 3. 同时使用内存缓存和 IndexedDB
  const cacheManagerBoth = CacheManager.getInstance<{ theme: string; language: string }>({
    strategy: 'both',
    persist: true, // 启用持久化
    storageKey: 'both_cache' // 自定义存储键名
  })

  const configBoth = await cacheManagerBoth.fetch(key, fetchUserConfig, 3600)
  console.log('混合策略用户配置:', configBoth)

  // 4. 直接从缓存中获取（不触发 fetchFunction）
  const cachedConfig = await cacheManagerDefault.get(key)
  console.log('缓存中的用户配置（默认策略）:', cachedConfig)

  // 5. 更新缓存数据
  await cacheManagerDefault.set(key, { theme: 'light', language: 'zh' }, 7200) // 2小时 TTL
  const updatedConfig = await cacheManagerDefault.get(key)
  console.log('更新后的用户配置（默认策略）:', updatedConfig)

  // 6. 清除特定缓存条目
  await cacheManagerDefault.clear(key)
  const clearedConfig = await cacheManagerDefault.get(key)
  console.log('清除后的用户配置（默认策略）:', clearedConfig)

  // 7. 清除所有缓存
  await cacheManagerDefault.clearAll()
  const allClearedConfig = await cacheManagerDefault.get(key)
  console.log('所有缓存已清除（默认策略）:', allClearedConfig)
}

// 运行示例
// exampleUsage()