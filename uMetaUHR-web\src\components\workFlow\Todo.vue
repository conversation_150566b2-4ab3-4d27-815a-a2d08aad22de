<template>
  <div class="todo">
    {{ activeEventId }}
    <component
      :is="loadComponent(props.schema?.ui?.type)"
      v-if="props.schema"
      :context="getContext(props)"
      :schema="props.schema?.ui"
    />
    <WnDialog ref="dialog" />
  </div>
</template>

<script lang="ts" setup>
import { defineProps, provide, ref } from 'vue'
import { loadComponent } from '../componentRegistry'
import { getContext } from '@/lib/getContext'
import WnDialog from '@/components/containers/WnDialog.vue'
import { DateTime } from 'luxon'
import type { CalendarEvent } from '@/components/action/views/CalendarType'
import { Debouncer } from '@/lib/Debouncer'
import { dbDelete, dbList, dbSave, refreshNotification } from '@/lib/dataMan'

const props = defineProps<{
  schema: any
  context: { [key: string]: any }
}>()

const dialog = ref<InstanceType<typeof WnDialog>>()
const activeEventId = ref('')

// 获取事件列表
const getCalendarEvents = new Debouncer(300, { leading: false, trailing: true }).debounce(
  async (param: any) => {
    const { startTime, endTime } = param
    console.log(DateTime.fromSeconds(startTime)?.toFormat('yyyy-MM-DD HH:mm:ss'))
    console.log(DateTime.fromSeconds(endTime)?.toFormat('yyyy-MM-DD HH:mm:ss'))

    return (
      (await dbList({
        sql: `SELECT *
        FROM "maxEMR".gtd_tasks
        WHERE "startTime" >= ?::int AND "startTime" < ?::int`,
        param: [+startTime, +endTime]
      })) || []
    )
  }
)

// 保存事件
const saveEvent = async (event: CalendarEvent) => {
  if (!event) return
  event.data = event.data || {}

  const { ids: [{ id = undefined } = {}] = [] } = await dbSave({
    table: 'gtd_tasks',
    data: [event],
    conflict_column: 'id',
    update_columns: [...(event.id ? ['id'] : []), 'title', 'startTime', 'duration', 'data']
  })
  if (id) event.id = id

  await refreshNotification({})
}

// 提供方法
provide('get-calendar-events', getCalendarEvents)
provide('add-calendar-event', async (event: CalendarEvent) => {
  const context = getContext(props) || {}
  context.detail.event = event
  context.events?.push(event)
  await saveEvent(event)
})
provide('save-calendar-event-2', saveEvent)
provide('delete-calendar-event', async () => {
  const {
    detail: { event }
  } = getContext(props)
  if (!confirm(`删除事件 "${event.title}" (ID: ${event.id})？`)) return

  await dbDelete({
    sql: `DELETE
          FROM "maxEMR".gtd_tasks
          WHERE id = ?::bigint`,
    param: [event.id]
  })

  const context = getContext(props)
  let { events = [] } = context
  let i = events.findIndex((e: any) => e.id == event.id)
  if (i >= 0) events.splice(i, 1)
  await window.popAlert({ message: 'Event is deleted' })
})
provide('save-calendar-event', async () => {
  const {
    detail: { event }
  } = getContext(props)
  await saveEvent(event)
  await window.popAlert({ message: 'Event is saved' })
})
// 编辑事件
const editCalendarEvent = async (event: CalendarEvent) => {
  const context = getContext(props)
  let { events = [] } = context
  const index = events.findIndex((e: CalendarEvent) => e.id === event.id)
  if (index < 0) return
  events.splice(index, 1, event)
  context.activeEventId = '' + event.id
  context.detail.event = event
}
provide('edit-calendar-event', editCalendarEvent)

defineExpose({ compName: props?.schema?.type || '-' })
</script>

<style lang="scss" scoped>
.todo {
  display: flex;
  flex: 1 1 auto;
  min-height: 300px;
}
</style>
