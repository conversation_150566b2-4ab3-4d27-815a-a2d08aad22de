type ThrottledFunction<T extends any[]> = (...args: T) => Promise<any>

export function throttleAsync<T extends any[]>(func: (...args: T) => Promise<any>, wait: number) {
  let lastTime = 0
  let timeout = null as any
  const queue: { args: T; resolve: (x?: any) => any; reject: (reason?: any) => any }[] = []

  // Executes the next task in the queue
  const executeNext = async (): Promise<any> => {
    if (queue.length === 0) return

    const { args, resolve, reject } = queue.shift()! // Extract the first task from the queue

    try {
      await func(...args) // Execute the function with provided arguments
      resolve() // Resolve the promise after function execution
    } catch (err) {
      reject(err) // Reject if there is an error
    }

    // Update the last time after execution
    lastTime = Date.now()

    // If there are more tasks in the queue, schedule the next one
    if (queue.length > 0) {
      timeout = setTimeout(executeNext, wait) // Delay next execution by 'wait' period
    } else {
      timeout = null // Clear timeout if queue is empty
    }
  }

  return async function (...args: T): Promise<any> {
    const now = Date.now()
    const remainingTime = wait - (now - lastTime)

    // Return a new Promise that resolves once the function has been executed
    return new Promise<any>((resolve, reject) => {
      if (remainingTime <= 0) {
        // If enough time has passed since last execution, execute immediately
        if (timeout) {
          clearTimeout(timeout) // Clear pending timeout if it exists
          timeout = null
        }
        lastTime = now
        func(...args)
          .then(resolve)
          .catch(reject) // Execute function immediately and return result
      } else {
        // If still in throttle period, add task to queue
        queue.push({ args, resolve, reject })

        if (!timeout) {
          // If no timeout is scheduled, we schedule the next execution after 'remainingTime'
          timeout = setTimeout(executeNext, remainingTime)
        }
      }
    })
  }
}
