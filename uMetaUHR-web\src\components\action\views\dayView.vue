<template>
  <div class="day-view">
    <!-- Controls remain the same -->
    <div class="controls">
      <button @click="nonWorkHoursFolded = !nonWorkHoursFolded">
        {{ nonWorkHoursFolded ? 'Show All Hours' : 'Fold Non-Work Hours' }}
      </button>
    </div>

    <!-- Single day header -->
    <div class="day-header">
      {{ currentDate.toFormat('EEE dd, yyyy') }}
    </div>

    <div class="day-body">
      <div class="day-grid">
        <!-- Time grid with dynamic height -->
        <div :style="{ height: dayHeight }" class="time-grid">
          <div v-for="hour in hours" :key="hour" class="time-slot">
            <div class="time-label">{{ hour }}:00</div>
            <div class="time-line"></div>
          </div>
        </div>

        <!-- Single day column -->
        <div
          :style="{ height: dayHeight }"
          class="day"
          @dblclick="handleDoubleClick(currentDate, $event)"
          @drop="handleDrop($event, currentDate)"
          @dragover.prevent="handleDragOver(currentDate, $event)"
        >
          <!-- Preview Event -->
          <div
            v-if="previewEvent?.day?.hasSame(currentDate, 'day')"
            :style="previewEvent?.style"
            class="event preview-event"
          >
            <div class="event-title">{{ previewEvent?.title }}</div>
            <div class="event-time">{{ previewEvent.time }}</div>
          </div>

          <!-- Calendar Events -->
          <div
            v-for="event in dayEvents(currentDate) || []"
            :key="event.id"
            :class="['event', { active: event.id === props?.activeEventId }]"
            :style="eventStyle(event, currentDate)"
            draggable="true"
            @dragstart="handleDragStart($event, event)"
            @click.stop="handleEdit(event)"
          >
            <div class="event-title">{{ event.title }}</div>
            <div class="event-time">{{ formatTime(event) }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, defineProps, ref } from 'vue'
import { DateTime } from 'luxon'
import type { CalendarEvent } from '@/components/action/views/CalendarType'
import ServerTime from '@/lib/ServerTime'
import { getContext } from '@/lib/getContext'
import { isEmpty } from '@/lib/util'

const props = defineProps<{
  schema?: any
  context?: { [key: string]: any }
  currentDate: DateTime
  activeEventId?: string
}>()

const emit = defineEmits(['event-drop', 'edit-event', 'add-event', 'reload-events'])

const MINUTES_PER_DAY = 24 * 60
const nonWorkHoursFolded = ref(true)

// Work hours from schema
const workStart = computed(() => props.schema?.workHours?.start ?? 9)
const workEnd = computed(() => props.schema?.workHours?.end ?? 17)

// Visible hours calculation
const hours = computed(() => {
  if (nonWorkHoursFolded.value) {
    return Array.from(
      { length: workEnd.value - workStart.value + 1 },
      (_, i) => workStart.value + i
    )
  }
  return Array.from({ length: 24 }, (_, i) => i)
})

// Day grid height
const dayHeight = computed(() => {
  const hoursCount = nonWorkHoursFolded.value ? workEnd.value - workStart.value + 1 : 24
  return `${hoursCount * 60}px`
})

// Events for current day
const dayEvents = (day: DateTime) => {
  let { events = [] } = getContext(props)
  if (isEmpty(events)) return []
  return events.filter((event: any) => DateTime.fromSeconds(event.startTime).hasSame(day, 'day'))
}

// Event time formatting
const formatTime = (event: CalendarEvent) => {
  const start = ServerTime.fromSeconds(event.startTime).toFormat('HH:mm')
  const end = ServerTime.fromSeconds(event.startTime + event.duration).toFormat('HH:mm')
  return `${start} - ${end}`
}

// Event positioning logic (same as WeekView)
const eventStyle = (event: CalendarEvent, day: DateTime) => {
  const start = DateTime.fromSeconds(event.startTime, { zone: day.zoneName })
  const startMinutes = start.hour * 60 + start.minute
  const endMinutes = startMinutes + event.duration / 60

  if (nonWorkHoursFolded.value) {
    const workStartMinutes = workStart.value * 60
    const workEndMinutes = workEnd.value * 60 + 60

    if (endMinutes <= workStartMinutes || startMinutes >= workEndMinutes) {
      return { display: 'none' }
    }

    const visibleStart = Math.max(startMinutes, workStartMinutes)
    const visibleEnd = Math.min(endMinutes, workEndMinutes)
    const visibleTop = visibleStart - workStartMinutes
    const visibleHeight = visibleEnd - visibleStart

    return visibleHeight > 0
      ? { top: `${visibleTop}px`, height: `${visibleHeight}px`, display: 'block' }
      : { display: 'none' }
  }

  return {
    top: `${startMinutes}px`,
    height: `${event.duration / 60}px`,
    display: 'block'
  }
}

// Drag and drop logic (same as WeekView)
const draggedEvent = ref<CalendarEvent | null>(null)
const previewEvent = ref<{
  day: DateTime
  style: { top: string; height: string }
  title: string
  time: string
} | null>(null)

const calculateEventPosition = (e: DragEvent | MouseEvent, day: DateTime) => {
  const dayElement = e.currentTarget as HTMLElement
  const rect = dayElement.getBoundingClientRect()
  const offsetY = Math.max(0, Math.min(e.clientY - rect.top, rect.height))

  const totalVisibleMinutes = nonWorkHoursFolded.value
    ? (workEnd.value - workStart.value + 1) * 60
    : MINUTES_PER_DAY

  const pixelsPerMinute = dayElement.offsetHeight / totalVisibleMinutes
  let snappedMinutes = Math.round(offsetY / pixelsPerMinute / 15) * 15

  if (nonWorkHoursFolded.value) {
    snappedMinutes += workStart.value * 60
  }

  return { snappedMinutes, dayElement }
}

const img = new Image()
img.src = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'

const handleDragStart = (e: DragEvent, event: CalendarEvent) => {
  if (!e.dataTransfer) return
  e.dataTransfer.setDragImage(img, 0, 0)
  e.dataTransfer.setData('text/plain', JSON.stringify(event))
  draggedEvent.value = event
  e.target?.addEventListener('dragend', () => (previewEvent.value = null), { once: true })
}

const handleDragOver = (day: DateTime, e: DragEvent) => {
  e.preventDefault()
  if (!draggedEvent.value) return

  let { snappedMinutes } = calculateEventPosition(e, day)
  if (nonWorkHoursFolded.value) {
    snappedMinutes -= workStart.value * 60
  }

  const previewStart = day.startOf('day').plus({ minutes: snappedMinutes })
  const durationMinutes = draggedEvent.value.duration / 60

  previewEvent.value = {
    day,
    style: { top: `${snappedMinutes}px`, height: `${durationMinutes}px` },
    title: draggedEvent.value.title,
    time: `${previewStart.toFormat('HH:mm')} - ${previewStart.plus({ minutes: durationMinutes }).toFormat('HH:mm')}`
  }
}

const handleDrop = (e: DragEvent, day: DateTime) => {
  const { snappedMinutes } = calculateEventPosition(e, day)
  const newStart = day.startOf('day').plus({ minutes: snappedMinutes })
  const eventData = JSON.parse(e.dataTransfer?.getData('text/plain') || '{}')
  emit('event-drop', eventData, newStart)
  previewEvent.value = null
}

const handleEdit = (event: CalendarEvent) => emit('edit-event', event)

const handleDoubleClick = (day: DateTime, e: MouseEvent) => {
  const { snappedMinutes } = calculateEventPosition(e, day)
  const eventStart = day.startOf('day').plus({ minutes: snappedMinutes })
  emit('add-event', eventStart)
  emit('reload-events')
}
</script>

<style lang="scss" scoped>
.day-view {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: auto;
}

.controls {
  padding: 0.5rem;
  background: #eee;

  button {
    padding: 0.25rem 0.5rem;
    background: #fff;
    border: 1px solid #ccc;
    border-radius: 4px;
    cursor: pointer;

    &:hover {
      background: #f0f0f0;
    }
  }
}

.day-header {
  padding: 0.5rem;
  background: #eee;
  font-weight: bold;
  text-align: center;
}

.day-body {
  flex: 1;
  overflow: auto;
  padding: 1rem 0.5rem;
}

.day-grid {
  display: flex;
  position: relative;
}

.time-grid {
  border-right: 1px solid #ddd;
}

.time-slot {
  height: 60px;
  position: relative;
}

.time-label {
  position: absolute;
  top: -10px;
  left: 5px;
  font-size: 0.8rem;
  color: #666;
}

.time-line {
  border-top: 1px solid #eee;
}

.day {
  flex: 1;
  border-right: 1px solid #ddd;
  position: relative;
}

.event {
  position: absolute;
  left: 5px;
  right: 5px;
  background: #e3f2fd;
  border-left: 3px solid #2196f3;
  padding: 5px;
  border-radius: 3px;
  cursor: pointer;

  &-title {
    font-weight: bold;
    font-size: 0.9rem;
  }

  &-time {
    font-size: 0.8rem;
    color: #666;
  }

  &.preview-event {
    opacity: 0.7;
    background: #b3e5fc;
    border-left: 3px solid #03a9f4;
  }

  &.active {
    background: #bbdefb !important;
    border-left: 3px solid brown;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 1);
  }
}
</style>
