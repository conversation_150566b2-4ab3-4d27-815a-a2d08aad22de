export function htmlToText(html: any): string {
  if (!html) return ''
  if (typeof html !== 'string') return JSON.stringify(html)
  if (!html.trim().startsWith('<')) return html

  const tempElement = document.createElement('div')
  tempElement.innerHTML = html

  const getTextWithNewLines = (element: HTMLElement): string => {
    return Array.from(element.childNodes)
      .map((node) => {
        if (node.nodeType === Node.TEXT_NODE) return node.nodeValue || ''
        if (node.nodeType === Node.ELEMENT_NODE) {
          const el = node as HTMLElement
          switch (el.tagName) {
            case 'BR':
              return '\n'
            case 'P':
            case 'DIV':
              return '\n' + getTextWithNewLines(el)
            case 'LI':
              return '\n- ' + getTextWithNewLines(el)
            case 'H1':
            case 'H2':
            case 'H3':
            case 'H4':
            case 'H5':
            case 'H6':
              return '\n' + getTextWithNewLines(el).toUpperCase() + '\n'
            default:
              return getTextWithNewLines(el)
          }
        }
        return ''
      })
      .join('')
  }

  try {
    let textContent = getTextWithNewLines(tempElement)
    textContent = textContent.replace(/\n\s*\n/g, '\n\n')
    return textContent.trim()
  } catch (error) {
    console.error('Failed to convert HTML to text:', error)
    return ''
  } finally {
    tempElement.remove()
  }
}

export function extractJsonFromText(text: any): any | null {
  if (typeof text !== 'string') {
    console.warn('Invalid text provided for extracting JSON：' + JSON.stringify(text))
    return null
  }

  const jsonRegex =
    /```json([^`]*)```|```([^`]*)```|```json([^`]*)|```([^`]*)|```json([^`]*$)|```([^`]*$)/g
  let match = jsonRegex.exec(text)

  if (!match) {
    // If no match is found, try to parse the text directly as JSON
    try {
      text = text.trim()
      if (
        !(
          (text.startsWith('{') && text.endsWith('}')) ||
          (text.startsWith('[') && text.endsWith(']'))
        )
      )
        return null
      return JSON.parse(text)
    } catch (error) {
      console.error('Failed to parse JSON:', error)
      console.error(text)
      return null
    }
  }

  while (match !== null) {
    for (let i = 1; i < match.length; i++) {
      if (match[i]) {
        try {
          return JSON.parse(match[i].trim())
        } catch (e) {
          console.error('Invalid JSON format found:', match[i])
          console.error(match[i])
        }
      }
    }
    match = jsonRegex.exec(text)
  }
  return null
}

// 定义全角字符到半角字符的映射
const CODE_MAP: Record<string, string> = {
  '\u00A0': ' ', // 不间断空格替换为普通空格
  '\u3000': ' ', // 全角空格替换为普通空格
  '“': '"', // 左双引号替换为标准双引号
  '”': '"', // 右双引号替换为标准双引号
  '¥': '$' // 日元符号替换为美元符号
  // '、': '、'      // 冗余映射，已移除
}

// 可选：引入缓存机制，提升重复字符串处理的效率
const cache: Map<string, string> = new Map()

/**
 * 将字符串中的全角标点转换为半角标点。
 * 使用正则表达式进行批量替换，简洁且高效。
 *
 * @param str 输入字符串
 * @returns 转换后的字符串
 */
export function toHalfWidthPunctuation(str: string): string {
  if (!str) return ''

  // 检查缓存，若存在则直接返回缓存结果
  if (cache.has(str)) {
    return cache.get(str)!
  }

  // 第一步：替换特定映射的字符
  const step1 = str.replace(/[\u00A0\u3000“”¥]/g, (char) => CODE_MAP[char] || char)

  // 第二步：替换全角标点字符（范围：！到～，U+FF01到U+FF5E）
  const step2 = step1.replace(/[\uFF01-\uFF5E]/g, (char) => {
    const code = char.charCodeAt(0)
    return String.fromCharCode(code - 0xfee0)
  })

  // 将结果存入缓存
  cache.set(str, step2)
  return step2
}
