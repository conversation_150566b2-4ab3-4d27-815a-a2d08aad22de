import { DateTime } from 'luxon'

export function age(timestamp: number, referenceTime: string = 'now') {
  try {
    if (!timestamp) return 'N/A'

    // 解析输入时间戳
    const fromDate = DateTime.fromSeconds(timestamp)
    const toDate = DateTime.now()

    // 处理参考时间（如果需要扩展）
    if (referenceTime !== 'now') {
      // throw new Error('Only "now" is supported for referenceTime')
    }

    // 检查时间有效性
    if (!fromDate.isValid) return 'Invalid date'
    if (fromDate > toDate) return '0天' // 未来时间处理

    // 计算精确的时间差
    const diff = toDate.diff(fromDate, ['years', 'months', 'days'])
    const years = diff.years
    const months = diff.months
    const days = Math.floor(diff.days) // 取整天数

    // 按优先级返回最大单位
    if (years >= 1) {
      return `${Math.floor(years)}岁`
    } else if (months >= 1) {
      return `${Math.floor(months)}月`
    } else {
      return `${days}天`
    }
  } catch (e) {
    return `<span class="error">Age calculation error: ${e instanceof Error ? e.message : String(e)}</span>`
  }
}
