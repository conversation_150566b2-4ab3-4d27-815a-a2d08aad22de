/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ACheckbox: typeof import('ant-design-vue/es')['Checkbox']
    ACheckboxGroup: typeof import('ant-design-vue/es')['CheckboxGroup']
    ACollapse: typeof import('ant-design-vue/es')['Collapse']
    ACollapsePanel: typeof import('ant-design-vue/es')['CollapsePanel']
    AConfigProvider: typeof import('ant-design-vue/es')['ConfigProvider']
    ADescriptions: typeof import('ant-design-vue/es')['Descriptions']
    ADescriptionsItem: typeof import('ant-design-vue/es')['DescriptionsItem']
    AEmpty: typeof import('ant-design-vue/es')['Empty']
    AInput: typeof import('ant-design-vue/es')['Input']
    AmbulatoryQC: typeof import('./src/components/workFlow/AmbulatoryQC.vue')['default']
    ARadio: typeof import('ant-design-vue/es')['Radio']
    ARadioButton: typeof import('ant-design-vue/es')['RadioButton']
    ARadioGroup: typeof import('ant-design-vue/es')['RadioGroup']
    ARangePicker: typeof import('ant-design-vue/es')['RangePicker']
    ASpin: typeof import('ant-design-vue/es')['Spin']
    ATable: typeof import('ant-design-vue/es')['Table']
    ATooltip: typeof import('ant-design-vue/es')['Tooltip']
    BlocklyEditor: typeof import('./src/components/action/BlocklyEditor.vue')['default']
    Button: typeof import('./src/components/action/Button.vue')['default']
    Calendar: typeof import('./src/components/action/Calendar.vue')['default']
    ChatBox: typeof import('./src/components/workFlow/ChatBox.vue')['default']
    CheckboxGroup: typeof import('./src/components/action/CheckboxGroup.vue')['default']
    Choice: typeof import('./src/components/action/Choice.vue')['default']
    ClinicalNotes: typeof import('./src/components/workFlow/ClinicalNotes.vue')['default']
    CodeMirror: typeof import('./src/components/action/TextProcessors/CodeMirror.vue')['default']
    Collapse: typeof import('./src/components/action/Collapse.vue')['default']
    Config: typeof import('./src/components/workItem/Config.vue')['default']
    Conversation: typeof import('./src/components/action/TextProcessors/Conversation.vue')['default']
    DataList: typeof import('./src/components/dataComp/DataList.vue')['default']
    DataViewer: typeof import('./src/components/action/TextProcessors/DataViewer.vue')['default']
    DatePick: typeof import('./src/components/action/DatePick.vue')['default']
    DateTime: typeof import('./src/components/action/DateTime.vue')['default']
    DayView: typeof import('./src/components/action/views/dayView.vue')['default']
    DbFile: typeof import('./src/components/containers/DB/DbFile.vue')['default']
    Descriptions: typeof import('./src/components/action/TextProcessors/Descriptions.vue')['default']
    DiagnosisEntry: typeof import('./src/components/workFlow/DiagnosisEntry.vue')['default']
    EmrEditor: typeof import('./src/components/action/TextProcessors/EmrEditor.vue')['default']
    External: typeof import('./src/components/containers/External.vue')['default']
    HelpTooltip: typeof import('./src/components/containers/HelpTooltip.vue')['default']
    IconCommunity: typeof import('./src/components/icons/IconCommunity.vue')['default']
    IconDocumentation: typeof import('./src/components/icons/IconDocumentation.vue')['default']
    IconEcosystem: typeof import('./src/components/icons/IconEcosystem.vue')['default']
    IconSupport: typeof import('./src/components/icons/IconSupport.vue')['default']
    IconTooling: typeof import('./src/components/icons/IconTooling.vue')['default']
    InputComp: typeof import('./src/components/action/InputComp.vue')['default']
    InputText: typeof import('./src/components/action/InputText.vue')['default']
    LabChart: typeof import('./src/components/workItem/LabChart.vue')['default']
    Layout: typeof import('./src/components/containers/Layout.vue')['default']
    Layout2: typeof import('./src/components/containers/Layout2.vue')['default']
    Line: typeof import('./src/components/action/charts/Line.vue')['default']
    LoopRender: typeof import('./src/components/action/LoopRender.vue')['default']
    MarkdownViewer: typeof import('./src/components/action/TextProcessors/MarkdownViewer.vue')['default']
    MasterData: typeof import('./src/components/workFlow/MasterData.vue')['default']
    MDAgents: typeof import('./src/components/workFlow/MDAgents.vue')['default']
    MonthView: typeof import('./src/components/action/views/monthView.vue')['default']
    NoteWriter: typeof import('./src/components/workFlow/NoteWriter.vue')['default']
    OrderEntry: typeof import('./src/components/workFlow/OrderEntry.vue')['default']
    Panel: typeof import('./src/components/containers/Panel.vue')['default']
    Pat: typeof import('./src/components/workFlow/Pat.vue')['default']
    PatAllergy: typeof import('./src/components/workFlow/PatAllergy.vue')['default']
    PatAppointment: typeof import('./src/components/workFlow/PatAppointment.vue')['default']
    PatCondition: typeof import('./src/components/workFlow/PatCondition.vue')['default']
    Patent: typeof import('./src/components/workFlow/Patent.vue')['default']
    PatientList: typeof import('./src/components/workFlow/PatientList.vue')['default']
    PatientWorkspace: typeof import('./src/components/workFlow/PatientWorkspace.vue')['default']
    PatIntake: typeof import('./src/components/workFlow/PatIntake.vue')['default']
    PatList: typeof import('./src/components/workFlow/PatList.vue')['default']
    PatOperation: typeof import('./src/components/workFlow/PatOperation.vue')['default']
    PlainTextEditor: typeof import('./src/components/action/TextProcessors/PlainTextEditor.vue')['default']
    RadioGroup: typeof import('./src/components/action/RadioGroup.vue')['default']
    RecordEntry: typeof import('./src/components/action/RecordEntry.vue')['default']
    RecordSelector: typeof import('./src/components/action/RecordSelector.vue')['default']
    RichTextEditor: typeof import('./src/components/action/TextProcessors/RichTextEditor.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Section: typeof import('./src/components/containers/Section.vue')['default']
    SensitiveDisplay: typeof import('./src/components/action/TextProcessors/SensitiveDisplay.vue')['default']
    SimpleLogin: typeof import('./src/components/workFlow/SimpleLogin.vue')['default']
    SmartDataEntry: typeof import('./src/components/workFlow/SmartDataEntry.vue')['default']
    Sop: typeof import('./src/components/workFlow/Sop.vue')['default']
    Stack: typeof import('./src/components/containers/Stack.vue')['default']
    Tag: typeof import('./src/components/action/Tag.vue')['default']
    Textarea: typeof import('./src/components/action/Textarea.vue')['default']
    TextDisplay: typeof import('./src/components/action/TextProcessors/TextDisplay.vue')['default']
    Toc: typeof import('./src/components/containers/Toc.vue')['default']
    TocDetail: typeof import('./src/components/containers/TocDetail.vue')['default']
    Todo: typeof import('./src/components/workFlow/Todo.vue')['default']
    Toolbar: typeof import('./src/components/containers/Toolbar.vue')['default']
    Tree: typeof import('./src/components/containers/Tree.vue')['default']
    TreeNode: typeof import('./src/components/containers/TreeNode.vue')['default']
    TypesenseMan: typeof import('./src/components/workItem/TypesenseMan.vue')['default']
    VerticalDivider: typeof import('./src/components/action/VerticalDivider.vue')['default']
    WeekView: typeof import('./src/components/action/views/weekView.vue')['default']
    Window: typeof import('./src/components/containers/Window.vue')['default']
    WnAlert: typeof import('./src/components/containers/WnAlert.vue')['default']
    WnDialog: typeof import('./src/components/containers/WnDialog.vue')['default']
    Workspace: typeof import('./src/components/workFlow/Workspace.vue')['default']
    WritingTask: typeof import('./src/components/workFlow/WritingTask.vue')['default']
  }
}
