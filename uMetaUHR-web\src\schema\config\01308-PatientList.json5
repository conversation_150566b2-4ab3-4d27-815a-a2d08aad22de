{
  type: 'patList',
  ui: {
    type: 'window',
    themes: ['body-flex'],
    style: {
      main: {
        background: '#E7EDF7'
      }
    },
    head: [
      {
        type: 'toolbar',
        style: {
          height: '64px',
          display: 'flex',
          'align-items': 'center'
        },
        children: [
          {
            type: 'button',
            customIcon: '/serving-number.svg',
            label: '开始叫号',
            style: {
              main: {
                'flex-direction': 'column',
                background: 'none',
                border: 'unset',
                gap: '0',
                padding: '0'
              }
            }
          },
          {
            type: 'button',
            customIcon: '/pause-receiving.svg',
            label: '暂停接诊',
            style: {
              main: {
                'flex-direction': 'column',
                background: 'none',
                border: 'unset',
                gap: '0',
                padding: '0'
              }
            }
          },
          {
            type: 'button',
            customIcon: '/refresh.svg',
            label: '刷新',
            style: {
              main: {
                'flex-direction': 'column',
                background: 'none',
                border: 'unset',
                gap: '0',
                padding: '0'
              }
            }
          }
        ]
      }
    ],
    body: [
      {
        type: 'tocDetail',
        myFavoriteId: '01308-PatientList-001',
        style: {
          vars: {
            workspaceBackground: '#EDF7FD',
            toolbarBackground: '#DAF0FF',
            toolbarBorder: '#A9D5EA'
          }
        },
        tocDefaultWidth: 800,
        toc: {
          type: 'toc',
          toolbar: [
            {
              type: 'patientCondition'
            }
          ],
          columnDef: [
            {
              field: 'id',
              displayName: '号序'
            },
            {
              field: 'name',
              displayName: '就诊状态'
            }
          ],
          event: {
            listItems: 'list-patients',
            selectItem: 'select-patient',
            openItem: 'open-patient-record'
          },
          selectedLine: 0,
          myFavoriteId: '01305-PatList-67',
          table: {
            type: 'patientList',
            columnDef: [
              {
                field: 'queue_number',
                displayName: '序号'
              },
              {
                field: 'visit_status',
                displayName: '就诊状态'
              },
              {
                field: 'patient_name',
                displayName: '姓名'
              },
              {
                field: 'birth_date',
                displayName: '年龄'
              },
              {
                field: 'gender',
                displayName: '性别'
              },
              {
                field: 'diagnosis',
                displayName: '诊断'
              },
              {
                field: 'medical_card_no',
                displayName: '卡号'
              },
              {
                field: 'operation',
                displayName: '操作',
                fixed: 'right',
                width: '120'
              }
            ]
          }
        },
        detail: {
          type: 'window',
          themes: ['body-flex'],
          body: [
            {
              type: 'stack',
              menu: [
                {
                  id: 'snapshot',
                  displayName: '健康信息快照'
                },
                {
                  id: 'm2',
                  displayName: '预问诊报告'
                },
                {
                  id: 'm3',
                  displayName: '上次就诊病历'
                }
              ],
              myFavoriteId: '01305-PatList-107',
              tabPosition: 'top',
              children: {
                snapshot: {
                  type: 'window',
                  themes: ['body-flex'],
                  body: [
                    {
                      type: 'layout2',
                      myFavoriteId: '01305-PatList-127',
                      style: {
                        child2: {
                          border: 'none',
                          width: '30rem'
                        }
                      },
                      children: [
                        {
                          type: 'panel',
                          style: {
                            main: {
                              display: 'block',
                              overflow: 'auto',
                              border: 'none'
                            }
                          },
                          children: [
                            {
                              type: 'section',
                              title: '基本信息',
                              collapsible: true,
                              myFavoriteId: '01305-PatList-151',
                              children: [
                                {
                                  type: 'textDisplay',
                                  templateFile: {
                                    type: 'SOP-prompts',
                                    name: '患者信息模板'
                                  }
                                }
                              ]
                            },
                            {
                              type: 'section',
                              title: '基本信息',
                              collapsible: true,
                              myFavoriteId: '01305-PatList-202',
                              children: [
                                {
                                  type: 'textDisplay',
                                  template: 'thi is a test'
                                }
                              ]
                            },
                            {
                              type: 'section',
                              title: '基本信息',
                              collapsible: true,
                              myFavoriteId: '01305-PatList-214',
                              children: [
                                {
                                  type: 'textDisplay',
                                  template: 'thi is a test'
                                }
                              ]
                            },
                            {
                              type: 'section',
                              title: '基本信息',
                              collapsible: true,
                              myFavoriteId: '01305-PatList-226',
                              children: [
                                {
                                  type: 'textDisplay',
                                  template: 'thi is a test'
                                }
                              ]
                            }
                          ]
                        },
                        {
                          type: 'section',
                          title: '编辑',
                          collapsible: true,
                          myFavoriteId: '01305-PatList-240',
                          children: [
                            {
                              type: 'dataViewer',
                              model: ''
                            }
                          ]
                        }
                      ]
                    }
                  ]
                },
                data: {
                  type: 'window',
                  body: [
                    {
                      type: 'section',
                      title: '工作内容',
                      collapsible: true,
                      myFavoriteId: '01305-PatList-259',
                      children: [
                        {
                          type: 'dataViewer',
                          model: ''
                        }
                      ]
                    }
                  ]
                }
              }
            }
          ],
          subContext: {
            path: 'detail',
            default: {}
          }
        }
      }
    ]
  }
}
