{
  type: 'patList',
  ui: {
    type: 'window',
    head: [
      {
        type: 'toolbar',
        children: [
          {
            type: 'button',
            customIcon: '/start-call.svg',
            event: {
              name: 'next-patient'
            },
            label: '开始叫号',
          },
          {
            type: 'button',
            customIcon: '/next-patient.svg',
            event: {
              name: 'next-patient'
            },
            label: '下一位',
          },
          {
            type: 'button',
            customIcon: '/pause-call.svg',
            event: {
              name: 'next-patient'
            },
            label: '暂停接诊',
          },
          {
            type: 'button',
            customIcon: '/refresh.svg',
            event: {
              name: 'refresh-patient-list'
            },
            label: '刷新',
          },
        ]
      }
    ],
    body: [
      {
        type: 'tocDetail',
        myFavoriteId: '01305-PatList-032',
        tocDefaultWidth: 800,
        style: {
          vars: {
            workspaceBackground: '#EDF7FD',
            toolbarBackground: '#DAF0FF',
            toolbarBorder: '#A9D5EA'
          }
        },
        toc: {
          type: 'stack',
          menu: [
            {
              id: 'patList',
              displayName: '患者列表'
            }
          ],
          myFavoriteId: '01305-PatList-045',
          children: {
            patList: {
              type: 'window',
              themes: [
                'body-flex'
              ],
              head: [
                {
                  type: 'toolbar',
                  style: {
                    alignItems: 'center',
                    gap: "16px"
                  },
                  children: [
                    {
                      type: "InputText",
                      model: "conditions.keyword",
                      event: {
                        enterEvent: 'change-keyword'
                      },
                    },
                    {
                      type: "DatePick",
                      model: "conditions.dateRange",
                      event: {
                        change: 'change-date'
                      },
                      rangePresets: [
                        {
                          label: "今天",
                          diff: 0,
                          unit: 'd'
                        },
                        {
                          label: "过去7天",
                          diff: -7,
                          unit: 'd'
                        },
                        {
                          label: "过去14天",
                          diff: -14,
                          unit: 'd'
                        },
                        {
                          label: "过去30天",
                          diff: -30,
                          unit: 'd'
                        },
                      ],
                      attrs: {}
                    },
                    {
                      type: "CheckboxGroup",
                      model: "conditions.visitPeriod",
                      isGroup: true,
                      options: [
                        {
                          "label": "上午",
                          "value": "AM"
                        },
                        {
                          "label": "下午",
                          "value": "PM"
                        },
                      ],
                      event: {
                        change: 'change-period'
                      },
                      style: {
                        marginLeft: "10px"
                      }
                    },
                    {
                      type: 'button',
                      event: {
                        name: 'reset-conditions'
                      },
                      label: '重置'
                    },
                  ]
                },
                {
                  type: 'toolbar',
                  children: [
                    {
                      type: "RadioGroup",
                      model: "conditions.visitStatus",
                      event: {
                        change: 'change-patient-list'
                      },
                      radioType: "button",
                      options: [
                        {
                          "label": "全部",
                          "value": "all",
                          "queryTemplate": 'xEncListAll'
                        },
                        {
                          "label": "待就诊",
                          "value": "waiting",
                          "queryTemplate": 'xEncListWaiting'
                        },
                        {
                          "label": "已过号",
                          "value": "missed",
                          "queryTemplate": 'xEncListMissed'
                        },
                        {
                          "label": "就诊中",
                          "value": "inProgress",
                          "queryTemplate": 'xEncListInProgress'
                        },
                        {
                          "label": "已就诊",
                          "value": "completed",
                          "queryTemplate": 'xEncListCompleted'
                        }
                      ],
                      attrs: {
                        buttonStyle: "solid"
                      }
                    },
                  ]
                }
              ],
              body: [
//                {
//                  type: 'dataViewer',
//                  model: ''
//                },
                {
                  type: 'tocComp',
                  head: [

                  ],
                  columnDef: [
                    {
                      field: 'id',
                      displayName: 'ID',
                      style: {
                        width: "50px"
                      }
                    },
                    {
                      field: 'name',
                      displayName: '姓名',
                      style: {
                        width: "100px"
                      }
                    },
                    {
                      field: 'gender',
                      displayName: '性别',
                      style: {
                        width: "50px"
                      }
                    },
                    {
                      displayName: '号序',
                      field: 'queue_number',
                      style: {
                        width: "50px"
                      }
                    },
                    {
                      displayName: '就诊状态',
                      field: 'status',
                      style: {
                        width: 100
                      }
                    },
                    {
                      displayName: '年龄',
                      field: 'age',
                      style: {
                        width: "50px"
                      }
                    },
                    {
                      displayName: '出生日期',
                      field: 'birth_date',
                      format: 'tms',
                      style: {
                        width: "50px"
                      }
                    },
                    {
                      displayName: '操作',
                      isCmd: 1,
                      comp: {
                        type: 'toolbar',
                        children: [
                          {
                            type: 'button',
                            themes: [
                              'icon-only',
                              'xs'
                            ],
                            customIcon: '/finish.svg',
                            event: {
                              name: 'open-patient-record'
                            }
                          },
                          {
                            type: 'button',
                            themes: [
                              'icon-only',
                              'xs'
                            ],
                            customIcon: '/call.svg',
                            event: {
                              name: 'open-patient-record'
                            }
                          }
                        ]
                      }
                    },
                  ],
                  event: {
                    tocCountItem: 'count-patients',
                    listItems: 'list-patients',
                    selectItem: 'select-patient',
                    openItem: 'open-patient-record',
                    messageEndPoint: 'toc-patient-list',
                    scrollToBottom: 'load-more-patient-list',
                    setPageSize: 'set-page-size'
                  },
                  selectedLine: 0,
                  model: 'patList',
                  myFavoriteId: '01305-PatList-67'
                }
              ],
              foot: [
                {
                  type: 'toolbar',
                  style: {
                    justifyContent: 'space-between'
                  },
                  children: [
                    //                    {
                    //                      type: 'dataViewer'
                    //                    },
                    {
                      type: 'textDisplay',
                      template: "共 <span style='color: #202020;font-weight: 500;'>{{totalPatients}}</span> 条"
                    },
                    {
                      type: 'textDisplay',
                      template: "<span style='color: #939393;'>最近刷新时间：{{lastRefreshTime}}</span>"
                    },
                  ]
                }
              ]
            }
          },
        },
        detail: {
          type: 'window',
          style: {
            body: {
              display: 'flex',
              flex: '1 1 auto'
            }
          },
          body: [
            {
              type: 'stack',
              style: {
                vars: {
                  themeBorderColor: '#365FD9',
                  themeActiveColor: '#365FD9'
                }
              },
              menu: [
                {
                  id: 'snapshot',
                  displayName: '健康信息快照'
                },
                {
                  id: 'm2',
                  displayName: '预问诊报告'
                },
                {
                  id: 'm3',
                  displayName: '上次就诊病历'
                },
                {
                  id: 'data',
                  displayName: '数据'
                }
              ],
              myFavoriteId: '01305-PatList-107',
              tabPosition: 'top',
              children: {
                snapshot: {
                  type: 'window',
                  style: {
                    main: {
                      display: 'flex',
                      flex: '1 1 auto'
                    },
                    body: {
                      display: 'flex',
                      flex: '1 1 auto'
                    }
                  },
                  body: [
                    {
                      type: 'layout2',
                      myFavoriteId: '01305-PatList-127',
                      style: {
                        child2: {
                          border: 'none',
                          width: '30rem'
                        },
                        main: {
                          flex: '1 1 auto',
                          display: 'flex'
                        }
                      },
                      children: [
                        {
                          type: 'panel',
                          style: {
                            main: {
                              display: 'block',
                              overflow: 'auto',
                              border: 'none'
                            }
                          },
                          children: [
                            {
                              type: 'section',
                              title: '基本信息',
                              collapsible: true,
                              myFavoriteId: '01305-PatList-151',
                              children: [
                                {
                                  type: 'textDisplay',
                                  templateFile: {
                                    type: 'SOP-prompts',
                                    name: '患者信息模板'
                                  }
                                }
                              ]
                            },
                            {
                              type: 'section',
                              title: '基本信息',
                              collapsible: true,
                              myFavoriteId: '01305-PatList-202',
                              children: [
                                {
                                  type: 'textDisplay',
                                  template: 'thi is a test'
                                }
                              ]
                            },
                            {
                              type: 'section',
                              title: '基本信息',
                              collapsible: true,
                              myFavoriteId: '01305-PatList-214',
                              children: [
                                {
                                  type: 'textDisplay',
                                  template: 'thi is a test'
                                }
                              ]
                            },
                            {
                              type: 'section',
                              title: '基本信息',
                              collapsible: true,
                              myFavoriteId: '01305-PatList-226',
                              children: [
                                {
                                  type: 'textDisplay',
                                  template: 'thi is a test'
                                }
                              ]
                            }
                          ]
                        },
                        {
                          type: 'section',
                          title: '编辑',
                          collapsible: true,
                          myFavoriteId: '01305-PatList-240',
                          children: [
                            {
                              type: 'dataViewer',
                              model: ''
                            }
                          ]
                        }
                      ]
                    }
                  ]
                },
                data: {
                  type: 'window',
                  body: [
                    {
                      type: 'section',
                      title: '工作内容',
                      collapsible: true,
                      myFavoriteId: '01305-PatList-259',
                      children: [
                        {
                          type: 'dataViewer',
                          model: ''
                        }
                      ]
                    }
                  ]
                }
              }
            }
          ],
          subContext: {
            path: 'detail',
            default: {}
          }
        }
      }
    ],
    style: {
      body: {
        display: 'flex',
        flex: '1 1 auto'
      }
    }
  },
  refreshInterval: 30,
  subContext: {
    path: '/patient',
    default: {}
  }
}
