{
  type: 'patList',
  ui: {
    type: 'window',
    head: [
      {
        type: 'toolbar',
        style: {
          height: '72px',
          alignItems: "center",
          gap: "16px"
        },
        children: [
          {
            type: 'button',
            customIcon: '/start-call.svg',
            event: {
              name: 'next-patient'
            },
            label: '开始叫号',
            style: {
              main: {
                flexDirection: "column",
                background: "unset",
                border: "unset",
                padding: "0px",
              }
            }
          },
          {
            type: 'button',
            customIcon: '/next-patient.svg',
            event: {
              name: 'next-patient'
            },
            label: '下一位',
            style: {
              main: {
                flexDirection: "column",
                background: "unset",
                border: "unset",
                padding: "0px"
              }
            }
          },
          {
            type: 'button',
            customIcon: '/pause-call.svg',
            event: {
              name: 'next-patient'
            },
            label: '暂停接诊',
            style: {
              main: {
                flexDirection: "column",
                background: "unset",
                border: "unset",
                padding: "0px"
              }
            }
          },
          {
            type: 'button',
            customIcon: '/refresh.svg',
            event: {
              name: 'refresh-patient-list'
            },
            label: '刷新',
            style: {
              main: {
                flexDirection: "column",
                background: "unset",
                border: "unset",
                padding: "0px"
              }
            }
          },
        ]
      }
    ],
    body: [
      {
        type: 'tocDetail',
        myFavoriteId: '01305-PatList-032',
        tocDefaultWidth: 800,
        style: {
          vars: {
            workspaceBackground: '#EDF7FD',
            toolbarBackground: '#DAF0FF',
            toolbarBorder: '#A9D5EA'
          }
        },
        toc: {
          type: 'stack',
          menu: [
            {
              id: 'patList',
              displayName: '患者列表'
            }
          ],
          myFavoriteId: '01305-PatList-045',
          children: {
            patList: {
              type: 'window',
              themes: ['body-flex'],
              body: [
                {
                  type: 'toc',
                  head: [
                    {
                      type: 'external',
                      externalSchema: '01308-PatCondition'
                    },
                  ],
                  headerHeight: 88,
                  footerHeight: 38,
                  footer: [
                    {
                      type: 'toolbar',
                      style: {
                        justifyContent: 'space-between'
                      },
                      children: [
                        {
                          type: 'textDisplay',
                          template: "共 <span style='color: #202020;font-weight: 500;'>{{count}}</span> 条"
                        },
                        {
                          type: 'textDisplay',
                          template: "<span style='color: #939393;'>最近刷新时间：{{lastRefreshTime}}</span>"
                        },
                      ]
                    }
                  ],
                  tableAttrs: {
                    pagination: false,
                    size: 'small',
                    columns: [
                      {
                        title: '号序',
                        width: 50,
                        dataIndex: 'queue_number',
                        key: 'queue_number',
                      },
                      {
                        title: '就诊状态',
                        width: 100,
                        dataIndex: 'visit_status_template',
                        key: 'visit_status_template',
                        customHtml: true
                      },
                      {
                        title: '姓名',
                        width: 100,
                        dataIndex: 'patient_name',
                        key: 'patient_name',
                        ellipsis: true
                      },
                      {
                        title: '性别',
                        width: 50,
                        dataIndex: 'gender',
                        key: 'gender',
                        ellipsis: true
                      },
                      {
                        title: '年龄',
                        width: 50,
                        dataIndex: 'patient_age',
                        key: 'patient_age',
                        ellipsis: true
                      },
                      {
                        title: '出生日期',
                        width: 100,
                        dataIndex: 'birth_date',
                        key: 'birth_date',
                        ellipsis: true
                      },
                      {
                        title: '就诊类型',
                        dataIndex: 'visit_type',
                        width: 80,
                        key: 'visit_type',
                        ellipsis: true
                      },
                      {
                        title: '诊断',
                        width: 130,
                        dataIndex: 'diagnosis_name',
                        key: 'diagnosis_name',
                        ellipsis: true
                      },
                      {
                        title: '费别',
                        dataIndex: 'fee_category',
                        width: 80,
                        key: 'fee_category',
                        ellipsis: true
                      },
                      {
                        title: '就诊卡号',
                        width: 100,
                        dataIndex: 'medical_card_no',
                        key: 'medical_card_no',
                        ellipsis: true
                      },
                      {
                        title: '患者ID',
                        width: 100,
                        dataIndex: 'patient_id',
                        key: 'patient_id',
                        ellipsis: true
                      },
                      {
                        title: '身份证号',
                        dataIndex: 'id_card_no',
                        width: 150,
                        customComponent: true,
                        key: 'id_card_no',
                        ellipsis: true
                      },
                      {
                        title: '挂号日期',
                        width: 100,
                        dataIndex: 'registration_date',
                        key: 'registration_date',
                        ellipsis: true
                      },
                      {
                        title: '挂号医生',
                        width: 100,
                        dataIndex: 'registering_doctor',
                        key: 'registering_doctor',
                        ellipsis: true
                      },
                      {
                        title: '午别',
                        width: 60,
                        dataIndex: 'visit_period',
                        key: 'visit_period',
                        ellipsis: true
                      },
                      {
                        title: '接诊时间',
                        width: 140,
                        dataIndex: 'triage_time',
                        key: 'triage_time',
                        ellipsis: true
                      },
                       {
                        title: '接诊医生',
                        width: 100,
                        dataIndex: 'attending_doctor',
                        key: 'attending_doctor',
                        ellipsis: true
                      },
                      {
                        title: '操作',
                        dataIndex: 'operationCol',
                        width: 110,
                        fixed: 'right',
                        key: 'operationCol',
                        customComponent: true
                      }
                    ],
                    customComponent: {
                      operationCol: {
                        type: "PatOperation",
                        ui: {
                          children: [
                            {
                              type: 'button',
                              customIcon: '/finish.svg',
                              showWhen: "context?.tableRowData?.visit_status === 'inProgress'",
                              style: {
                                main: {
                                  background: 'none',
                                  border: 'none',
                                  color: 'white',
                                  'box-shadow': 'none',
                                  padding: 0,
                                  height: 'auto'
                                },
                                vars: {
                                  iconBtnSize: '20px'
                                }
                              }
                            },
                            {
                              type: 'button',
                              customIcon: '/finish-disabled.svg',
                              showWhen: "context?.tableRowData?.visit_status !== 'inProgress'",
                              disabledWhen: "context?.tableRowData?.visit_status !== 'inProgress'",
                              style: {
                                main: {
                                  background: 'none',
                                  border: 'none',
                                  color: 'white',
                                  'box-shadow': 'none',
                                  padding: 0,
                                  height: 'auto'
                                },
                                vars: {
                                  iconBtnSize: '20px'
                                }
                              }
                            },
                            {
                              type: 'button',
                              customIcon: '/call.svg',
                              showWhen: "context?.tableRowData?.visit_status === 'pending' || context?.tableRowData?.visit_status === 'missed'",
                              style: {
                                main: {
                                  background: 'none',
                                  border: 'none',
                                  color: 'white',
                                  'box-shadow': 'none',
                                  padding: 0,
                                  height: 'auto'
                                },
                                vars: {
                                  iconBtnSize: '20px'
                                }
                              },
                              event: {
                                name: 'call-patient'
                              }
                            },
                            {
                              type: 'button',
                              customIcon: '/call-disabled.svg',
                              showWhen: "context?.tableRowData?.visit_status !== 'pending' && context?.tableRowData?.visit_status !== 'missed'",
                              disabledWhen: "context?.tableRowData?.visit_status !== 'pending' && context?.tableRowData?.visit_status !== 'missed'",
                              style: {
                                main: {
                                  background: 'none',
                                  border: 'none',
                                  color: 'white',
                                  'box-shadow': 'none',
                                  padding: 0,
                                  height: 'auto'
                                },
                                vars: {
                                  iconBtnSize: '20px'
                                }
                              }
                            }
                          ]
                        }
                      },
                      id_card_no: {
                        type: 'SensitiveDisplay',
                        model: "tableRowData.id_card_no"
                      }
                    }
                  },
                  emptyText: "emptyText",
                  event: {
                    listItems: 'list-patients',
                    selectItem: 'select-patient',
                    openItem: 'open-patient-record',
                    messageEndPoint: 'toc-patient-list',
                    scrollToBottom: 'load-more-patient-list',
                    setPageSize: 'set-page-size'
                  },
                  model: "data",
                  selectedLine: 0,
                  myFavoriteId: '01305-PatList-67'
                }
              ]
            }
          },
          subContext: {
            path: 'list',
            default: {}
          }
        },
        detail: {
          type: 'window',
          style: {
            body: {
              display: 'flex',
              flex: '1 1 auto'
            }
          },
          body: [
            {
              type: 'stack',
              style: {
                vars: {
                  themeBorderColor: '#365FD9',
                  themeActiveColor: '#365FD9'
                },
                menu: {
                  marginLeft: 0
                }
              },
              menu: [
                {
                  id: 'snapshot',
                  displayName: '健康信息快照'
                },
                {
                  id: 'm2',
                  displayName: '预问诊报告'
                },
                {
                  id: 'm3',
                  displayName: '上次就诊病历'
                },
                {
                  id: 'data',
                  displayName: '数据'
                }
              ],
              myFavoriteId: '01305-PatList-107',
              tabPosition: 'top',
              children: {
                snapshot: {
                  type: 'window',
                  style: {
                    main: {
                      display: 'flex',
                      flex: '1 1 auto'
                    },
                    body: {
                      display: 'flex',
                      flex: '1 1 auto'
                    }
                  },
                  body: [
                    {
                      type: 'layout2',
                      myFavoriteId: '01305-PatList-127',
                      leftWidth: 477,
                      style: {
                        child2: {
                          border: 'none',
                          width: '30rem'
                        },
                        main: {
                          flex: '1 1 auto',
                          display: 'flex'
                        }
                      },
                      children: [
                        {
                          type: 'panel',
                          style: {
                            main: {
                              display: 'block',
                              overflow: 'auto',
                              border: 'none',
                              background: '#EDF3FC'
                            }
                          },
                          children: [
                            {
                              type: 'Collapse',
                              attrs: {
                                bordered: false,
                                showArrow: false,
                                collapsible: "header",
                                ghost: true,
                                activeKey: ['patientInfo', 'allergyHistory', 'familyInfo'],
                              },
                              myFavoriteId: '01305-PatList-151',
                              children: [
                                {
                                  key: 'patientInfo',
                                  attrs: {
                                    showArrow: false,
                                    collapsible: "header",
                                  },
                                  slots: [
                                    {
                                      name: 'header',
                                      customIcon: {
                                        src: '/user.svg',
                                      },
                                      customComponent: {
                                        type: "textDisplay",
                                        template: "<div style='display:flex;align-items:center'><p style='font-size: 16px;font-weight: 700;margin-right: 8px'>{{patientInfo.name}}</p><p style='margin-right:8px'>|</p><p style='font-size: 16px;font-weight: 400;margin-right: 8px'>{{patientInfo.patient_age}}</p><p style='margin-right:8px'>|</p><p style='font-size: 16px;font-weight: 400;margin-right: 8px'>{{patientInfo.gender}}</p></div>"
                                      },
                                    }
                                  ],
                                  children: [
                                    {
                                      type: 'tag',
                                      mode: 'display',
                                      style: {
                                        main: {
                                          gap: '8px',
                                          marginBottom: '10px'
                                        }
                                      },
                                      options: [
                                        {
                                          value: 'first_visit',
                                          label: '初诊',
                                          show: 'patientInfo.insuranceInfo.FirstVisit',
                                          style: {
                                            background: '#E8F1FF',
                                            border: '1px solid #337DFF',
                                            borderRadius: '12px',
                                            fontSize: '14px',
                                            color: '#2666CF',
                                            height: '24px'
                                          }
                                        },
                                        {
                                          value: 'first_visit',
                                          label: '复诊',
                                          show: 'patientInfo.insuranceInfo.followUpVisit',
                                          style: {
                                            background: '#E8F1FF',
                                            border: '1px solid #337DFF',
                                            borderRadius: '12px',
                                            fontSize: '14px',
                                            color: '#2666CF',
                                            height: '24px'
                                          }
                                        },
                                        {
                                          value: 'self_pay',
                                          label: '自费',
                                          show: 'patientInfo.insuranceInfo.selfFunded',
                                          style: {
                                            background: '#FFFCD6',
                                            border: '1px solid #FAC219',
                                            borderRadius: '12px',
                                            fontSize: '14px',
                                            color: '#D49610',
                                            height: '24px'
                                          }
                                        },
                                        {
                                          value: 'self_pay',
                                          label: '医保',
                                          show: 'patientInfo.insuranceInfo.insuranceUser',
                                          style: {
                                            background: '#FFFCD6',
                                            border: '1px solid #FAC219',
                                            borderRadius: '12px',
                                            fontSize: '14px',
                                            color: '#D49610',
                                            height: '24px'
                                          }
                                        }
                                      ]
                                    },
                                    {
                                      type: 'Descriptions',
                                      attrs: {
                                        column: 1,
                                        size: 'small'
                                      },
                                      children: [
                                        {
                                          label: "身份证号",
                                          type: "SensitiveDisplay",
                                          model: 'patientInfo.registerInfo.id_card_no'
                                        },
                                        {
                                          label: "卡号",
                                          model: 'patientInfo.registerInfo.medical_card_no'
                                        },
                                        {
                                          label: "电话",
                                          model: 'patientInfo.phone'
                                        },
                                        {
                                          label: "地址",
                                          model: 'patientInfo.address'
                                        }
                                      ]
                                    }
                                  ]
                                },
                                {
                                  key: 'allergyHistory',
                                  attrs: {
                                    showArrow: false,
                                    collapsible: "header",
                                  },
                                  slots: [
                                    {
                                      name: 'header',
                                      customIcon: {
                                        src: '/allergy-history.svg',
                                      },
                                      customComponent: {
                                        type: "textDisplay",
                                        template: "<p style='font-size: 16px;font-weight: 700;margin-right: 8px'>过敏史</p>"
                                      },
                                    }
                                  ],
                                  children: [
                                    {
                                      type: "LoopRender",
                                      model: 'patientInfo.allergies',
                                      children: [
                                        {
                                          type: 'toolbar',
                                          style: {
                                            background: "#FFFFFF",
                                            margin: '0 15px 16px 15px',
                                            height: '52px'
                                          },
                                          children: [
                                            {
                                              type: 'button',
                                              customIcon: '/allergy.svg',
                                              style: {
                                                main: {
                                                  background: 'none',
                                                  border: 'none',
                                                  color: 'white',
                                                  'box-shadow': 'none',
                                                  padding: 0,
                                                  height: 'auto'
                                                },
                                                vars: {
                                                  iconBtnSize: '20px'
                                                },
                                                event: {
                                                  name: 'upload'
                                                }
                                              }
                                            },
                                            {
                                              type: 'textDisplay',
                                              template: "<span style='color: rgba(0,0,0,0.80);font-weight: 700;margin: 0 16px'>{{contextAddonsData.name}}</span><span style='color: rgba(0,0,0,0.80);margin-right: 8px'>{{contextAddonsData.type}}</span><span style='font-size: 12px;margin-right: 8px;color: #939393;'>{{contextAddonsData.update_date}}</span>"
                                            }
                                          ]
                                        }
                                      ]
                                    }
                                  ]
                                },
                                {
                                  key: 'familyInfo',
                                  attrs: {
                                    showArrow: false,
                                    collapsible: "header",
                                  },
                                  slots: [
                                    {
                                      name: 'header',
                                      customIcon: {
                                        src: '/family.svg',
                                      },
                                      customComponent: {
                                        type: "textDisplay",
                                        template: "<p style='font-size: 16px;font-weight: 700;margin-right: 8px'>家族史</p>"
                                      },
                                    }
                                  ],
                                  children: [
                                    {
                                      type: 'Descriptions',
                                      attrs: {
                                        column: 1,
                                        size: 'small'
                                      },
                                      model: "patientInfo.familyHistory"
                                    }
                                  ]
                                },
                              ]
                            }
                          ]
                        },
                        {
                          type: 'section',
                          title: '编辑',
                          collapsible: true,
                          myFavoriteId: '01305-PatList-240',
                          children: [
                            {
                              type: 'dataViewer',
                              model: ''
                            }
                          ]
                        }
                      ]
                    }
                  ]
                },
                data: {
                  type: 'window',
                  body: [
                    {
                      type: 'section',
                      title: '工作内容',
                      collapsible: true,
                      myFavoriteId: '01305-PatList-259',
                      children: [
                        {
                          type: 'dataViewer',
                          model: ''
                        }
                      ]
                    }
                  ]
                }
              }
            }
          ],
          subContext: {
            path: 'detail',
            default: {}
          }
        }
      }
    ],
    style: {
      body: {
        display: 'flex',
        flex: '1 1 auto'
      }
    }
  },
  refreshInterval: 30,
  subContext: {
    path: '/patient',
    default: {}
  }
}
