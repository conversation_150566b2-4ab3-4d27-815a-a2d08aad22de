<template>
  <div class="data-list">
    <div
      v-for="(node, index) in items"
      :key="index"
      :class="['data-list-item']"
      :style="style?.main"
    >
      <component
        :is="loadComponent(schema?.nodeSchema)"
        :context="node"
        :schema="schema?.nodeSchema"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { getContext, getNestedValue } from '@/lib/getContext'
import { loadComponent } from '@/components/componentRegistry'
import { computed } from 'vue'

const props = defineProps({
  schema: Object,
  context: Object,
  style: Object,
  externalSchema: String
})

const items = computed(() => {
  let context = getContext(props)
  return getNestedValue(context, props?.schema?.model)
})

defineExpose({ compName: props?.schema?.type || '-' })
</script>

<style lang="scss" scoped>
.data-list {
  //border: 1px solid red;

  .data-list-item {
    border: 1px solid lightblue;
    margin: 3px 5px;
    padding: 2px;
    border-radius: 3px;

    &.selected {
      //border: 1px solid blue;
      box-shadow: 2px 2px 3px #5e9797;
    }
  }

  .data-list-toolbar {
    display: flex;
    align-items: center;
    justify-content: right;
    background: #effafa;
    margin: 1px 2px;
    padding: 2px;
  }

  button {
    border: none;
    background: lightcyan;
    border-radius: 3px;
    margin: 1px 2px;
    box-shadow: 1px 1px 3px darkgray;
  }
}
</style>
