CREATE TABLE roles
(
    id                  INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    name                VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    type                VARCHAR(20)  NOT NULL,
    level               INT          NOT NULL,
    parent_id           INT,
    description         TEXT,
    privilege_json      JSONB        NOT NULL DEFAULT '{}',
    n_privilege_json    JSONB        NOT NULL DEFAULT '{}',
    security_class      VARCHAR(50),
    default_workspace   VARCHAR(50),
    service_area        VARCHAR(50),
    department          VARCHAR(50),
    is_provider_role    BOOLEAN      NOT NULL DEFAULT FALSE,
    can_override_orders BOOLEAN      NOT NULL DEFAULT FALSE,
    default_navigator   VARCHAR(50),
    default_flowsheet   VARCHAR(50),
    menu_config         JSONB        NOT NULL DEFAULT '{}',
    status              VARCHAR(20)  NOT NULL DEFAULT 'active',
    version             INT          NOT NULL DEFAULT 1,
    created_by          <PERSON><PERSON><PERSON><PERSON>(50)  NOT NULL,
    created_at          INT          NOT NULL DEFAULT EXTRACT(EPOCH FROM NOW()),
    updated_at          INT          NOT NULL DEFAULT EXTRACT(EPOCH FROM NOW()),
    CONSTRAINT uk_roles_name UNIQUE (name),
    CONSTRAINT ck_roles_type CHECK (type IN ('system', 'business', 'custom')),
    CONSTRAINT fk_roles_parent FOREIGN KEY (parent_id) REFERENCES roles (id)
);

-- Table comments
COMMENT ON TABLE roles IS 'System role definitions for access control and workflow configuration (角色表)';
COMMENT ON COLUMN roles.id IS 'Primary key identifier';
COMMENT ON COLUMN roles.name IS 'Unique role name identifier';
COMMENT ON COLUMN roles.type IS 'Role type: system|business|custom';
COMMENT ON COLUMN roles.level IS 'Role hierarchy level';
COMMENT ON COLUMN roles.parent_id IS 'Parent role ID for inheritance';
COMMENT ON COLUMN roles.description IS 'Role description and purpose';
COMMENT ON COLUMN roles.privilege_json IS 'JSON structure of role privileges';
COMMENT ON COLUMN roles.n_privilege_json IS 'JSON structure of negative privileges';
COMMENT ON COLUMN roles.security_class IS 'Security classification level';
COMMENT ON COLUMN roles.default_workspace IS 'Default workspace configuration';
COMMENT ON COLUMN roles.service_area IS 'Associated service area';
COMMENT ON COLUMN roles.department IS 'Associated department';
COMMENT ON COLUMN roles.is_provider_role IS 'Flag for provider-specific roles';
COMMENT ON COLUMN roles.can_override_orders IS 'Flag for order override permission';
COMMENT ON COLUMN roles.default_navigator IS 'Default navigation configuration';
COMMENT ON COLUMN roles.default_flowsheet IS 'Default flowsheet configuration';
COMMENT ON COLUMN roles.menu_config IS 'JSON menu configuration';
COMMENT ON COLUMN roles.status IS 'Role status: active|inactive';
COMMENT ON COLUMN roles.version IS 'Version number for optimistic locking';
COMMENT ON COLUMN roles.created_by IS 'Creator user ID';
COMMENT ON COLUMN roles.created_at IS 'Creation timestamp (seconds since epoch)';
COMMENT ON COLUMN roles.updated_at IS 'Last update timestamp (seconds since epoch)';

-- Indexes for performance
CREATE INDEX idx_roles_name ON roles (name);
CREATE INDEX idx_roles_type ON roles (type);
CREATE INDEX idx_roles_level ON roles (level);
CREATE INDEX idx_roles_parent_id ON roles (parent_id);
CREATE INDEX idx_roles_status ON roles (status) WHERE status = 'active';
