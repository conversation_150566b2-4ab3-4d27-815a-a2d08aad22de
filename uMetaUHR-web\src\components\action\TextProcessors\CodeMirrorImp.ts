import 'codemirror/lib/codemirror.css'
import 'codemirror/mode/javascript/javascript' // json代码高亮必须引入
import 'codemirror/mode/htmlmixed/htmlmixed' // html代码高亮必须引入
import 'codemirror/mode/markdown/markdown' // markdown代码高亮必须引入
import 'codemirror/mode/gfm/gfm' // markdown代码高亮必须引入
import 'codemirror/mode/pegjs/pegjs' // markdown代码高亮必须引入
import 'codemirror/addon/lint/lint.css'
import 'codemirror/addon/lint/lint'
import 'codemirror/addon/lint/json-lint'

// 主题样式（我直接用了纯白色的，看着比较舒服）
import 'codemirror/theme/eclipse.css'
import 'codemirror/theme/idea.css'
import 'codemirror/theme/rubyblue.css'

// 括号显示匹配
import 'codemirror/addon/edit/matchbrackets'
import 'codemirror/addon/selection/active-line'
// 括号、引号编辑和删除时成对出现
import 'codemirror/addon/edit/closebrackets'
// 折叠代码要用到一些玩意
import 'codemirror/addon/fold/foldgutter.css'
import 'codemirror/addon/fold/foldgutter'
import 'codemirror/addon/fold/xml-fold'
import 'codemirror/addon/fold/foldcode'
import 'codemirror/addon/fold/brace-fold'
import 'codemirror/addon/fold/indent-fold.js'
import 'codemirror/addon/fold/markdown-fold.js'
import 'codemirror/addon/fold/comment-fold.js'
