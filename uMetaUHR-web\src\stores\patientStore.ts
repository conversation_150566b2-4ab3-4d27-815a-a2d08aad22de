import type { Patient, NewPatient } from './metaPinia-types'
import { createMetaPinia } from './metaPinia'

/**
 * 患者数据管理扩展
 */
export function createPatientStore(initialData: { patients?: Patient[]; currentPatient?: Patient | null }) {
  const med = createMetaPinia(initialData)

  // 患者专用操作扩展
  const patientApi = {
    ...med,

    /**
     * 添加新患者
     */
    addPatient(patient: NewPatient): Patient {
      if (!patient.name) throw new Error('患者姓名不能为空')
      
      const newPatient: Patient = {
        id: `patient_${Date.now()}`,
        name: patient.name,
        age: patient.age,
        gender: patient.gender,
        orders: [],
        labs: [],
        ...patient
      }

      if (!med.data.patients) med.data.patients = []
      med.data.patients.push(newPatient)
      med.logAction(`添加患者 ${newPatient.name}`)
      return newPatient
    },

    /**
     * 快速获取患者列表
     */
    getPatientList() {
      return med.data.patients || []
    },

    /**
     * 更新患者信息
     */
    updatePatient(patientId: string, updates: Partial<Patient>): boolean {
      const patient = med.data.patients?.find(p => p.id === patientId)
      if (!patient) return false

      const oldValue = { ...patient }
      Object.assign(patient, updates)
      med.logAction(`更新患者信息 ${patientId}`)
      return true
    }
  }

  return patientApi
}

export type PatientStore = ReturnType<typeof createPatientStore>
