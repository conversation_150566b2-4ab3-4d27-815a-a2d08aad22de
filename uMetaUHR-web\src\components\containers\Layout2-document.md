## 控件名称

LayoutThreePanel

## 控件描述

### 组件定位

三面板布局容器组件，提供可调整大小的左右面板和自动调整宽度的中间面板。主要用于需要同时展示多个内容区域的应用场景。

### 核心功能

1. 可调整大小的左右面板（固定宽度）
2. 自动调整宽度的中间面板
3. 面板尺寸持久化缓存
4. 平滑的拖拽调整体验
5. 支持动态显示/隐藏右侧面板

### 使用场景

1. 主-详-辅布局（如左侧导航，中间主要内容，右侧工具面板）
2. 代码编辑器布局（左侧文件树，中间编辑器，右侧预览）
3. 数据分析工具（左侧数据源，中间分析区，右侧可视化配置）

### 交互规范

1. 用户可以通过拖拽分隔线调整左右面板宽度
2. 最小面板宽度限制为150px
3. 拖拽时显示col-resize光标
4. 拖拽过程中禁用文本选择
5. 调整后的尺寸会自动保存到缓存

```mermaid
stateDiagram-v2
    [*] --> Idle
    Idle --> Dragging: 鼠标按下分隔线
    Dragging --> Resizing: 鼠标移动
    Resizing --> Dragging: 继续移动
    Dragging --> Idle: 鼠标释放
    Resizing --> Idle: 鼠标释放
```

## schema

### 结构

```json
{
  "type": "LayoutThreePanel",
  "style": {
    "main": {},
    // 主容器样式
    "child1": {},
    // 左侧面板样式
    "child2": {},
    // 中间面板样式 
    "child3": {}
    // 右侧面板样式
  },
  "children": [
    { /* 左侧面板组件配置 */ },
    { /* 中间面板组件配置 */ },
    { /* 右侧面板组件配置 */ }
  ],
  "myFavoriteId": "unique-id"
  // 用于缓存尺寸的唯一ID
}
```

### 字段说明

- `style.main`: 主容器样式对象
- `style.child1`: 左侧面板样式对象
- `style.child2`: 中间面板样式对象
- `style.child3`: 右侧面板样式对象
- `children`: 子组件配置数组（最多3个）
    - 索引0: 左侧面板组件
    - 索引1: 中间面板组件
    - 索引2: 右侧面板组件（可选）
- `myFavoriteId`: 唯一标识符，用于持久化存储面板尺寸

### 注意事项

1. 右侧面板是可选的，当children数组长度小于3时不会显示
2. 面板最小宽度固定为150px，无法通过schema配置修改
3. 必须提供myFavoriteId才能启用尺寸持久化功能

## context

### 数据流

```mermaid
flowchart LR
    Parent[父组件] -->|提供context| LayoutThreePanel
    LayoutThreePanel -->|传递context| Left[左侧子组件]
    LayoutThreePanel -->|传递context| Middle[中间子组件]
    LayoutThreePanel -->|传递context| Right[右侧子组件]
```

### 使用说明

1. 组件会将接收到的context原样传递给所有子组件
2. 子组件可以通过props.context访问上下文数据
3. 组件本身不修改或增强context

## 事件

### 用户事件

1. 面板调整事件
    - 触发: 用户拖拽左右分隔线时
    - 参数: 无显式事件发射，但会更新内部状态并保存到缓存

### 系统事件

1. 尺寸缓存加载
    - 触发: 组件挂载时
    - 行为: 从缓存加载之前保存的面板尺寸
2. 尺寸缓存保存
    - 触发: 用户完成面板调整后
    - 行为: 将当前面板尺寸保存到缓存

### 注意事项

1. 组件不直接对外暴露事件，所有交互通过状态变化体现
2. 尺寸变化通过宽度ref变量反映，父组件可以监听这些变化

## 样式规范

1. 主容器使用flex布局
2. 左右面板有1px的边框分隔
3. 分隔线宽度为7px，悬停时显示棕色拖拽手柄
4. 中间面板有最小宽度200px的限制
5. 所有面板默认启用overflow-auto

## 性能考虑

1. 使用debounce技术优化频繁的resize事件
2. 尺寸变化只在拖拽结束时保存到缓存
3. 拖拽过程中使用CSS transforms而不是直接修改宽度属性