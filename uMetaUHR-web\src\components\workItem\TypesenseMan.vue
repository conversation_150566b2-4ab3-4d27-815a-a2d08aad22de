<template>
  <div class="sop">
    <component
      :is="loadComponent(props.schema?.ui?.type)"
      v-if="props.schema"
      :context="getContext(props)"
      :schema="props.schema?.ui"
    />
    <WnDialog ref="dialog" />
  </div>
</template>

<script lang="ts" setup>
import { defineProps, provide, ref } from 'vue'
import { loadComponent } from '../componentRegistry'
import { getContext } from '@/lib/getContext'
import WnDialog from '@/components/containers/WnDialog.vue'
import { listCollections, typesenseClient } from '@/lib/typesense/typesense'
import { dbList } from '@/lib/dataMan'
import { isEmpty } from '@/lib/util'
import { DateTime } from 'luxon'

let props = defineProps<{
  schema: any
  context: { [key: string]: any }
}>()

const dialog = ref<InstanceType<typeof WnDialog>>()

let collections = [] as any
provide('list-docs', async (param: any = {}) => {
  collections = await listCollections()
  return collections
})

provide('select-doc', async (param: any = {}) => {
  const { selectedLine = 0, data: { name } = {} as any } = param || {}
  const context = getContext(props)
  const data = collections[selectedLine]
  context.detail = { data, docType: name }
})

provide('query-condition', async (param: any = {}) => {
  const {
    detail: { data, docType },
    category: [cat = ''] = []
  } = getContext(props)
  const filter = cat ? { filter_by: `category:=${cat}` } : {}
  return {
    search: {
      docType,
      param: { query_by: 'name', typo_tokens_threshold: 0, max_candidates: 30, ...filter }
    }
  }
})

provide('select-suggestion', async (param: any = {}) => {
  let { doc } = param
  const context = getContext(props)
  context.doc = doc
})

const collectionSchema = {
  name: 'col1',
  fields: [
    { name: 'recId', type: 'string', index: true },
    { name: 'name', type: 'string', index: true },
    { name: 'category', type: 'string', index: true }
  ]
} as any

provide('typesense-new-collection', async (param: any = {}) => {
  const collections = await typesenseClient.collections().retrieve()
  const colName = collectionSchema.name
  const c = collections.find((c) => c.name === collectionSchema.name)

  const start = DateTime.now()
  if (c) {
    await typesenseClient.collections(colName).delete()
  }

  await typesenseClient.collections().create(collectionSchema)

  const end = DateTime.now()
  const timeSpent = end.diff(start, 'seconds').toMillis() / 1000 // Convert milliseconds to seconds
  await window.popAlert({ message: `done with collection in ${timeSpent} seconds` })
})

const fillTypesense = async (sql: string, param: any = {}) => {
  const start = DateTime.now()
  let offset = 0
  for (let i = 0; i < 10000; i++) {
    const data = await dbList({ sql, param: [offset] })
    if (isEmpty(data)) break
    offset += data.length

    try {
      await typesenseClient
        .collections(collectionSchema.name)
        .documents()
        .import(data, { action: 'upsert' })
    } catch (e) {
      console.error(e)
    }
  }
  const end = DateTime.now()
  const timeSpent = end.diff(start, 'seconds').toMillis() / 1000 // Convert milliseconds to seconds

  await window.popAlert({ message: `done for ${offset} records in ${timeSpent} seconds` })
}

provide('typesense-fill-diag', async (param: any = {}) => {
  const sql = `
    select id::text as "recId", name, 'dx' as category
    from "maxEMR".diagnosis_def
    limit 5000 offset ?::int
  `
  await fillTypesense(sql)
})

provide('typesense-fill-meds', async (param: any) => {
  const sql = `
    select id::text as "recId", name, 'meds' as category
    from "maxEMR".meds_dict2
    limit 5000 offset ?::int
  `
  await fillTypesense(sql)
})

defineExpose({ compName: props?.schema?.type || '-' })
</script>
<style lang="scss" scoped>
.sop {
  display: flex;
  flex: 1 1 auto;
}
</style>
