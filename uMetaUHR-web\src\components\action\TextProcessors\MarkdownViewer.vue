<template>
  <div :class="['markdown-viewer', schema?.theme]">
    <span v-if="schema?.label" class="label">{{ schema?.label }}</span>
    <div
      v-if="!editMode"
      :class="['view-panel']"
      :style="{ 'border-top': schema?.label ? 'none' : '' }"
      v-html="markdownHTML"
    />
    <textarea
      v-else
      :value="rawData"
      rows="10"
      @blur="setVal"
      @input="handleInput($event.target.value)"
    />
    <label v-if="'editMode' in props?.schema" class="switch">
      <input :value="editMode" type="checkbox" @change="handleToggle" />
      <span class="slider"></span>
    </label>
  </div>
</template>

<script lang="ts" setup>
import { computed, defineProps, onMounted, ref, watch } from 'vue'
import { marked } from 'marked'
import { getNestedValue, setNestedValue } from '@/lib/getContext'
import Handlebars from 'handlebars'
import { Debouncer } from '@/lib/Debouncer'

const props = defineProps<{
  schema: {
    type: string
    model: string
    templateModel?: string
  }
  context: { [key: string]: any }
  markdownText?: String
}>()

let markdownHTML = ref('') as any
const editMode = ref(false)

const rawData = computed(() => getRawData())

function computedMarkdown(data?: any): string {
  const d = data ?? getRawData()
  // Ensure `d` is a string, if it's an object, convert it to string representation
  const dataString = typeof d === 'object' ? JSON.stringify(d) : String(d || '')
  if (!props?.schema?.templateModel) return dataString
  const template = getNestedValue(props.context, props.schema.templateModel)
  if (!template || typeof template !== 'string') return dataString
  try {
    // Compile the template with Handlebars and return the result as a string
    return Handlebars.compile(template)(d) || ''
  } catch (error) {
    // Handle potential errors in template compilation and ensure return is a string
    console.error('Error compiling template:', error)
    return ''
  }
}

let lastEditedText = ''

function handleInput(value: any) {
  try {
    lastEditedText = value
  } catch (e) {
    console.error(e)
  }
}

function setVal() {
  setNestedValue(props.context, props.schema.model, lastEditedText)
}

function handleToggle() {
  editMode.value = !editMode.value
  if (!editMode.value) {
    setNestedValue(props.context, props.schema.model, lastEditedText)
  } else {
    lastEditedText = getNestedValue(props.context, props.schema.model)
  }
}

function getRawData() {
  if (props?.markdownText) {
    //from property
    return props?.markdownText
  }
  if (props?.schema?.markdownText) {
    //from schema
    return props?.schema?.markdownText
  }
  if (!props?.schema?.model) return '' //from model
  return getNestedValue(props.context, props?.schema.model)
}

const debouncer = new Debouncer(200, { leading: false, trailing: true })

const updateView = debouncer.debounce(async (newValue?: any) => {
  const text = computedMarkdown(await newValue) || null
  if (typeof text !== 'string') return 'error'
  markdownHTML.value = marked.parse(text)
})

onMounted(() => updateView())
watch(() => getRawData(), updateView)
watch(
  () =>
    props?.schema?.templateModel &&
    getNestedValue(props.context, props?.schema?.templateModel || ''),
  async () => updateView()
)

defineExpose({ compName: props?.schema?.type || '-' })
</script>

<style lang="scss" scoped>
.markdown-viewer {
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
  position: relative;
  min-height: 2rem;
  padding: 5px;

  .view-panel {
    padding: 5px;
    font-size: 9pt;
    border: 1px solid lightblue;
    //border-top: none;
    border-radius: 0 0 3px 3px;
  }

  .label {
    background: rgba(173, 216, 230, 0.44);
  }

  .switch {
    position: absolute;
    top: 9px;
    right: 10px;
    display: inline-block;
    width: 30px;
    height: 15px;

    input {
      opacity: 0;
      width: 0;
      height: 0;

      &:checked + .slider {
        background-color: lightblue;

        &:before {
          transform: translateX(15px);
        }
      }
    }

    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: 0.4s;
      border-radius: 15px;

      &:before {
        position: absolute;
        content: '';
        height: 13px;
        width: 13px;
        left: 1px;
        bottom: 1px;
        background-color: white;
        transition: 0.4s;
        border-radius: 50%;
      }
    }

    &:hover .slider {
      background-color: #bbb;
    }
  }
}

.editMode.markdown-viewer {
  border: 1px solid lightgray;
  margin: 2px;
  border-radius: 5px;
}

h3 {
}
</style>
