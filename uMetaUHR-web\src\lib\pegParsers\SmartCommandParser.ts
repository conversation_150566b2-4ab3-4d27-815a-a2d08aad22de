import { htmlToText, toHalfWidthPunctuation } from '@/lib/textFormater'

function generateFriendlyErrorMessage(input: string, error: any): string {
  // 获取错误的行号、列号和预期的匹配规则
  const { line, column } = error.location.start
  const expected = error.expected.map((e: any) => e.description).join(', ')

  // 提取错误上下文的附近几行内容
  const inputLines = input.split('\n')
  const errorContext = [
    inputLines[line - 2] || '', // 上一行
    inputLines[line - 1] || '', // 当前行
    ' '.repeat(column - 1) + '^', // 错误指示符
    inputLines[line] || '' // 下一行
  ].join('\n')

  // 构造友好提示信息
  return `
错误位置：第 ${line} 行，第 ${column} 列
错误上下文：
${errorContext}

解析失败，预期匹配：${expected}

可能的原因：
1. 检查是否缺少分隔符（如 '---' 或 '==='）。
2. 检查是否有未闭合的引号或未匹配的参数。
3. 确保命令名称后有正确的换行符。
4. 如果这是最后一段内容，确保以换行结束。
  `
}

async function parseInput(input: string) {
  let parser
  try {
    // 动态加载解析器
    parser = (await import('@/pegrule/smart-command')) as any
    // 调用解析器解析输入
    input = toHalfWidthPunctuation(input)
    input = htmlToText(input)
    return parser.parse(input + '\n')
  } catch (error) {
    // 提取错误信息并生成友好的错误提示
    if (error instanceof parser.SyntaxError) {
      const message = generateFriendlyErrorMessage(input, error)
      console.error(message)
      throw new Error(message) // 抛出用户友好的错误信息
    } else {
      console.error('Unexpected error during parsing:', error)
      throw error // 其他未知错误直接抛出
    }
  }
}

export { parseInput }
