{
  type: 'workspace',
  ui: {
    type: 'stack',
    tabPosition: 'top-navi',
    style: {
      vars: {
        themeColor: 'white',
        themeBC: '#364EAF',
        themeActiveBC: '#7481C8',
        themeActiveColor: 'white',
        themeContentBC: 'darkgrey'
      },
      toolbar: {
        height: '56px',
        'line-height': '56px'
      },
      menu: {
        height: '80%',
        marginTop: '5px'
      }
    },
    menu: [
      {
        id: 'patList',
        displayName: '挂号患者',
        customIcon: {
          src: '/patient-admin.svg'
        }
      },
      {
        id: 'statistics',
        displayName: '统计报表',
        customIcon: {
          src: '/statistics.svg'
        }
      },
      {
        id: 'information-list',
        displayName: '消息列表',
        icon1: {
          src: '/information-list.svg'
        }
      },
      {
        type: 'toolbar',
        style: {
          background: 'none',
          margin: 0,
          padding: 0,
          color: 'white',
          'justify-content': 'right'
        },
        children: [
          {
            type: 'button',
            customIcon: '/upload.svg',
            style: {
              main: {
                background: 'none',
                border: 'none',
                color: 'white',
                'box-shadow': 'none',
                height: '80%',
                margin: '3px',
                width: '40px'
              },
              vars: {
                iconBtnSize: '30px'
              },
              event: {
                name: 'upload'
              }
            }
          },
          {
            type: 'button',
            customIcon: '/signal.svg',
            style: {
              main: {
                background: 'none',
                border: 'none',
                color: 'white',
                'box-shadow': 'none',
                height: '80%',
                margin: '3px',
                width: '40px'
              },
              vars: {
                iconBtnSize: '30px'
              },
              event: {
                name: 'upload'
              }
            }
          },
          {
            type: 'button',
            customIcon: '/notification.svg',
            style: {
              main: {
                background: 'none',
                border: 'none',
                color: 'white',
                'box-shadow': 'none',
                height: '80%',
                margin: '3px',
                width: '40px'
              },
              vars: {
                iconBtnSize: '30px'
              },
              event: {
                name: 'upload'
              }
            }
          },
          {
            type: 'button',
            customIcon: '/application.svg',
            style: {
              main: {
                background: 'none',
                border: 'none',
                color: 'white',
                'box-shadow': 'none',
                height: '80%',
                margin: '3px',
                width: '40px'
              },
              vars: {
                iconBtnSize: '30px'
              },
              event: {
                name: 'upload'
              }
            }
          },
          {
            type: 'button',
            customIcon: '/home.svg',
            style: {
              main: {
                background: 'none',
                border: 'none',
                color: 'white',
                'box-shadow': 'none',
                height: '80%',
                margin: '3px',
                width: '40px'
              },
              vars: {
                iconBtnSize: '30px'
              },
              event: {
                name: 'upload'
              }
            }
          },
          {
            type: 'verticalDivider',
            style: {
              'margin-top': '10px'
            }
          },
          {
            type: 'button',
            customIcon: '/user-icon.svg',
            label: '成吉思汗',
            style: {
              main: {
                background: 'none',
                border: 'none',
                color: 'white',
                'box-shadow': 'none',
                height: '80%',
                margin: '3px'
              },
              vars: {
                iconBtnSize: '20px'
              },
              event: {
                name: 'upload'
              }
            },
            event: {
              name: 'quality-control'
            }
          }
        ]
      },
      {
        type: 'button',
        customIcon: '/logo.svg',
        style: {
          main: {
            background: 'none',
            border: 'none',
            color: 'white',
            'box-shadow': 'none',
            height: '80%',
            margin: '3px'
          }
        }
      }
    ],
    children: {
      patList: {
        type: 'external',
        externalSchema: '01308-PatientList'
      },
      statistics: {
        type: 'external',
        externalSchema: '00300-MasterDataFile'
      },
      'information-list': {
        type: 'external',
        externalSchema: '00600-PatIntake'
      }
    },
    myFavoriteId: '00200-Workspace.stack-60',
    event: {
      messageEndPoint: 'patient-workspace'
    }
  },
  subContext: {
    path: '/patient',
    default: {}
  }
}
