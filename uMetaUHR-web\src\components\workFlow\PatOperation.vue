<template>
  <div class="patent-operation">
    <component
      :is="loadComponent(child?.type)"
      v-for="(child, index) in schema.ui.children"
      :key="index"
      :context="getContext(props)"
      :schema="child"
    />
  </div>
</template>

<script lang="ts" setup>
import { defineProps } from 'vue'
import { loadComponent } from '../componentRegistry'
import { getContext } from '@/lib/getContext'

const props = defineProps<{
  schema: {
    type: string
    ui?: any,
  }
  context: { [key: string]: any },
  record: any
}>()

</script>