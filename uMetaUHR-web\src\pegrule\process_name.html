<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <link href="/logo.svg" rel="icon">
  <meta content="width=device-width, initial-scale=1.0" name="viewport">
  <title>文本处理工具</title>
  <style>
      body {
          font-family: Arial, sans-serif;
          padding: 20px;
      }

      textarea {
          width: 100%;
          height: 200px;
          margin-bottom: 20px;
          padding: 10px;
          font-family: monospace;
          font-size: 14px;
      }

      button {
          padding: 10px 20px;
          background-color: #4CAF50;
          color: white;
          border: none;
          cursor: pointer;
          font-size: 16px;
          margin-right: 10px;
      }

      button:hover {
          background-color: #45a049;
      }

      #output {
          white-space: pre-wrap;
          background-color: #f4f4f4;
          padding: 10px;
          border: 1px solid #ddd;
          font-family: monospace;
          margin-top: 20px;
      }
  </style>
</head>
<body>

<h1>文本处理工具</h1>

<textarea id="inputText" placeholder="将文本粘贴在此处..."></textarea><br>
<button onclick="processText()">处理文本</button>
<button onclick="copyToClipboard()">复制结果</button>

<h2>处理后的结果：</h2>
<div id="output"></div>

<script>
  function processText() {
    // 获取输入的文本
    const inputText = document.getElementById('inputText').value

    // 按行分割文本
    const lines = inputText.split('\n')

    // 存储所有名称的对象（以名称为键）
    const uniqueEntries = {}

    // 处理每一行
    lines.forEach(line => {
      // 使用正则表达式提取名称
      const match = line.match(/"([^"]+)"/)
      if (match && match[1]) {
        const name = match[1]
        uniqueEntries[name] = line  // 使用名称去重
      }
    })

    // 按名称倒序排序
    const sortedEntries = Object.values(uniqueEntries).sort((a, b) => {
      const nameA = a.match(/"([^"]+)"/)[1]
      const nameB = b.match(/"([^"]+)"/)[1]
      return nameB.localeCompare(nameA)  // 倒序排序
    })

    // 输出处理后的文本
    document.getElementById('output').textContent = sortedEntries.join('\n')
  }

  function copyToClipboard() {
    const outputText = document.getElementById('output').textContent
    if (outputText) {
      // 创建一个临时textarea来执行复制
      const tempTextArea = document.createElement('textarea')
      tempTextArea.value = outputText
      document.body.appendChild(tempTextArea)
      tempTextArea.select()
      document.execCommand('copy')
      document.body.removeChild(tempTextArea)
      alert('已复制处理后的结果到剪贴板!')
    } else {
      alert('没有处理后的文本可供复制!')
    }
  }
</script>

</body>
</html>