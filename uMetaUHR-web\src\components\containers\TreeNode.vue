<template>
  <div class="tree-node">
    <span>{{ node.label }}</span>
    <button @click="updateLabel">Update Label</button>
    <button @click="addChild">Add Child</button>
    <div v-if="node.children.length">
      <TreeNode v-for="child in node.children" :key="child.id" :node="child" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { inject, ref } from 'vue'

interface TreeNode {
  id: number
  label: string
  children: TreeNode[]
}

const props = defineProps<{ node: TreeNode }>()

const store = inject<any>('store')

const newLabel = ref('')

const updateLabel = () => {
  store.mutations.updateNode(props.node.id, newLabel.value)
}

const addChild = () => {
  const newNode: TreeNode = {
    id: Date.now(),
    label: 'New Node',
    children: []
  }
  store.mutations.addNode(props.node.id, newNode)
}
defineExpose({ compName: props?.schema?.type || '-' })
</script>

<style lang="scss" scoped>
.tree-node {
  border-left: 1px solid #ccc;
  padding-left: 1rem;
}
</style>
