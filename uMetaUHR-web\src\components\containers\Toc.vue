<template>
  <div :class="`${theme} field-w-toc`" :style="schema?.style?.main" ref="wToc">
    <div v-if="schema?.toolbar" :style="schema?.style?.toolbar" class="toolbar" ref="wTocToolBar">
      <component
        :is="loadComponent(child?.type)"
        v-for="(child, index) in schema?.toolbar"
        :key="index"
        :context="getContext(props)"
        :schema="child"
      />
    </div>
    <div
      v-if="schema!.head"
      :key="`head${state.headRenderSeq}`"
      class="field-w-window-head"
      ref="wTocHead"
    >
      <component
        :is="loadComponent(child?.type)"
        v-for="(child, index) in schema!.head || []"
        :key="`panel-child-${index}`"
        :context="getContext(props)"
        :schema="child"
      />
    </div>
    <div class="field-w-toc-body" v-if="schema?.tableAttrs">
      <a-table
        v-bind="state?.schema?.tableAttrs"
        :data-source="tableData"
        key="tocTableKey"
        ref="tocTable"
        :loading="state?.loading"
        :scroll="state.scroll"
        :rowClassName="rowClassName"
        :customRow="
          (_: any, index: number) => {
            return {
              onClick: (event: PointerEvent) => handleRowClick(index, event),
              onDblclick: (event: PointerEvent) => handleRowDblClick(index, event)
            }
          }
        "
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.customComponent">
            <component
              :is="loadComponent(schema.tableAttrs.customComponent[column.key].type)"
              :context="{ ...getContext(props), tableRowData: record }"
              :schema="schema.tableAttrs.customComponent[column.key]"
              :record="record"
            />
          </template>
          <template v-else-if="column.customHtml">
            <div v-html="record[column.key]"></div>
          </template>
          <template v-else>
            {{ record[column.key] }}
          </template>
        </template>
        <template #emptyText v-if="state?.schema?.emptyText">
          <a-empty :description="getNestedValue(context, state?.schema?.emptyText)" />
        </template>
      </a-table>
      <!-- <table key="tocTableKey" ref="tocTable">
        <thead>
          <tr>
            <template
              v-for="(col, colIndex) in state?.schema?.columnDef"
              :key="`hd-col-${colIndex}-${sortEventKey}`"
            >
              <th :style="col?.style" @click="handleSort($event, col, colIndex)">
                {{ col.displayName || col.field }} <sup>{{ getSortMarker(colIndex) || '' }}</sup>
              </th>
            </template>
          </tr>
        </thead>
        <tbody :key="`${tocKey}`" ref="sortableTable">
          <template v-for="(row, rowIndex) in state?.data" :key="`tr-${rowIndex}`">
            <tr
              :class="`fw-${state.selectedLine === rowIndex ? 'selectedLine' : 'normal-items'}`"
              :data-id="rowIndex"
              tabindex="-1"
              @click="selectLine($event, rowIndex, true)"
              @dblclick="openItem($event, rowIndex)"
            >
              <td
                v-for="(col, colIndex) in state?.schema?.columnDef"
                :key="`col-${colIndex}-`"
                :style="col?.style"
              >
                {{ lineName(row, col, colIndex) || '&nbsp;' }}
              </td>
            </tr>
          </template>
        </tbody>
      </table> -->
    </div>
    <div v-if="schema!.footer" ref="wTocFooter">
      <component
        :is="loadComponent(child?.type)"
        v-for="(child, index) in schema!.footer || []"
        :key="`toc-footer-child-${index}`"
        :context="getContext(props)"
        :schema="child"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  computed,
  getCurrentInstance,
  inject,
  nextTick,
  onBeforeUnmount,
  onMounted,
  onUnmounted,
  reactive,
  ref,
  watch
} from 'vue'
import { JSONPath } from 'jsonpath-plus'
import Sortable from 'sortablejs'
import { Debouncer } from '@/lib/Debouncer'
import { loadComponent } from '@/components/componentRegistry'
import { getContext, getNestedValue } from '@/lib/getContext'
import { cacheManager } from '@/lib/CacheManager'
import type { CommandFlow } from '@/components/action/commandFlow/CommandFlow'
import type { CommandManager } from '@/components/action/commandFlow/CommandFlowManager'
import ServerTime from '@/lib/ServerTime'
import { useEventBus } from '@/components/mixins/eventBus'
import { Table } from 'ant-design-vue'

interface ColumnDef {
  field: string
  displayName?: string
  format?: string
  style?: Record<string, string>
  concept?: string
  dataType?: string
  model?: string
  type?: string
  fixed?: boolean
  sortable?: boolean | string
  width?: string
  customComponent?: Record<string, Record<string, string>>
  customHtml?: boolean
}

interface Schema {
  style?: {
    main?: Record<string, string>
    toolbar?: Record<string, string>
  }
  toolbar?: any
  head?: any
  tableAttrs?: any
  columnDef?: ColumnDef[]
  myFavoriteId?: string
  selectedLineModel?: string
  naviWithKey?: string
  event?: {
    selectItem?: string
    listItems?: string
    shouldRefreshItems?: string
    openItem?: string
    tocCommandSelectTopic?: string
    scrollToBottom?: string
    setPageSize?: string
    messageEndPoint?: string
  }
  emptyText?: any
  table?: any
  footer?: any
  model?: string
  headerHeight?: number
  footerHeight?: number
  customComponent?: any
}

interface Props {
  schema?: Schema
  context?: any
  theme?: string
  toolbarSeq?: number
  toolbar?: any
  columnDef?: ColumnDef[]
  data?: any[]
  selectedLine?: number
  filterFun?: ((...args: any[]) => any) | null
  headRenderSeq?: string
}

const props = defineProps<Props>()

interface State {
  schema: Schema
  headRenderSeq: string
  columnDef: ColumnDef[]
  selectedLine: number
  toolbar: any
  theme: string
  toolbarSeq: number
  filterFun: ((...args: any[]) => any) | null
  loading: boolean
  scroll: { x: number; y: number }
  data: any[]
}

const state = reactive<State>({
  schema: props.schema || { model: '' },
  headRenderSeq: props?.headRenderSeq || '',
  columnDef: props.columnDef || [],
  selectedLine: props.selectedLine || -1,
  toolbar: props.toolbar || {},
  theme: props.theme || 'table-detail',
  toolbarSeq: props.toolbarSeq || 0,
  filterFun: props.filterFun || null,
  loading: false,
  scroll: { x: 1920, y: 300 },
  data: props.data || []
})

const sentinel = ref(null)

const tableData = computed(() => {
  return getNestedValue(props.context, props.schema?.model || '') || []
})

const rowClassName = (_: any, index: number) => {
  if (index === state.selectedLine) {
    return 'custom-selected-row'
  } else if (index % 2 === 1) {
    return 'table-striped-row'
  } else {
    return 'table-normal-row'
  }
}

const tocTable = ref<InstanceType<typeof Table> | null>(null)
// const tocTable = ref<HTMLElement | null>(null);
const wToc = ref<HTMLElement | null>(null)
const wTocToolBar = ref<HTMLElement | null>(null)
const wTocHead = ref<HTMLElement | null>(null)
const wTocFooter = ref<HTMLElement | null>(null)
const sortableTable = ref<HTMLElement | null>(null)

const {
  schema: {
    event: {
      selectItem: eventSelectItem = 'toc-select-line',
      listItems: eventListItem = 'toc-list-data',
      shouldRefreshItems: eventRefreshItems = 'toc-should-refresh',
      openItem: eventOpenItem = 'toc-open-line',
      tocCommandSelectTopic = 'command-select-topic',
      scrollToBottom: eventScrollToBottom = 'toc-scroll-to-bottom',
      setPageSize: eventSetPageSize = 'toc-set-page-size'
    } = {}
  } = {}
} = props

const tocListItem = inject(eventListItem, () => 1) as Function
const scrollToBottom = inject(eventScrollToBottom, () => 1) as Function
const setPageSize = inject(eventSetPageSize, () => 1) as Function

const tocTableKey = ref(1)
const sortEventKey = ref(1)
const tocKey = ref(1)
let sortOrder: number[] = []
let sortInfo: Record<number, string> = {} // Store only direction

const getSortMarker = (colIndex: number): string => {
  if (!sortInfo || !sortOrder) return ''
  const direction = sortInfo[colIndex]
  const order = sortOrder.indexOf(colIndex) + 1
  const directionSymbol = { u: '▲', d: '▼' }[direction] || ''
  return directionSymbol && order ? `${directionSymbol}${order}` : ''
}

watch(
  () => getNestedValue(props.context, props.schema?.model || ''),
  async (newValue, oldValue) => {
    debugger
    // if (isEmpty(tableData.value)) return
    const favID = props.schema?.myFavoriteId
    let selectedLine = 0
    if (favID) {
      const favData = (await cacheManager.get(favID)) || {}
      selectedLine = favData.selectedLine
      if (selectedLine >= tableData.value.length) {
        selectedLine = tableData.value.length - 1
      }
      sortOrder = favData.sortOrder || []
      sortInfo = favData.sortInfo || {}
    }
    // await sortTable()
    await selectLine(null, selectedLine || 0)
    // scrollToSelectedLine()
    tocTableKey.value++
  }
)

async function saveFav(sortOrder: any, sortInfo: any) {
  const favID = props.schema?.myFavoriteId
  if (!favID) return
  const favData = (await cacheManager.get(favID)) || {}
  favData.sortOrder = sortOrder
  favData.sortInfo = sortInfo
  await cacheManager.set(favID, favData)
}

async function handleSort(event: MouseEvent, __: ColumnDef, colIndex: number) {
  if (!tableData.value?.length) return

  if ((event.metaKey || event.ctrlKey) && event.shiftKey) {
    sortOrder = []
    sortInfo = {}
    await sortTable()
    await saveFav(sortOrder, sortInfo)
    tocTableKey.value++
    sortEventKey.value++
    return
  }

  sortInfo[colIndex] = sortInfo[colIndex] === 'u' ? 'd' : 'u'
  sortOrder = [colIndex, ...sortOrder.filter((x) => x !== colIndex)]
  if (sortOrder.length > 5) delete sortInfo[sortOrder.pop()!]
  const selectedLine = state.selectedLine
  const indexMap = await sortTable()
  await saveFav(sortOrder, sortInfo)
  await selectLine(null, indexMap?.[selectedLine] || 0)
  scrollToSelectedLine()
}

const sortTable = async (): Promise<Record<number, number>> => {
  const data = tableData.value || []
  if (!Array.isArray(data) || data.length === 0) return {}

  data.forEach((row: any, index) => {
    row[originalIndexSymbol] = index
  })

  const keys = props.schema?.columnDef || []
  if (!Array.isArray(sortOrder) || sortOrder.length === 0) return {}

  sortOrder
    .slice()
    .reverse()
    .forEach((i) => {
      const key = keys[i]?.field
      const direction = sortInfo[i]

      if (!key || !direction) return

      data.sort((a, b) => {
        const aValue = getNestedValue(a, key)
        const bValue = getNestedValue(b, key)

        if (aValue == null && bValue == null) return 0
        if (aValue == null) return direction === 'u' ? -1 : 1
        if (bValue == null) return direction === 'u' ? 1 : -1

        const aComparable = isFinite(aValue) ? +aValue : aValue
        const bComparable = isFinite(bValue) ? +bValue : bValue

        if (aComparable < bComparable) return direction === 'u' ? -1 : 1
        if (aComparable > bComparable) return direction === 'u' ? 1 : -1
        return 0
      })
    })

  const indexMap: Record<number, number> = {}
  data.forEach((row: any, newIndex) => {
    indexMap[row[originalIndexSymbol]] = newIndex
    delete row[originalIndexSymbol]
  })

  tableData.value = data
  tocKey.value++
  sortEventKey.value++

  return indexMap
}

const commandManager = inject<CommandManager>('commandManager')

const comp = getCurrentInstance()
const eventBus = useEventBus()
const callBackWithContext = (data: any) => {
  let { cb } = data || {}
  if (!cb) return
  cb({ comp: comp?.exposed || {} })
}

const calTableScroll = function () {
  if (wToc.value) {
    const containerHeight = wToc.value.clientHeight
    const y =
      containerHeight - (props.schema?.headerHeight || 0) - (props.schema?.footerHeight || 0) - 40
    state.scroll = {
      y,
      x: props.schema?.tableAttrs.columns.length * 100
    }
    setPageSize(Math.ceil(y / 40))
  }
}

async function fetchData() {
  state.loading = true
  await tocListItem({ queryParam: state.schema || {} })
  state.loading = false
}

function addScrollListener() {
  const dom = tocTable.value?.$el.querySelector('.ant-table-body')
  if (dom) {
    dom.addEventListener('scroll', handleTableScroll)
  }
}

onMounted(async () => {
  nextTick(async () => {
    calTableScroll()
    await fetchData()
    if (sortableTable.value) {
      Sortable.create(sortableTable.value, {
        animation: 150,
        onEnd: (event: any) => {
          const parent = event.item.parentNode
          if (event.oldIndex < event.newIndex) {
            parent.insertBefore(event.item, parent.children[event.oldIndex])
          } else {
            parent.insertBefore(event.item, parent.children[event.oldIndex + 1])
          }

          const movedItem = tableData.value.splice(event.oldIndex, 1)[0]
          tableData.value.splice(event.newIndex, 0, movedItem)

          if (+state.selectedLine === +event.oldIndex) {
            selectLine(null, event.newIndex)
          }
        },
        ghostClass: '',
        chosenClass: ''
      })
    }

    tocTable.value?.$el?.addEventListener('keydown', handleKeydown)

    commandManager?.on(tocCommandSelectTopic, async (flow: CommandFlow) => {
      let line = (await flow.request('item_to_select', tableData.value)) || 0
      if (line < 0) line = 0
      if (line >= tableData.value.length) line = tableData.value.length - 1
      await selectLine(null, line)
      scrollToSelectedLine()
    })

    if (props.schema?.event?.messageEndPoint) {
      eventBus.on(props.schema?.event?.messageEndPoint, callBackWithContext)
    }
    addScrollListener()
  })
})

onUnmounted(() => {
  tocTable.value?.$el?.removeEventListener('keydown', handleKeydown)
  if (props.schema?.event?.messageEndPoint) {
    eventBus.off(props.schema?.event?.messageEndPoint, callBackWithContext)
  }
})

onBeforeUnmount(() => {
  const dom = tocTable.value?.$el.querySelector('.ant-table-body')
  if (dom) {
    dom.removeEventListener('scroll', handleTableScroll)
  }
})

const tocRefreshItems = inject(eventRefreshItems, () => 1) as Function

// watch(
//   async () => await tocRefreshItems(getContext(props), props?.schema?.model),
//   async (newValue) => {
//     tableData.value = await tocListItem({ queryParam: state?.schema || {} })
//     await selectLine(null, 0)
//   }
// )

function lineName(data: object, colDef: ColumnDef, idx: number): string {
  const { field: path, format } = colDef
  const [value = ''] = JSONPath({ path: `$.${path}`, json: data }) || []
  if (format === 'tms') {
    return ServerTime.formatTms(value)
  }
  return value
}

const tocSelectItemByEvent = inject(eventSelectItem, () => 1) as Function

const doTocSelectItemByEvent = new Debouncer(50).debounce(async (selectedLine: number) =>
  tocSelectItemByEvent(
    {
      selectedLine: selectedLine,
      data: tableData.value?.[selectedLine]
    },
    props
  )
)

const tocOpenItemByEvent = inject(eventOpenItem, () => 1) as Function

async function openItem(event: PointerEvent | null, rowIndex: number) {
  event?.stopPropagation?.()
  event?.preventDefault?.()

  if (rowIndex < 0) rowIndex = 0
  if (rowIndex >= tableData.value.length) rowIndex = tableData.value.length - 1

  await tocOpenItemByEvent(
    {
      selectedLine: rowIndex,
      data: tableData.value?.[rowIndex]
    },
    props
  )
}

function handleRowClick(index: number, event: PointerEvent) {
  selectLine(event, index, true)
}

async function handleRowDblClick(index: number, event: PointerEvent) {
  openItem(event, index)
}

async function selectLine(event: PointerEvent | null, rowIndex: number, saveAsFav = false) {
  event?.stopPropagation?.()
  event?.preventDefault?.()

  const { schema = {}, context: { locModel = {} } = {} } = props

  if (!tableData.value.length) return

  if (rowIndex < 0) rowIndex = 0
  if (rowIndex >= tableData.value.length) rowIndex = tableData.value.length - 1

  state.selectedLine = rowIndex

  const favID = schema.myFavoriteId
  if (saveAsFav && favID) {
    const favData = (await cacheManager.get(favID)) || {}
    favData.selectedLine = rowIndex
    await cacheManager.set(favID, favData)
  }

  if (schema.selectedLineModel && tableData.value[state.selectedLine]) {
    locModel[schema.selectedLineModel] = { data: tableData.value[state.selectedLine] }
  }

  await doTocSelectItemByEvent(state.selectedLine)
}

const handleKeydown = (event: KeyboardEvent) => {
  if (props.schema?.naviWithKey === 'no') return

  const delta = event.key === 'ArrowUp' ? -1 : event.key === 'ArrowDown' ? 1 : 0
  if (!delta) return

  event.preventDefault()
  event.stopPropagation()
  selectLine(null, state.selectedLine + delta, true)
  scrollToSelectedLine()
}

const nextItem = async () => {
  if (state.selectedLine + 1 >= tableData.value.length)
    return {
      message: '队列中无更多的待就诊患者。'
    }
  await selectLine(null, state.selectedLine + 1, true)
  return tableData.value?.[state.selectedLine] || {}
}

const scrollToSelectedLine = () => {
  nextTick(() => {
    try {
      if (props.schema?.naviWithKey === 'no') return

      const selectedRow = tocTable.value?.$el?.querySelector(
        `tr:nth-child(${state.selectedLine + 1})`
      )
      if (selectedRow === undefined) return //selectRow can be 0

      let parentContainer = tocTable.value?.$el
      while (parentContainer && parentContainer.tagName !== 'DIV') {
        parentContainer = parentContainer.parentElement
      }

      if (!selectedRow || !parentContainer) return

      const rowRect = selectedRow.getBoundingClientRect()
      const parentRect = parentContainer.getBoundingClientRect()

      const isVisible = rowRect.top >= parentRect.top + 20 && rowRect.bottom <= parentRect.bottom

      if (isVisible) return

      selectedRow.scrollIntoView({ behavior: 'smooth', block: 'center' })
    } catch (e) {
      console.error('Error scrolling to selected line:', e)
    }
  })
}

const newItem = (item = {} as any) => {
  if (!tableData.value) return
  tableData.value.push(item)
  state.selectedLine = tableData.value.length - 1
}

const handleTableScroll = new Debouncer(200).debounce((event) => {
  const target = event.target
  if (target.scrollTop + target.clientHeight >= target.scrollHeight - 50) {
    console.log('xxxxxxxxxx')
    scrollToBottom()
  }
})

const handleTableSort = (data: { column: any; prop: string; order: any }, event: MouseEvent) => {}

defineExpose({ compName: 'toc', tocState: state, newItem, nextItem })
</script>

<style lang="scss" scoped>
.field-w-toc {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  overflow: auto;

  .field-w-toc-body {
    overflow: auto;
    flex: 1;
  }
}

.field-w-toc-toolbar,
.toolbar {
  flex: 0 !important;
}

.col-custum-html {
  display: flex;
  align-items: center;
}

.table-detail.field-w-toc {
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
  overflow: hidden;
  padding: 1px;

  .field-w-toc-body {
    display: block;
    overflow: auto;
    position: relative;

    th {
      top: 0;
      position: sticky;
      z-index: 1;
    }
  }
}
</style>
