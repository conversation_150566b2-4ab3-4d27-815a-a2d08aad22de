// server-time.ts
import { DateTime } from 'luxon'
import ntpClient from 'ntp-client'

interface TimeParams {
  timeZone?: string
  tms?: number
  dat?: number
  dtm?: number
  format?: string
}

class ServerTime {
  private static serverOffset: number = 0 // 服务器时间偏移（秒）
  private static lastSyncTime: number = 0 // 上次校对时间（秒）
  private static ntpServer: string = 'pool.ntp.org' // NTP 服务器地址
  private static defaultTimeZone: string = 'UTC' // 默认时区

  /**
   * 配置 NTP 服务器地址
   * @param {string} server - NTP 服务器地址
   */
  static setNtpServer(server: string): void {
    this.ntpServer = server
  }

  static fromSeconds(time: number) {
    return DateTime.fromSeconds(time, { zone: ServerTime.defaultTimeZone })
  }

  /**
   * 设置默认时区
   * @param {string} timeZone - 默认时区
   */
  static setDefaultTimeZone(timeZone: string): void {
    this.defaultTimeZone = timeZone
  }

  /**
   * 初始化服务器时间偏移
   */
  static init(): void {
    // this.syncServerOffset()
    // setInterval(() => this.syncServerOffset(), 30 * 60 * 1000) // 每 30 分钟校对一次
  }

  /**
   * 返回当前时间的 dat、dtm 和 tms
   * @param {TimeParams} [params={}] - 参数对象
   * @returns {Object} - 包含 dat、dtm 和 tms 的对象
   */
  static now(params: TimeParams = {}): { dat: number; dtm: number; tms: number } {
    const { timeZone = this.defaultTimeZone } = params
    const tms = Math.floor(DateTime.now().plus({ seconds: this.serverOffset }).toSeconds()) // 考虑服务器偏移
    const dat = Math.floor(tms / 86400) // 天数时间戳
    const dtm = tms % 86400 // 当天过去的秒数
    return { dat, dtm, tms }
  }

  /**
   * 返回当前时间的 dat、dtm 和 tms
   * @param {TimeParams} [params={}] - 参数对象
   * @returns {Object} - 包含 dat、dtm 和 tms 的对象
   */
  static now2(params: TimeParams = {}): DateTime {
    const { timeZone = this.defaultTimeZone } = params
    return DateTime.now()
  }

  /**
   * 根据 dat 和 dtm 计算时间戳
   * @param {TimeParams} params - 参数对象
   * @returns {number} - 秒数时间戳
   * @throws {Error} - 如果 dat 或 dtm 无效
   */
  static fromDatDtm(params: TimeParams): number {
    const { dat = 0, dtm = 0, timeZone = this.defaultTimeZone } = params
    const timestamp = dat * 86400 + dtm
    return DateTime.fromSeconds(timestamp, { zone: 'utc' }).setZone(timeZone).toSeconds()
  }

  /**
   * 将 dat 格式化为 "YYYY-MM-DD"
   * @param {number} dat - 天数时间戳
   * @param {TimeParams} [params={}] - 参数对象
   * @returns {string} - 格式化后的日期字符串
   * @throws {Error} - 如果 dat 无效
   */
  static formatDat(dat: number, params: TimeParams = {}): string {
    const { timeZone = this.defaultTimeZone } = params
    const timestamp = dat * 86400
    return DateTime.fromSeconds(timestamp, { zone: 'utc' }).setZone(timeZone).toFormat('yyyy-MM-dd')
  }

  /**
   * 将 dtm 格式化为 "HH:mm"
   * @param {number} dtm - 一天内的偏移秒数
   * @param {TimeParams} [params={}] - 参数对象
   * @returns {string} - 格式化后的时间字符串
   * @throws {Error} - 如果 dtm 无效
   */
  static formatDtm(dtm: number, params: TimeParams = {}): string {
    const { timeZone = this.defaultTimeZone } = params
    const timestamp = dtm
    return DateTime.fromSeconds(timestamp, { zone: 'utc' }).setZone(timeZone).toFormat('HH:mm')
  }

  /**
   * 将 dat 和 dtm 格式化为 "YYYY-MM-DD HH:mm"
   * @param {number} dat - 天数时间戳
   * @param {number} dtm - 一天内的偏移秒数
   * @param {TimeParams} [params={}] - 参数对象
   * @returns {string} - 格式化后的日期时间字符串
   * @throws {Error} - 如果 dat 或 dtm 无效
   */
  static formatDatDtm(dat: number, dtm: number, params: TimeParams = {}): string {
    const { timeZone = this.defaultTimeZone } = params
    const timestamp = dat * 86400 + dtm
    return DateTime.fromSeconds(timestamp, { zone: 'utc' })
      .setZone(timeZone)
      .toFormat('yyyy-MM-dd HH:mm')
  }

  /**
   * 将 tms 格式化为 "MMdd HH:mm"
   * @param {number} tms - 秒数时间戳
   * @param {TimeParams} [params={}] - 参数对象
   * @returns {string} - 格式化后的日期时间字符串
   * @throws {Error} - 如果 tms 无效
   */
  static formatTms(tms: number, params: TimeParams = {}): string {
    const { timeZone = this.defaultTimeZone, format = 'yyyy/MM/dd HH:mm' } = params
    let rst = ''
    try {
      rst = DateTime.fromSeconds(tms, { zone: 'utc' }).setZone(timeZone).toFormat(format)
    } catch (e) {
      //
    }
    return rst
  }

  /**
   * 校对服务器时间偏移
   */
  private static async syncServerOffset(): Promise<void> {
    try {
      const ntpTime = await this.getNtpTime()
      const localTime = DateTime.now().toSeconds() // 本地时间（UTC）
      const serverOffset = ntpTime - localTime
      if (!Number.isInteger(serverOffset) || Math.abs(serverOffset) > 3600) return
      this.serverOffset = serverOffset
      this.lastSyncTime = ntpTime
      console.log('Server offset synced:', this.serverOffset)
    } catch (error) {
      console.error('Failed to sync server offset:', error)
    }
  }

  /**
   * 获取 NTP 时间
   * @returns {Promise<number>} - NTP 时间（秒）
   */
  private static async getNtpTime(): Promise<number> {
    return new Promise((resolve, reject) => {
      ntpClient.getNetworkTime(this.ntpServer, 123, (err, date) => {
        if (err) {
          reject(err)
        } else {
          resolve(DateTime.fromJSDate(date!).toSeconds())
        }
      })
    })
  }
}

// 初始化服务器时间偏移
ServerTime.init()

export default ServerTime
