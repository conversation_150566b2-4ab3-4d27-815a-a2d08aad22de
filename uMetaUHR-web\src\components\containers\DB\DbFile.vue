<template>
  <div :key="refreshKey" :class="[schema?.theme, 'field-w-dbfile']" :style="schema?.style?.main">
    <component
      :is="comp"
      :context="getContext(props)"
      :schema="childSchema"
      @blur="autoSaveFile()"
      @keydown="handleKeydown($event)"
    />
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, onUnmounted, provide, ref } from 'vue'
import { loadComponent } from '@/components/componentRegistry'
import { getContext } from '@/lib/getContext'
import { dbDelete, dbGet, dbList, dbSave } from '@/lib/dataMan'
import { loadSchema } from '@/schema/schemaRegistry'
import hotkeys from 'hotkeys-js'
import SparkMD5 from 'spark-md5'
import serverTime from '@/lib/ServerTime'
import ServerTime from '@/lib/ServerTime'

let refreshKey = ref(0)
const props = defineProps({
  schema: Object,
  context: Object,
  theme: String
})

let comp = null as any
let childSchema = ref({}) as any
let fileList = ref([]) as any

function handleKeydown(event: KeyboardEvent) {
  if ((event.metaKey || event.ctrlKey) && event.key === 's') {
    event.preventDefault()
    event.stopPropagation()
    saveDBFile()
  }
}

onMounted(async () => {
  let { default: schema = {} } = await loadSchema('01100-DbFile')

  let { ui = {}, hkeys = [] } = schema
  props.schema!.body = ui
  childSchema.value = ui
  comp = computed(() => loadComponent(ui?.type))

  for (let { key, scope = {} } of hkeys) {
    if (!key) continue
    hotkeys.unbind(key, scope)
    hotkeys(key, scope, handleKeydown)
  }
})

onUnmounted(() => {})

let lastSavedHashes = new Map<string | number, string>() // Store last saved hash for each ID
let autoSaveTimer = null as any // Store the timer reference

function generateMD5Hash(data: any): string {
  return SparkMD5.hash(JSON.stringify(data))
}

async function autoSaveFile() {
  if (autoSaveTimer !== null) clearTimeout(autoSaveTimer)

  autoSaveTimer = setTimeout(
    async () => {
      const context = getContext(props)
      const dbFile = context?.detail?.dbFile
      if (!dbFile) return

      const currentHash = generateMD5Hash(dbFile)
      const lastSaved = lastSavedHashes.get(dbFile.id)
      if (lastSaved !== currentHash) {
        if (lastSaved) await saveDBFile()
        lastSavedHashes.set(dbFile.id, currentHash)
      }
      await autoSaveFile()
    },
    10 * 60 * 1000
  ) // 60 seconds
}

let listDbFile = async () => {
  let fileType = props?.schema?.file?.type || ''
  if (!fileType) return

  let sql = `
    SELECT id, name, filetime, servicetime
    FROM "maxEMR".file_system
    WHERE type = ?::text`
  fileList.value = (await dbList({ sql, param: [fileType] })) || []
  return fileList.value
}
provide('list-db-file', listDbFile)

provide('select-db-file', async (param: any = {}) => {
  let context = getContext(props)
  let { data: { id = 0 } = {} } = param as any
  if (!id) return
  let fileType = props?.schema?.file?.type || ''
  if (!fileType) return

  let f2 =
    (await dbGet({
      sql: `SELECT *
          FROM "maxEMR".file_system
          WHERE id = ?::int
            AND type = ?::text  `,
      param: [id, fileType]
    })) || {}

  let dbFile = fileList.value.find((x: any) => x.id == id) || {}
  Object.assign(dbFile, f2)

  context.detail = { dbFile } //refresh

  await autoSaveFile()
  return {}
})

provide('new-db-file', async (param: any = {}) => {
  let context = getContext(props)
  let time = ServerTime.now()?.tms
  const dbFile = { name: 'new', filetime: time, servicetime: time, data: {}, tag: {} }
  context.detail = { dbFile }
  fileList.value.push(dbFile)
})

let saveDBFile = async () => {
  let context = getContext(props)
  let dbFile = context?.detail?.dbFile
  if (!dbFile) return null
  let fileType = props?.schema?.file?.type || ''
  if (!fileType) return

  dbFile.filetime = serverTime.now()?.tms
  dbFile.type = fileType
  let { ids: [{ id } = {} as any] = [] } = await dbSave({
    table: 'file_system',
    data: [dbFile],
    conflict_column: 'id',
    update_columns: [
      ...(dbFile.id ? ['id'] : []),
      'data',
      'type',
      'tag2',
      'name',
      'filetime',
      'servicetime'
    ]
  })
  if (id) dbFile.id = id
}

provide('save-db-file', saveDBFile)

provide('delete-db-file', async (param: any = {}) => {
  let context = getContext(props)
  let { name, id } = context?.detail?.dbFile || {}
  if (!id) return null
  if (!confirm(`删除记录 ${name} (${id})？`)) return

  let i = fileList.value.findIndex((x: any) => x.id == id)
  fileList.value.splice(i, 1)

  await dbDelete({
    sql: `delete
          from "maxEMR".file_system
          where id = ?::int`,
    param: [id]
  })
})

defineExpose({ compName: props?.schema?.type || '-' })
</script>

<style lang="scss" scoped>
.field-w-dbfile {
  display: flex;
  flex: 1;
  flex-direction: column;

  > .field-w-toc-detail {
    flex: 1;
  }
}
</style>
