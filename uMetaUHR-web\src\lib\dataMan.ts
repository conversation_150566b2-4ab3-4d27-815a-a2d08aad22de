import axios from 'axios'
import { isEmpty } from '@/lib/util'

// Function to dbGet the sessionId from local storage
function getSessionId(): string | null {
  return localStorage.getItem('sessionId')
}

// Common axios configuration with CORS and session headers
const axiosConfig = (sessionId: string | null): any => ({
  headers: {
    'Session-ID': sessionId || '' //caused error
  }
})

interface ListResponse {
  results: any[]
}

interface GetResponse {
  data: any
}

interface SaveResponse {
  data: any
}

export async function dbList(param: any): Promise<any[]> {
  const sessionId = getSessionId()
  const response = await axios.post<ListResponse>(
    'http://localhost:8080/listData',
    param,
    axiosConfig(sessionId)
  )
  const { data: { results = [] } = {} } = response
  return results
}

export async function dbGet(param: any): Promise<any> {
  const sessionId = getSessionId()
  const response = await axios.post<GetResponse>(
    'http://localhost:8080/getData',
    param,
    axiosConfig(sessionId)
  )
  const { data } = response
  return data
}

export async function dbSave(param: any): Promise<any> {
  const sessionId = getSessionId()

  const { data: dataList = [] } = param || {}
  if (isEmpty(dataList)) return

  const response = await axios.post<SaveResponse>(
    'http://localhost:8080/upsertData',
    param,
    axiosConfig(sessionId)
  )

  await (window as any).popAlert({ message: '保存完毕' })
  const { data } = response
  return data
}

export async function dbDelete(param: any): Promise<any> {
  const sessionId = getSessionId()
  const response = await axios.post<SaveResponse>(
    'http://localhost:8080/deleteData',
    param,
    axiosConfig(sessionId)
  )

  await (window as any).popAlert({ message: '删除完毕' })
  const { data } = response
  return data
}

export async function refreshNotification(param: any): Promise<any[]> {
  const sessionId = getSessionId()
  const response = await axios.post<ListResponse>(
    'http://localhost:8080/notification/load',
    param,
    axiosConfig(sessionId)
  )
  const { data: { results = [] } = {} } = response
  return results
}
