<template>
  <div class="pat-intake">
    <component
      :is="loadComponent(props.schema?.ui?.type)"
      v-if="props.schema"
      :context="getContext(props)"
      :schema="props.schema?.ui"
    />
  </div>
</template>

<script lang="ts" setup>
import { defineProps, onMounted, provide } from 'vue'
import { loadComponent } from '../componentRegistry'
import { getContext } from '@/lib/getContext'
import { dbGet, dbList, dbSave } from '@/lib/dataMan'
import { isEmpty, removeEmptyNodes } from '@/lib/util'
import { getAgent, useAgents, useAI } from '@/lib/ai'
import { deepMerge } from '@/lib/copymerge'
import { extractJsonFromText } from '@/lib/textFormater'

const props = defineProps<{
  schema: {
    type: string
    children: Array<any>
  }
  context: { [key: string]: any }
}>()

provide('toc-list-data', async (param: any) =>
  dbList({
    table: 'pat_intake',
    columns: ['id', 'name']
  })
)

provide('toc-get-data', async (param: any) => {
  let { id } = param || {}
  if (!id) return {}

  return dbGet({
    table: 'pat_intake',
    id,
    columns: ['id', 'data', 'name']
  })
})

provide('toc-save-data', async (param: any) => {
  let { detail: data } = getContext(props)
  await dbSave({
    table: 'pat_intake',
    data: [data],
    conflict_column: 'id',
    update_columns: ['id', 'data', 'name']
  })
})

onMounted(async () => {
  await useAgents(['预约', '数据处理'])
})

const processAgentConversation = async (props: any, agentType: string) => {
  let {
    detail: { data: { chat = {}, chat: { conversationChatBox = [] } = {} } = {} }
  } = getContext(props)
  let { data: { role: { role_spec = '' } = {}, config = {} } = {} } = await getAgent(agentType)
  let data = removeEmptyNodes(chat.baseData) || {}
  let history = chat.baseDataHistory || []
  history = history.map(removeEmptyNodes)

  await window.popAlert({ message: '开始分析 ... ' })

  for (let conversation of conversationChatBox) {
    let { role, content } = conversation || {}
    if (role !== 'patient') continue

    let { content: cnt } = (await useAI(
      [
        { role: 'system', content: role_spec },
        { role: 'user', content }
      ],
      config
    )) as any

    let d = removeEmptyNodes(extractJsonFromText(cnt))
    history.push(d)
    data = deepMerge(data, d)
    await window.popAlert({ message: JSON.stringify(data) })
  }
  await window.popAlert({ message: '结束' })

  chat.baseData = data
  chat.baseDataHistory = history
}

provide('agent-appointment', async (context: any) => {
  await processAgentConversation(props, '预约')
})

provide('agent-intake', async (context: any) => {
  await processAgentConversation(props, '数据处理')
})

provide('agent-collect-idea', async (context: any) => {
  let {
    detail: { data: { chat = {}, chat: { conversationChatBox = [] } = {} } = {} }
  } = getContext(props)

  let {
    baseData: {
      appointment_details: {
        is_willing_to_appoint = false,
        available_time_slots = [],
        desired_departments = []
      } = {}
    } = {}
  } = (chat || {}) as any

  if (!is_willing_to_appoint) {
    await window.popAlert({ message: '无需预约' })
    return
  }

  const qs = [
    !isEmpty(available_time_slots) && '请问什么时间方便？',
    !isEmpty(desired_departments) && '希望预约哪个科室？'
  ].filter(Boolean)

  if (isEmpty(qs)) {
    await window.popAlert({ message: '数据已经齐全' })
    return
  }
  conversationChatBox.push({
    role: 'system',
    content: `向患者问下列问题：
    ${qs.join('\n')}`
  })

  await window.popAlert({ message: '完成' })
})

provide('clear', async () => {
  let { context: { chat = {} } = {} } = props
  for (let key of Object.keys(chat)) {
    delete chat[key]
  }
  chat.conversationChatBox = []
})

provide('reply', async () => {
  let { data: { config = {}, role: { player_role = '', role_spec = '' } = {} } = {} } =
    await getAgent('医生')
  let {
    detail: { data: { chat = {}, chat: { conversationChatBox = [] } = {} } = {} }
  } = getContext(props)

  let msgs = chat.conversationChatBox
  if (!msgs) {
    msgs = chat.conversationChatBox = []
  }

  msgs.push({
    role: 'patient', // as a patient, ask doctor for suggestions
    content: chat.reply
  })

  await window.popAlert({ message: '询问医生 ... ' })
  try {
    const { content: cnt } = (await useAI(
      [{ role: 'system', content: role_spec }, ...msgs],
      config,
      'patient'
    )) as any
    chat.conversationChatBox.push({
      role: 'doctor', // doctor replies.
      content: cnt
    })
  } catch (error) {
    await window.popAlert({ message: `结果：${JSON.stringify(error)}` })
  }
})

provide('suggest-reply', async () => {
  let { data: { config = {}, role: { role_spec = '' } = {} } = {} } = await getAgent('患者')
  let {
    detail: { data: { chat = {}, chat: { conversationChatBox = [] } = {} } = {} }
  } = getContext(props)

  let msgs = chat.conversationChatBox || []

  await window.popAlert({ message: '建议患者问题 ... ' })
  const { content: respContent } = (await useAI(
    [{ role: 'system', content: role_spec }, ...msgs],
    config,
    'patient'
  )) as any

  if (!respContent) {
    await window.popAlert({ message: '无结果 ... ' })
    return
  }

  chat.reply = respContent
})

defineExpose({ compName: props?.schema?.type || '-' })
</script>

<style lang="scss" scoped>
.pat-intake {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  overflow: auto;
}
</style>
